import {
  Component,
  Input,
  ViewChild,
  ElementRef,
  ViewEncapsulation,
  Inject,
  LOCALE_ID,
  inject,

} from '@angular/core';
import { CommonModule } from '@angular/common';
import { OverlayPanelModule } from 'primeng/overlaypanel';

import { CommentHistoryItem, CommentInput, CommentService, FavoriteTypeEnum, SemanticChatInput } from '../../services';

import { FormsModule } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';
import { MarkdownPipe } from '../../markdown.pipe';
import { MessageService } from 'primeng/api';
import { SummarySubTab } from '../../shared/enums/case-tab.enum';
import { ICaseSummaryUserUpdateResult } from '../search/semantic-search/search-result-list/_models/user-updates.model';
import { DialogModule } from 'primeng/dialog';


@Component({
  selector: 'comments-shared',
  standalone: true,
  templateUrl: './comments-shared.component.html',
  encapsulation: ViewEncapsulation.None, // Ensures styles are not encapsulated
  imports: [
    CommonModule,
    FormsModule,
    OverlayPanelModule,
    InputTextModule,
    ButtonModule,
    MarkdownPipe,
    DialogModule
  ]
})
export class CommentSharedComponent {
  @Input() typeId!: FavoriteTypeEnum;
  @Input() refId!: string;
  @Input() text!: string;
  @Input() caseId!: number;

  displayUserUpdatesDialog: boolean = false;
  protected readonly _messageService = inject(MessageService);
  noteText: string = '';
  displayNoteDialog: boolean = false;
  isEditingNote: boolean = false;
  editingNoteId: number | null = null;
  currentNoteType: SummarySubTab | null = null;
  private currentCaseSummaryId: number | null = null;
  userUpdates: CommentHistoryItem[] = [];
  pinnedUpdates: ICaseSummaryUserUpdateResult[] = [];
  otherUpdates: ICaseSummaryUserUpdateResult[] = [];
  displayDeleteConfirm: boolean = false;
  displayDeleteChatConfirm: boolean = false;
  confirmingEntry: any = null;
  constructor(
    private commentService: CommentService,
    @Inject(LOCALE_ID) private locale: string
  ) {


  }

  ngOnInit() {

  }

  openAddNoteDialog() {

    this.noteText = '';
    this.displayNoteDialog = true;
    this.isEditingNote = false;
    this.editingNoteId = null;

    if (!this.currentCaseSummaryId) {
      return;
    }
  }

  saveComment() {
    console.log('Saving note type:', this.currentNoteType, 'Note text:', this.noteText);
    if (!this.noteText.trim()) {
      console.error('Note text is required');
      return;
    }

    var payload: CommentInput = {
      // caseId: this.selectedCase?.caseId!.toString(),
      caseId: this.caseId != null ? this.caseId.toString() : this.refId,
      comment: this.noteText,
      // content: this.selectedCase?.cleanTxt,
      content: this.text,
      refId: this.refId,
      typeId: this.typeId
    };

    this.commentService.storeComment(payload).subscribe({
      next: () => {
        console.log('Note added successfully');
        this.displayNoteDialog = false;
        this.displayUserUpdatesDialog = false;
        this._messageService.add({
          severity: 'success',
          summary: 'نجحت العملية',
          detail: 'تم أضافة ملاحظة بنجاح'
        });
      },
      error: (err) => {
        console.error('Error saving note:', err);
        this._messageService.add({
          severity: 'error',
          summary: 'فشل الإضافة',
          detail: err?.error?.error?.message || 'حدث خطأ أثناء حفظ الملاحظة'
        });
      }
    });
  }

  deleteComment(selectedResult: any) {
    const input: CommentInput = {
      caseId: selectedResult.CaseId,
      refId: selectedResult.RefId,
      typeId: FavoriteTypeEnum.Case,
      comment: selectedResult.comment
    };
    this.commentService.deleteComment(input).subscribe({
      next: () => {
        // this.loading = false;
        // this.fetchAll();
        this.getComments();
      },
      error: (err) => {
        // this.loading = false;
        console.error('Error retrieving case details:', err);
      },
    });
  }

  openUpdateNoteDialog(noteId: number, noteText: string) {
    this.isEditingNote = true;
    this.editingNoteId = noteId;
    this.noteText = noteText;
    this.displayNoteDialog = true;
    this.displayUserUpdatesDialog = false;
  }

  openUserUpdatesDialog() {
    this.userUpdates = [];
    this.pinnedUpdates = [];
    this.otherUpdates = [];
    let summaryId: number | null = null;

    const refId = this.refId;
    if (!refId) {
      console.error('caseId is undefined');
      return;
    }
    this.getComments();
    // this.commentService.getComments(refId.toString()).subscribe({
    //   next: (response) => {
    //     const userUpdatesArray = response.data || [];
    //     this.userUpdates = userUpdatesArray.commentHistory ?? [];
    //     this.displayUserUpdatesDialog = true;
    //   },
    //   error: (err) => console.error('Error fetching user updates:', err)
    // });
  }

  getComments() {
    const refId = this.refId;
    this.commentService.getComments(refId.toString()).subscribe({
      next: (response) => {
        const userUpdatesArray = response.data || [];
        this.userUpdates = userUpdatesArray.commentHistory ?? [];
        this.displayUserUpdatesDialog = true;
      },
      error: (err) => console.error('Error fetching user updates:', err)
    });
  }

  sortUserUpdates(order: 'ASC' | 'DESC') {
    this.otherUpdates = this.sortArray(this.otherUpdates, order);
    console.log('Sorted Other Updates:', this.otherUpdates);
  }

  private sortArray(array: any[], order: 'ASC' | 'DESC'): any[] {
    array.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
    return order === 'DESC' ? array.reverse() : array;
  }

}
