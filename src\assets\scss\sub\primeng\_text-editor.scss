
// ---------- Text Editor
// .p-editor-content{
//     height: 150px;
// }
// [dir=rtl] { 
//     .ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg{ 
//         right: auto;
//         left: 0; 
//     }
//     .ql-snow .ql-picker-label{
//         padding-left: 2px;
//         padding-right: 8px;
//     }
//     .ql-editor{ 
//         text-align: right; 
//     }
// }
// .ql-clipboard{
//     display: none;
// }