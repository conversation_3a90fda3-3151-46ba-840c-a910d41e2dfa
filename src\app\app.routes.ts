import { RouterModule, Routes } from '@angular/router';
import { LandingPageComponent } from './pages/landing-page/landing-page.component';
import { NgModule } from '@angular/core';
import { AuthorizationGuard, canActivateAuthGuard } from './auth/public-api';
import { UserManagementModule } from "./pages/user-management/user-management.module";
import { PoliciesComponent } from "./pages/policies/policies.component";
import { Role } from "./shared/enums/role.enum";
import { DeactivatedAccountComponent } from "./pages/deactivated-account/deactivated-account.component";
import { IsActiveGuard } from "./auth/guards/is-active.guard";
import { IsAuthenticatedGuard } from "./auth/guards/is-authenticated.guard";
import { BypassLoginComponent } from "./pages/bypass-login/bypass-login.component";
import { BypassLoginGuard } from "./auth/guards/bypass-login.guard";
import {
  SemanticSearchLandingFileComponent
} from "./pages/search/semantic-search/semantic-search-landing/semantic-search-landing-file/semantic-search-landing-file.component";
import {
  SemanticSearchLandingTextComponent
} from "./pages/search/semantic-search/semantic-search-landing/semantic-search-landing-text/semantic-search-landing-text.component";
import { CaseSummaryComponent } from "./pages/case-summary/case-summary.component";
import { StatisticsComponent } from "./pages/statistics/statistics.component";
import { ClassificationServiceComponent } from "./pages/classification-service/classification-service.component";
import { FavoriteComponent } from './pages/my-settings/favorite/favorite.component';
import { MyActivitiesComponent } from './pages/my-settings/my-activities/my-activities.component';

export const routes: Routes = [
  {
    path: "",
    component: LandingPageComponent
  },
  {
    path: 'policies',
    component: PoliciesComponent
  },
  {
    path: 'bypass-login',
    component: BypassLoginComponent,
    canActivate: [BypassLoginGuard]
  },
  {
    path: '',
    loadComponent: () => import('./layout/app-layout/app-layout.component').then(p => p.AppLayoutComponent),
    canActivateChild: [canActivateAuthGuard],
    children: [
      // {
      //     path: "personal-data",
      //     loadComponent: () => import('./pages/personal-data/personal-data.component').then(p => p.PersonalDataComponent)
      // },
      {
        path: "dashboard",
        loadComponent: () => import('./pages/dashboard/dashboard.component').then(p => p.DashboardComponent),
        canActivate: [IsActiveGuard, IsAuthenticatedGuard]
      },
      {
        path: "my-settings",
        loadChildren: () => import('./pages/my-settings/my-settings.module').then(p => p.MySettingsModule),
        canActivate: [IsActiveGuard, IsAuthenticatedGuard]
      },
      {
        path: 'favorites',
        component: FavoriteComponent
      },

      {
        path: 'my-activities',
        component: MyActivitiesComponent
      },

      {
        path: "users-case-viewer/:caseId",
        loadComponent: () => import('./pages/my-settings/users-case-viewer/users-case-viewer.component').then(p => p.UsersCaseViewerComponent),
        canActivate: [IsActiveGuard, IsAuthenticatedGuard]
      },

      {
        path: "users-law-viewer/:lawId",
        loadComponent: () => import('./pages/my-settings/users-law-viewer/users-law-viewer.component').then(p => p.UsersLawViewerComponent),
        canActivate: [IsActiveGuard, IsAuthenticatedGuard]
      },

      {
        path: 'classification-service',
        loadComponent: () => import('./pages/classification-service/classification-service.component').then(p => p.ClassificationServiceComponent),
        canActivate: [IsActiveGuard, IsAuthenticatedGuard]
      },
      {
        path: 'statistics',
        loadComponent: () => import('./pages/statistics/statistics.component').then(p => p.StatisticsComponent),
        canActivate: [IsActiveGuard, IsAuthenticatedGuard]
      },
      {
        path: 'case-summary',
        loadComponent: () => import('./pages/case-summary/case-summary.component').then(p => p.CaseSummaryComponent),
        canActivate: [IsActiveGuard, IsAuthenticatedGuard]
      },
      {
        path: "extract",
        loadChildren: () => import('./pages/extract/extract.module').then(p => p.ExtractModuleModule),
        canActivate: [IsActiveGuard, IsAuthenticatedGuard]
      },
      {
        path: "search",
        loadChildren: () => import('./pages/search/search.module').then(p => p.SearchModule),
        canActivate: [IsActiveGuard, IsAuthenticatedGuard]
      },
      {
        path: "user-managements",
        loadChildren: () => import('./pages/user-management/user-management.module').then(p => p.UserManagementModule),
        canActivate: [AuthorizationGuard, IsActiveGuard, IsAuthenticatedGuard],
        data: {
          roles: [Role.ADMIN, Role.SuperAdmin, Role.ElmSupportAdmin]
        },
      }
    ],
  },
  {
    path: "404",
    loadComponent: () => import('./pages/not-found/not-found.component').then(p => p.NotFoundComponent)
  },
  {
    path: "login-callback",
    loadComponent: () => import('./auth/login-callback/login-callback.component').then(p => p.LoginCallbackComponent)
  },
  {
    path: "unauthorized",
    loadComponent: () => import('./pages/unauthorized/unauthorized.component').then(p => p.UnauthorizedComponent)

  },
  {
    path: 'deactivated-account',
    component: DeactivatedAccountComponent,
  },
  {
    path: "**",
    redirectTo: '404',
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule {
}
