import { Component } from '@angular/core';
import { MySettingsBaseComponent } from './my-settings-base.component';

@Component({
  selector: 'app-my-settings',
  templateUrl: './my-settings.component.html',
  styleUrls: ['./my-settings.component.scss']
})
export class MySettingsComponent extends MySettingsBaseComponent {

  protected selectedSection: null | 'favorite'
    | 'comments'
    | 'account_setting'
    | 'personal_information'
    | 'activities';


  /**
   *
   */
  constructor() {
    super('')
    this.selectedSection = 'personal_information';

  }

}
