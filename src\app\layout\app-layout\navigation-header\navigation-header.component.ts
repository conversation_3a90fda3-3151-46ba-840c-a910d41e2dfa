import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewEncapsulation, inject } from '@angular/core';
import { AngularSvgIconModule } from 'angular-svg-icon';
import { Router, RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { AuthService } from '../../../auth/auth.service';
import { UserProfile } from '../../../auth/public-api';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import {AppConfigService} from "../../../shared/app-config.service";

@Component({
  selector: 'app-navigation-header',
  standalone: true,
  imports: [CommonModule, RouterModule, AngularSvgIconModule, TranslateModule, OverlayPanelModule],
  templateUrl: './navigation-header.component.html',
  styles: `
  .list-group{
    min-width: 10rem;
  }
  .list-group-item:hover {
    color:#365a4e
  }
  `
})
export class NavigationHeaderComponent implements OnInit {

  protected readonly authService = inject(AuthService);
  protected readonly appConfigService = inject(AppConfigService);
  protected readonly router = inject(Router);
  protected userInformation: UserProfile | null = null;
  constructor() { }

  ngOnInit() {
    this.authService.userProfileObservable.subscribe(user => {
      this.userInformation = user;
    });
  }

  logout() {
    if (this.appConfigService.appConfig.useByPass) {
      localStorage.clear();
      this.router.navigate(['/']);
      return;
    }

    this.authService.logout();
  }

}
