import { NgModule, ModuleWithProviders, SkipSelf, Optional } from '@angular/core';
import { ExternalConfiguration } from './configuration';
import { HttpClient } from '@angular/common/http';


@NgModule({
  imports:      [],
  declarations: [],
  exports:      [],
  providers: []
})
export class ExternalApiModule {
    public static forRoot(configurationFactory: () => ExternalConfiguration): ModuleWithProviders<ExternalApiModule> {
        return {
            ngModule: ExternalApiModule,
            providers: [ { provide: ExternalConfiguration, useFactory: configurationFactory } ]
        };
    }

    constructor( @Optional() @SkipSelf() parentModule: ExternalApiModule,
                 @Optional() http: HttpClient) {
        if (parentModule) {
            throw new Error('ExternalApiModule is already loaded. Import in your base AppModule only.');
        }
        if (!http) {
            throw new Error('You need to import the HttpClientModule in your AppModule! \n' +
            'See also https://github.com/angular/angular/issues/20575');
        }
    }
}
