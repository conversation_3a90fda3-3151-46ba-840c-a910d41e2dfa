import { Component, Inject, inject } from '@angular/core';
import { Router, RouterOutlet } from '@angular/router';
import { AppConfigService } from './shared/app-config.service';
import { CommonModule } from '@angular/common';
import { TranslateService } from '@ngx-translate/core';
import { setHtmlDir } from './layout/lang-switch/lang-switch.component';
//import { AuthenticationModule } from './shared/components/authentication/authentication.module';
import { BlockUI, BlockUIModule, NgBlockUI } from 'ng-block-ui';
//import { AuthenticationService } from './shared/components/authentication/authentication.service';
import { AuthModule } from './auth/public-api';
//import { AuthCheckerComponent } from './auth/auth-checker/auth-checker.component';
import { ToastModule } from 'primeng/toast';
import { AuthService } from './auth/auth.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [CommonModule, RouterOutlet, BlockUIModule, AuthModule, ToastModule],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent {
  private readonly _router = inject(Router);
  private readonly translate = inject(TranslateService);

  /**
   *
   */
  constructor(private authService: AuthService) {
    //_config.loadAppConfig();
    this.authService.initializeAuthState();
    this.translate.setDefaultLang('ar');
    this.translate.use('ar');
    setHtmlDir();
  }
  ngOnInit() {
    //Called after the constructor, initializing input properties, and the first call to ngOnChanges.
    //this.authServices.startWatching();
    //Add 'implements OnInit' to the class.
    this._router.events.forEach(eve => {
      //console.log(eve);
    });


  }
}
