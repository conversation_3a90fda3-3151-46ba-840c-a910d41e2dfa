import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { UserManagementComponent } from "./user-management.component";
import { RouterModule } from "@angular/router";
import { TableModule } from "primeng/table";
import { ButtonModule } from "primeng/button";
import { MenuModule } from "primeng/menu";
import { DialogModule } from "primeng/dialog";
import { CreateUserComponent } from './create-user/create-user.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DropdownModule } from "primeng/dropdown";
import { CheckboxModule } from "primeng/checkbox";
import { ConfirmDialogModule } from "primeng/confirmdialog";
import { ViewUserDetailsComponent } from './view-user-details/view-user-details.component';
import { UpdateUserComponent } from './update-user/update-user.component';
import { FormControlValidationsComponent } from '../../shared/public.api';
import { TranslateModule } from '@ngx-translate/core';
import { CreateUserWithProfileComponent } from './create-user-with-profile/create-user-with-profile.component';



@NgModule({
  declarations: [
    UserManagementComponent,
    CreateUserComponent,
    ViewUserDetailsComponent,
    UpdateUserComponent,
    CreateUserWithProfileComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TableModule,
    ButtonModule,
    FormControlValidationsComponent,
    TranslateModule,
    RouterModule.forChild([
      {
        path: "",
        component: UserManagementComponent
      },
      {
        path: "create-with-user-profile",
        component: CreateUserWithProfileComponent
      }
    ]),
    MenuModule,
    DialogModule,
    DropdownModule,
    FormsModule,
    CheckboxModule,
    ConfirmDialogModule 
  ]
})
export class UserManagementModule { }
