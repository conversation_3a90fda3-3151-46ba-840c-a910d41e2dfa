/**
 * MOSAED.Internal.Api
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { User } from './user';
import { UserProfile } from './user-profile';
import { UsersWhitelist } from './users-whitelist';
import { PersonFullName } from './person-full-name';


export interface SsoUser { 
    id: number;
    createdAt: string;
    createdById: number;
    createdByUser: UserProfile;
    updatedAt?: string | null;
    updatedBy?: number | null;
    updatedByUser: UserProfile;
    userId: number;
    user: User;
    isExternal?: boolean | null;
    userCode?: string | null;
    firstName?: string | null;
    secondName?: string | null;
    thirdName?: string | null;
    familyName?: string | null;
    birthDate?: string | null;
    genderTypeId?: number | null;
    genderTypeDesc?: string | null;
    email?: string | null;
    networkUser?: string | null;
    phone?: string | null;
    registrationMethodId?: number | null;
    sub?: string | null;
    identityTypeId?: number | null;
    usersWhitelistId: string;
    usersWhitelist: UsersWhitelist;
    readonly fullName?: string | null;
    personFullName: PersonFullName;
}

