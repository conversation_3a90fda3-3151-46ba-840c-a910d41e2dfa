/**
 * MOSAED.Internal.Api
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface Keywords { 
    proceduralLegalKeywords?: Array<string> | null;
    rightsAndObligationsKeywords?: Array<string> | null;
    legalPrinciplesAndDoctrinesKeywords?: Array<string> | null;
    contractAndAgreementKeywords?: Array<string> | null;
    propertyAndOwnershipKeywords?: Array<string> | null;
    administrativeAndRegulatoryKeywords?: Array<string> | null;
    remediesAndReliefKeywords?: Array<string> | null;
    caseClassificationKeywords?: Array<string> | null;
}

