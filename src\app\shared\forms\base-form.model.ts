import { AbstractControl, FormGroup } from "@angular/forms";
import * as helper from "../Helpers/scroll-to-msg-error";
import { DevLog } from "../log-functions";

export class BaseForm {
  private readonly _form: FormGroup;

  constructor() {
    this._form = new FormGroup({});
  }

  public get form(): FormGroup {
    return this._form;
  }

  public get isInvalid() {
    return !this.isValid;
  }

  public get value() {
    return this._form.value;
  }

  public get isValid() {
    if (!this.form.valid) {
      this.validateFormGroup(this.form);
      helper.scrollToElementWithErrorMessage();
      return false;
    }

    this.removeEmptyControls(this.form);
    return true;
  }

  public validateForm() {
    this.validateFormGroup(this.form);
  }

  private removeEmptyControls(form: FormGroup) {
    Object.keys(form.controls).forEach((controlName) => {
      let _formControl = form.controls[controlName];
      if (_formControl.value === '' || _formControl.value === "") {
        _formControl.reset();
      }
    });
  }

  private validateFormGroup(form: FormGroup) {
    Object.keys(form.controls).forEach((controlName: string) => {
      let _formControl = form.get(controlName);
      if (!_formControl) return;

      if (_formControl instanceof FormGroup) {
        this.validateFormGroup(_formControl as FormGroup);
      } else {
        this.validateFormControl(_formControl, controlName)
      }
    });
  }

  private validateFormControl(formControl: AbstractControl, formControlName: string) {
    this.removeWhitespace(formControlName, formControl,);
    formControl?.markAsDirty();
    formControl?.markAsTouched();
    if (formControl?.invalid) {
      DevLog.Log({ name: formControlName, errors: formControl.errors });
    }
  }

  private removeWhitespace(formControlName: string, formControl: AbstractControl) {
    let isWhitespace = /^\s*$/.test(formControl?.value);
    if (isWhitespace) {
      DevLog.Log({ error: `remove Whitespace of ${formControlName}:`, value: formControl?.value });
      formControl.setValue(null);
    }
  }

}
