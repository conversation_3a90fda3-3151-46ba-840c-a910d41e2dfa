/**
 * MOSAED.Internal.Api
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { FavoriteTypeEnum } from './favorite-type-enum';
import { HighlightSectionEnum } from './highlight-section-enum';


export interface HighlightInput { 
    refId?: string | null;
    caseId?: string | null;
    sectionId: HighlightSectionEnum;
    startOffset: number;
    endOffset: number;
    color?: string | null;
    selectedText?: string | null;
    typeId: FavoriteTypeEnum;
}
export namespace HighlightInput {
}


