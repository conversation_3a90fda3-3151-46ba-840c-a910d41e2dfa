<lib-form-field-error-message [form]="form" [formControlKey]="formCtrl.key" [formValidatorErrorsIn]="['required']"
    message="{{ getKey('required') | translate:{$: label} }}">
</lib-form-field-error-message>

<lib-form-field-error-message [form]="form" [formControlKey]="formCtrl.key" [formValidatorErrorsIn]="['pattern']"
    [formValidatorErrorsOut]="['required']" [message]="patternMsg |translate:{'$':label}">
</lib-form-field-error-message>

<lib-form-field-error-message [form]="form" [formControlKey]="formCtrl.key" [formValidatorErrorsIn]="['min']"
    [formValidatorErrorsOut]="['required']" message="{{ getMinRangeErrorMsg() }}">
</lib-form-field-error-message>

<lib-form-field-error-message [form]="form" [formControlKey]="formCtrl.key" [formValidatorErrorsIn]="['max']"
    [formValidatorErrorsOut]="['pattern']" message="{{ getKey('maxRang') | translate:{$:formCtrl.max, field: label} }}">
</lib-form-field-error-message>

<lib-form-field-error-message [form]="form" [formControlKey]="formCtrl.key" [formValidatorErrorsIn]="['maxlength']"
    [formValidatorErrorsOut]="['pattern']"
    message="{{ getKey('textMaxLength') | translate:{$:formCtrl.maxRequiredLength, field: label} }}">
</lib-form-field-error-message>

<lib-form-field-error-message [form]="form" [formControlKey]="formCtrl.key" [formValidatorErrorsIn]="['minlength']"
    [formValidatorErrorsOut]="['pattern', 'required']" message="{{ getMinLengthErrorMsg() }}">
</lib-form-field-error-message>

<lib-form-field-error-message [form]="form" [formControlKey]="formCtrl.key" [formValidatorErrorsIn]="['ngbDate']"
    [formValidatorErrorsOut]="['required']" message="{{ getDateErrorMsg() }}">
</lib-form-field-error-message>

<!-- <div class="invalid-feedback d-block" *ngIf="form|isValidField:formCtrl.key:['ngbDate']:['minDate']">
    {{ getKey('dateMinErrorMsg') | translate:{$:comparingLabel, field: label} }}
</div> -->