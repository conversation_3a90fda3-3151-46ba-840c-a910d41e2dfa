import { Injectable } from '@angular/core';
import { AbstractSecurityStorage } from 'angular-auth-oidc-client';

@Injectable()
export class BrowserStorageService implements AbstractSecurityStorage {
  read(key: string): any {
    return localStorage.getItem(key);
  }

  write(key: string, value: any): void {
    localStorage.setItem(key, value);
  }

  remove(key: string): void {
    localStorage.removeItem(key);
  }

  getAllKeys(): string[] {
    return Object.keys(localStorage);
  }

  clear(): void {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('id_token');
  }
}
