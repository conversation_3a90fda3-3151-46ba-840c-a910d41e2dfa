import { BehaviorSubject, Subject } from "rxjs";

export class SystemEvent<TEventMessage> {
    private event: string;
    private eventSubject: Subject<TEventMessage | null>;

    public getListener = () => this.eventSubject.asObservable();
    public pushEvent(eventMessage: TEventMessage) {
        console.log(`new event message pushed for ${this.event}`);
        this.eventSubject.next(eventMessage);
    }

    constructor(event: string) {
        this.event = event;
        this.eventSubject = new Subject<TEventMessage | null>();
    }
}

export class SystemContinuesEvent<TEventMessage> {
    private event: string;
    private eventSubject: BehaviorSubject<TEventMessage | null>;

    public getListener = () => this.eventSubject.asObservable();
    public pushEvent(eventMessage: TEventMessage) {
        console.log(`new event message pushed for ${this.event}`);
        this.eventSubject.next(eventMessage);
    }

    constructor(event: string) {
        this.event = event;
        this.eventSubject = new BehaviorSubject<TEventMessage | null>(null);
    }
}
