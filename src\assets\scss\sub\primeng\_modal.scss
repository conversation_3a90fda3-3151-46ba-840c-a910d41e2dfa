// ---------- Modal
.p-dialog .p-dialog-header .p-dialog-title {
  font-weight: 700;
  font-size: 1.1rem;
  @extend .bold;
}
body .p-dialog .p-dialog-header {
  background-color: #f0f0f6;
}
body .p-dialog-mask.p-component-overlay {
  background-color: rgba(0, 0, 0, 0.674);
  @extend .blurred;
}
body .reg-popup {
  .p-dialog-header {
    background-color: #fff;
    padding: 0;
  }
  .p-dialog-footer {
    @extend .justify-content-center;
    @extend .d-flex;
    padding: 0 0 2rem;
  }
  .p-dialog {
    border-radius: 20px;
    overflow: hidden;
  }
}
body .otp-popup {
  .p-dialog-header {
    background-color: #fff;
    padding: 0;
  }
  .p-dialog {
    border-radius: 20px;
    overflow: hidden;
  }
  .p-dialog-content {
    padding: 0 1rem 1rem 1rem;
  }
}

.normal-popup {
  .p-dialog .p-dialog-content {
    padding: 1rem;
    overflow-y: visible;
  }
  .p-dialog .p-dialog-footer {
    padding: 1rem;
  }
  .p-dialog .p-dialog-header {
    padding: 1rem;
    color: $secondary;
    background: #fafafa;
    position: relative;
    border-bottom: 2px solid #dddddd;
  }
}
