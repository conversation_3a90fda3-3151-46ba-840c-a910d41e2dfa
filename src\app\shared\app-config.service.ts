import { HttpClient } from '@angular/common/http';
import { Injectable, Provider } from '@angular/core';
import {BehaviorSubject, firstValueFrom, Subject} from 'rxjs';
import {BackendConfigResponse, IAppConfig} from './models/app-config.interface';

@Injectable({
  providedIn: 'root'
})
export class AppConfigService {
  private configLoaded = new BehaviorSubject<void>(undefined);
  private _appConfig!: IAppConfig;

  private getConfigPath() {
    const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
    return isLocalhost ? '/assets/config.local.json' : '/assets/config.json'
  }

  // private appConfig!: IAppConfig;

  public get isLoaded(): boolean {
    return this.appConfig != undefined;
  }
  // TODO use backend service (new endpoint)
  private readonly source = this.http.get<IAppConfig>(this.getConfigPath());

  constructor(private http: HttpClient) {
    console.log("Start Loading Config File...")

    // firstValueFrom(this.source).then(data => {
    //   this.appConfig = data;
    // });
  }

  private checkConfig(): void {
    if (!this.appConfig) {
      throw Error('Config file not loaded!');
    }
  }

  public get appConfig(): IAppConfig {
    if (!this._appConfig) {
      throw new Error("Configuration not loaded");
    }
    return this._appConfig;
  }

  public async loadAppConfig(): Promise<void> {
    try {
      const localConfigPath = this.getConfigPath();
      console.log(`Loading local config from ${localConfigPath}`);
      const localConfig = await firstValueFrom(
        this.http.get<{ apiBaseUrl: string }>(localConfigPath)
      );

      this._appConfig = { apiBaseUrl: localConfig.apiBaseUrl } as IAppConfig;
      console.log(`Loaded local config: apiBaseUrl = ${this._appConfig.apiBaseUrl}`);

      const backendConfigUrl = `${this._appConfig.apiBaseUrl}/api/configuration/ui`;
      console.log(`Loading backend config from ${backendConfigUrl}`);
      const backendConfigResponse: BackendConfigResponse = await firstValueFrom(
        this.http.get<BackendConfigResponse>(backendConfigUrl)
      );

      const backendConfig = backendConfigResponse.data;

      this._appConfig = { ...this._appConfig, ...backendConfig };
      console.log('Loaded backend config:', backendConfig);

      this.configLoaded.next();
    } catch (error) {
      console.error('Error loading configuration:', error);
      throw error;
    }
  }

  onConfigLoaded() {
    return this.configLoaded.asObservable();
  }

  get apiBaseUrl() {
    this.checkConfig();
    return this.appConfig.apiBaseUrl;
  }

  get oidc() {
    this.checkConfig();
    return this.appConfig.oidc;
  }

  static Factory(http: HttpClient) {
    var ref = new AppConfigService(http);
    return ref;
  }

  public static get providers(): Provider {
    return { provide: AppConfigService, deps: [HttpClient], useFactory: (httpClient: HttpClient) => AppConfigService.Factory(httpClient) }
  }
}

export function apiBaseUrlServiceFactory(myAsyncService: AppConfigService): () => string {
  return () => myAsyncService.apiBaseUrl;
}
