import { Component, OnInit } from '@angular/core';
import { RegularSearchBaseResult } from '../regular-search-base-result';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-regular-search-regulations-result',
  templateUrl: './regular-search-regulations-result.component.html',
  styleUrls: ['./regular-search-regulations-result.component.css']
})
export class RegularSearchRegulationsResultComponent extends RegularSearchBaseResult<Array<IRegularSearchRegulationsResult>> implements OnInit {



  protected override getDataObservable(): Observable<IRegularSearchRegulationsResult[]> {
    return this._httpClient.get<IRegularSearchRegulationsResult[]>(this._basePath + "/document/regulations/" + this.name)
  }


}

interface IRegularSearchRegulationsResult {
  name: string;
  description: string;
  url: string;
  issueDate: string;
  issueTool: string;
  issueNumber: string;
  state: string;
  lastUpdated: string;
}
