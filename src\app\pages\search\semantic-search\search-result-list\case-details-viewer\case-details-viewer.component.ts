import { Component, Inject, inject, Input, LOCALE_ID, OnInit, ViewChild, ElementRef } from '@angular/core';
import {
  BASE_PATH,
  CaseSummariesService,
  CaseSummarizationInput,
  ChatHistoryItem,
  ChatService,
  CommentHistoryItem,
  CommentInput,
  CommentService,
  FavoriteService,
  FavoriteTypeEnum,
  HighlightInput,
  HighlightItem,
  HighlightResult,
  HighlightSectionEnum,
  HighlightService,
  IracCaseSummarizationInput, SemanticChatInput
} from '../../../../../services';
import { MainTab, SummarySubTab } from "../../../../../shared/enums/case-tab.enum";
import { ICaseSummaryUserUpdateResult } from "../_models/user-updates.model";
import { HttpClient } from "@angular/common/http";
import { AuthService } from "../../../../../auth/auth.service";
import { MessageService } from "primeng/api";
import { CaseData } from "../../../../../services/model/case-data";
import { OverlayPanel } from 'primeng/overlaypanel';
import { HighlightSectionComponent } from '../../../../highlight/highlight-section.component';

@Component({
  selector: 'app-case-details-viewer',
  templateUrl: './case-details-viewer.component.html',
  styleUrls: ['./case-details-viewer.component.css']
})
export class CaseDetailsViewerComponent implements OnInit {
  protected readonly httpClient = inject(HttpClient);
  protected readonly basePath = inject(BASE_PATH);
  protected readonly _messageService = inject(MessageService);
  selectedCaseActions!: string;
  selectedCaseReasons!: string;
  selectedCaseVerdicts!: string;
  selectedCaseTextToBeHighlight!: string;
  selectedNerList!: string;
  favoriteType = FavoriteTypeEnum;
  currentNetworkUser!: string | null;

  @ViewChild('highlightAction', { static: false }) highlightActionComponent!: HighlightSectionComponent;
  @ViewChild('highlightReason', { static: false }) highlightReasonComponent!: HighlightSectionComponent;
  @ViewChild('highlightVerdict', { static: false }) highlightVerdictComponent!: HighlightSectionComponent;

  value!: string;
  @Input() selectedCase: CaseData | null = null;
  protected title: string = 'نص القضية';
  protected readonly MainTab = MainTab;
  protected readonly SummarySubTab = SummarySubTab;
  protected currentTab: MainTab = MainTab.CaseText;
  summarySubTab: SummarySubTab = SummarySubTab.General;
  protected content: string = '';
  nerPredictWords: string[] = [];
  nerList: string[] = [];
  displayNoteDialog: boolean = false;
  currentNoteType: SummarySubTab | null = null;
  @Input() autoOpenChat = false;
  displayGeneralAddNoteButton: boolean = false;
  displayIracAddNoteButton: boolean = false;

  generalSummaryId: number | null = null;
  iracSummaryId: number | null = null;
  private currentCaseSummaryId: number | null = null;

  generalSummaryText: string = '';
  iracSummaryText: string = '';
  iracIssues: string = '';
  iracAnalysis: string = '';
  iracRules: string = '';
  iracConclusion?: string[] | null = [];
  noteText: string = '';

  displayUserUpdatesDialog: boolean = false;
  userUpdates: CommentHistoryItem[] = [];
  pinnedUpdates: ICaseSummaryUserUpdateResult[] = [];
  otherUpdates: ICaseSummaryUserUpdateResult[] = [];

  isEditingNote: boolean = false;
  editingNoteId: number | null = null;

  newMessage: string = '';

  // messages: { sender: 'user' | 'system', text: string }[] = [
  //   { sender: 'system', text: 'ماهو دور الجاني هنا؟' },
  //   { sender: 'user',   text: 'دور الجاني هنا يدور حول الاعتداء ...' },
  // ];

  messages: { sender: 'user' | 'system'; text: string; }[] = [];
  chatHistory: ChatHistoryItem[] = [];
  highlightHistory: HighlightItem[] = [];
  @ViewChild('chatBtn', { read: ElementRef }) chatBtn!: ElementRef<HTMLButtonElement>;
  // @ViewChild('scrollContainer') scrollContainer!: ElementRef;
  groupedChatHistory: { label: string; entries: ChatHistoryItem[]; }[] = [];

  @ViewChild('chatOverlay') chatOverlay!: OverlayPanel;
  constructor(
    private caseSummariesService: CaseSummariesService,
    private commentService: CommentService,
    private chatService: ChatService,
    private authService: AuthService,
    private favorite: FavoriteService,
    private highlightService: HighlightService,
    @Inject(LOCALE_ID) private locale: string
  ) {


  }

  sendMessage(): void {
    if (!this.newMessage.trim()) {
      return;
    }

    this.messages.push({ sender: 'user', text: this.newMessage });
    this.messages.push({
      sender: 'system',
      text: 'هذه إجابة الذكاء الاصطناعي ...'
    });

    this.queryChat();
  }

  deleteMessages(): void {
    if (!this.selectedCase) {
      return;
    }
    const input: SemanticChatInput = {
      caseId: this.selectedCase.caseId.toString()
    };
    this.chatService.deleteChat(input).subscribe({
      next: (response) => {
        let responseString = response.data.response?.toString() || '';
        this.messages = [];
        this.chatHistory = [];

        // this.chatHistory = response.data?.chatHistory ?? [];
        this.newMessage = '';
        // this.scrollToBottom();
      },
      error: (err) => {
        console.error('Error querying chat:', err);
      }
    });
  }

  clearMessages() {
    this.messages = [];
    this.chatHistory = [];
  }

  queryChat() {
    if (!this.selectedCase) {
      return;
    }

    const input: SemanticChatInput = {
      document: this.selectedCase.cleanTxt ?? '',
      messages: this.newMessage,
      chatHistory: this.chatHistory,
      refId: this.selectedCase.refId
    };
    this.chatService.getChat(input).subscribe({
      next: (response) => {
        let responseString = response.data.response?.toString() || '';
        this.messages.push({ sender: 'system', text: responseString });

        if (response.data?.chatHistory && response.data?.chatHistory.length > 0) {
          this.chatHistory.push(...response.data.chatHistory);
        }

        // this.chatHistory = response.data?.chatHistory ?? [];
        this.newMessage = '';
        this.groupChatHistory();
      },
      error: (err) => {
        console.error('Error querying chat:', err);
      }
    });
  }

  groupChatHistory() {
    const now = new Date();
    const grouped: { [key: string]: ChatHistoryItem[] } = {
      'Older': [],
      'Last 7 Days': [],
      'Yesterday': [],
      'Today': [],
    };

    for (const entry of this.chatHistory) {
      if (!entry.creationDate) continue;
      const date = new Date(entry.creationDate + 'Z');
      const diffTime = now.getTime() - date.getTime();
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays === 0) {
        grouped['Today'].push(entry);
      } else if (diffDays === 1) {
        grouped['Yesterday'].push(entry);
      } else if (diffDays <= 7) {
        grouped['Last 7 Days'].push(entry);
      } else {
        grouped['Older'].push(entry);
      }
    }

    this.groupedChatHistory = Object.entries(grouped)
      .filter(([_, entries]) => entries.length > 0)
      .map(([label, entries]) => ({ label, entries }));
  }

  ngAfterViewInit() {
    if (this.autoOpenChat) {
      // defer until after view init
      window.scrollTo(0, document.body.scrollHeight || document.documentElement.scrollHeight);
      this.chatBtn.nativeElement.click();
    }
  }

  ngOnInit() {
    if (!this.selectedCase) {
      return;
    }
    this.DoesItHaveFavorites();

    // this.rawSectionTextMap[HighlightSectionEnum.Actions] = this.selectedCase.actions ?? '';
    // this.rawSectionTextMap[HighlightSectionEnum.Reasons] = this.selectedCase.reasons ?? '';
    // this.rawSectionTextMap[HighlightSectionEnum.Verdict] = this.selectedCase.verdict ?? '';

    // this.selectedCaseActions = this.selectedCase?.actions ?? '';
    // this.selectedCaseReasons = this.selectedCase?.reasons ?? '';
    // this.selectedCaseVerdicts = this.selectedCase?.verdict ?? '';

    this.authService.currentNetworkUser$.subscribe({
      next: (networkUser) => {
        this.currentNetworkUser = networkUser;
        this.queryChat();
        this.getHighlights();
      },
      error: (err) => console.error('Error fetching network user:', err),
    });

    this.title = 'نص القضية';
    this.currentTab = MainTab.CaseText;
    this.content = this.selectedCase.text!;
    this.nerList = this.selectedCase.groupedNamedEnities!;
    this.nerPredictWords = this.selectedCase.groupedKeywords!;
  }

  openAddNoteDialog() {

    this.noteText = '';
    this.displayNoteDialog = true;
    this.isEditingNote = false;
    this.editingNoteId = null;

    if (!this.currentCaseSummaryId) {
      return;
    }
  }

  saveComment() {
    console.log('Saving note type:', this.currentNoteType, 'Note text:', this.noteText);
    if (!this.noteText.trim()) {
      console.error('Note text is required');
      return;
    }

    var payload: CommentInput = {
      caseId: this.selectedCase?.caseId!.toString(),
      comment: this.noteText,
      content: this.selectedCase?.cleanTxt,
      refId: String(this.selectedCase?.refId ?? ''),
      typeId: FavoriteTypeEnum.Case
    };

    this.commentService.storeComment(payload).subscribe({
      next: () => {
        console.log('Note added successfully');
        this.displayNoteDialog = false;
        this.displayUserUpdatesDialog = false;
        this._messageService.add({
          severity: 'success',
          summary: 'نجحت العملية',
          detail: 'تم أضافة ملاحظة بنجاح'
        });
      },
      error: (err) => console.error('Error saving note:', err),
    });
  }

  openUpdateNoteDialog(noteId: number, noteText: string) {
    this.isEditingNote = true;
    this.editingNoteId = noteId;
    this.noteText = noteText;
    this.displayNoteDialog = true;
    this.displayUserUpdatesDialog = false;
  }

  updateTab(tab: MainTab) {
    this.currentTab = tab;

    if (tab === MainTab.Summary) {
      this.summarySubTab = SummarySubTab.General;
    }
  }


  objectKeys = Object.keys;
  isObject(val: any): boolean {
    return typeof val === 'object';
  }

  updateSummarySubTab(subTab: SummarySubTab) {
    this.summarySubTab = subTab;
  }

  generateSummary() {
    if (!this.selectedCase) {
      return;
    }

    this.displayGeneralAddNoteButton = false;
    this.displayIracAddNoteButton = false;

    if (this.summarySubTab === SummarySubTab.General) {
      const input: CaseSummarizationInput = {
        fullText: this.selectedCase.text ?? '',
        caseId: this.selectedCase.caseId,
        // recordId: this.selectedCase.recordId
      };

      this.caseSummariesService.generateGeneralSummarization(input).subscribe({
        next: (response) => {
          this.generalSummaryText = response.data.textSummarize || '';
          this.generalSummaryId = response.data?.caseAiSummaryId ?? null;
          this.displayGeneralAddNoteButton = this.generalSummaryText.trim().length > 0;
        },
        error: (err) => {
          console.error('Error generating general summary:', err);
        }
      });

    } else if (this.summarySubTab === SummarySubTab.IRAC) {
      const input: IracCaseSummarizationInput = {
        fullText: this.selectedCase.text ?? '',
        caseId: this.selectedCase.caseId,
        // recordId: this.selectedCase.recordId
      };

      this.caseSummariesService.generateIracSummarization(input).subscribe({
        next: (response) => {
          this.iracIssues = response.data.issues || '';
          this.iracRules = response.data.rules || '';
          this.iracAnalysis = response.data.analysis || '';
          this.iracConclusion = response.data.conclusion || [];
          this.iracSummaryText = response.data.textSummarize || '';
          this.iracSummaryId = response.data?.caseAiSummaryId ?? null;
          this.displayIracAddNoteButton = this.iracSummaryText.trim().length > 0;
        },
        error: (err) => {
          console.error('Error generating IRAC summary:', err);
        }
      });
    }
  }

  openUserUpdatesDialog() {
    this.userUpdates = [];
    this.pinnedUpdates = [];
    this.otherUpdates = [];
    let summaryId: number | null = null;

    const refId = this.selectedCase?.refId;
    if (!refId) {
      console.error('caseId is undefined');
      return;
    }

    this.commentService.getComments(refId.toString()).subscribe({
      next: (response) => {
        const userUpdatesArray = response.data || [];
        this.userUpdates = userUpdatesArray.commentHistory ?? [];
        this.displayUserUpdatesDialog = true;
      },
      error: (err) => console.error('Error fetching user updates:', err)
    });
  }

  sortUserUpdates(order: 'ASC' | 'DESC') {
    this.otherUpdates = this.sortArray(this.otherUpdates, order);
    console.log('Sorted Other Updates:', this.otherUpdates);
  }

  private sortArray(array: any[], order: 'ASC' | 'DESC'): any[] {
    array.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
    return order === 'DESC' ? array.reverse() : array;
  }



  @ViewChild('highlightContainer', { static: false }) containerRef!: ElementRef;

  showColorPicker = false;
  colorPickerPosition = { x: 0, y: 0 };
  highlightColors = ['#fffa65', '#a3f7bf', '#ffb3ba', '#bae1ff'];
  // currentSection: 'Actions' | 'Reasons' | 'Verdict' | null = null;
  private currentRange: Range | null = null;

  // handleTextSelection(event: Event, section: typeof this.currentSection) {
  // const mouseEvent = event as MouseEvent;
  //   const selection = window.getSelection();
  //   if (!selection || selection.isCollapsed) {
  //     this.showColorPicker = false;
  //     return;
  //   }


  //   this.currentSection = section;            // remember which section
  //   this.currentRange = selection.getRangeAt(0).cloneRange();

  //   const rect = this.currentRange.getBoundingClientRect();
  //   this.colorPickerPosition = {
  //     x: rect.left + window.scrollX,
  //     y: rect.top + window.scrollY - 40
  //   };
  //   this.showColorPicker = true;
  // }

  loading = false;
  isFavorite = false;
  addToFavorite() {
    this.favorite.storeFavorite({
      refId: this.selectedCase?.refId,
      typeId: FavoriteTypeEnum.Case,
      summary: this.selectedCase?.cleanTxt
    }).subscribe({
      next: () => {
        this.loading = false;
        this.isFavorite = true;
      },
      error: (err) => {
        this.loading = false;
        console.error('Error retrieving case details:', err);
      },
    });
  }

  DoesItHaveFavorites() {
    this.favorite.doesItHaveFavorites({
      refId: this.selectedCase?.refId,
      typeId: FavoriteTypeEnum.Case
    }).subscribe({
      next: (result) => {
        this.loading = false;
        this.isFavorite = result.data.isFavorite!;
      },
      error: (err) => {
        this.loading = false;
        console.error('Error retrieving case details:', err);
      },
    });
  }

  removeFavorite() {
    this.favorite.removeFavorites({
      refId: this.selectedCase?.refId,
      typeId: FavoriteTypeEnum.Case
    }).subscribe({
      next: () => {
        this.loading = false;
        this.isFavorite = false;
      },
      error: (err) => {
        this.loading = false;
        console.error('Error retrieving case details:', err);
      },
    });
  }

  saveHighlight(input: HighlightInput) {
    this.highlightService.saveHighlights(input).subscribe({
      next: (response) => {
        console.error('Added');
      },
      error: (err) => {
        console.error('Error querying chat:', err);
      }
    });
  }

  highlightfetched = false;
  getHighlights() {

    if (!this.selectedCase?.refId) {
      // nothing to load, or show an error/toast
      return;
    }
    this.highlightService.getHighlightsByRefId(this.selectedCase?.refId).subscribe({
      next: (response) => {
        if (response.data?.highlightItems && response.data?.highlightItems.length > 0) {
          this.highlightHistory = response.data.highlightItems ?? [];
        }
        this.highlightfetched = true;
        // setTimeout(() => this.applyServerHighlights(), 0);
      },
      error: (err) => {
        console.error('Error querying chat:', err);
      }
    });
  }

  // rawSectionTextMap: { [key in HighlightSectionEnum]?: string } = {};
  // applyHighlightOld(color: string) {
  //   if (!this.currentRange || !this.currentSection || !this.selectedCase?.refId) return;

  //   const range = this.currentRange;
  //   const section = this.currentSection as HighlightSectionEnum;
  //   const text = range.toString();

  //   // 1) Compute offsets against the raw text
  //   const rawText = this.rawSectionTextMap[section];
  //   if (rawText == null) { return }
  //   const startOffset = rawText.indexOf(text);
  //   const endOffset = startOffset + text.length;
  //   if (startOffset === -1) {
  //     console.warn('Selected text not found in raw text:', { section, text });
  //     return;
  //   }

  //   // 2) Insert the highlight span into the DOM
  //   const span = document.createElement('span');
  //   span.style.backgroundColor = color;
  //   span.textContent = text;
  //   span.style.borderRadius = '3px';
  //   span.style.padding = '0 2px';

  //   range.deleteContents();
  //   range.insertNode(span);
  //   window.getSelection()?.removeAllRanges();
  //   this.showColorPicker = false;

  //   // 3) Save via API
  //   const input: HighlightInput = {
  //     refId: this.selectedCase.refId,
  //     caseId: this.selectedCase.caseId.toString(),
  //     selectedText: text,
  //     sectionId: section,
  //     startOffset,
  //     endOffset,
  //     color,
  //     typeId: FavoriteTypeEnum.Case
  //   };
  //   this.saveHighlight(input);
  // }

  // setRangeFromText(selectedText: string): boolean {
  //   if (!selectedText.trim()) return false;

  //   const sections: HighlightSectionEnum[] = ['Actions', 'Reasons', 'Verdict'];

  //   for (const section of sections) {
  //     const rawText = this.rawSectionTextMap[section];
  //     if (!rawText) continue;

  //     const startIndex = rawText.indexOf(selectedText);
  //     if (startIndex === -1) continue;

  //     // Instead of getElementById, get the container element another way
  //     // For example, query by class name or data attribute that identifies section
  //     const sectionElement = document.querySelector(`[data-section="${section}"]`);
  //     if (!sectionElement) {
  //       console.warn(`No DOM element for section: ${section}`);
  //       continue;
  //     }

  //     const textNode = this.findTextNode(sectionElement, selectedText);
  //     if (!textNode) {
  //       console.warn(`Text node with "${selectedText}" not found in section element: ${section}`);
  //       continue;
  //     }

  //     const offsetInNode = textNode.textContent!.indexOf(selectedText);
  //     if (offsetInNode === -1) {
  //       console.warn(`Selected text not found in the text node content.`);
  //       continue;
  //     }

  //     const range = document.createRange();
  //     range.setStart(textNode, offsetInNode);
  //     range.setEnd(textNode, offsetInNode + selectedText.length);

  //     this.currentRange = range;
  //     this.currentSection = section;

  //     return true;
  //   }

  //   console.warn(`Selected text "${selectedText}" not found in any section.`);
  //   return false;
  // }

  clearDropdownHighlights() {
    const sections: HighlightSectionEnum[] = ['Actions', 'Reasons', 'Verdict'];
    sections.forEach(section => {
      const container = document.querySelector(`[data-section="${section}"]`);
      if (!container) return;

      const dropdownHighlights = container.querySelectorAll('span[data-source="dropdown"]');
      dropdownHighlights.forEach(span => {
        const parent = span.parentNode;
        if (!parent) return;
        parent.replaceChild(document.createTextNode(span.textContent || ''), span);
        parent.normalize();
      });
    });
  }

  findTextNode(element: Node, text: string): Text | null {
    for (const node of Array.from(element.childNodes)) {
      if (node.nodeType === Node.TEXT_NODE && node.textContent?.includes(text)) {
        return node as Text;
      } else if (node.hasChildNodes()) {
        const found = this.findTextNode(node, text);
        if (found) return found;
      }
    }
    return null;
  }

  resetHighlights() {
    // this.getHighlights();

    this.highlightActionComponent?.applyServerHighlights();
    this.highlightReasonComponent?.applyServerHighlights();
    this.highlightVerdictComponent?.applyServerHighlights();
  }

  // applyHighlight(color: string) {
  //   this.insertHighlight(color, true); // saves
  // }

  applyHighlighsOnCase(selectedText: string) {
    if (typeof selectedText !== 'string') {
      console.error('Expected string but got:', selectedText);
      return;
    }

    if (!selectedText.trim()) {
      console.warn('selectedText is empty or only whitespace');
      return;
    }

    const escapedText = selectedText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = new RegExp(escapedText, 'g');

    const updateSection = (
      component: HighlightSectionComponent | null | undefined,
      sectionKey: string
    ) => {
      if (!component || !component.containerRef?.nativeElement) return;

      const container = component.containerRef.nativeElement.querySelector(
        `[data-section="${sectionKey}"] .actions-text`
      ) as HTMLElement;

      const rawText = component.rawText ?? '';
      if (!rawText) return;

      if (regex.test(rawText)) {
        container.innerHTML = rawText.replace(regex, '<mark>$&</mark>');
      } else {
        container.innerHTML = rawText; // Reset to unhighlighted
      }
    };

    updateSection(this.highlightActionComponent, 'Actions');
    updateSection(this.highlightReasonComponent, 'Reasons');
    updateSection(this.highlightVerdictComponent, 'Verdict');
  }

  // applyHighlighsOnCaseOld(selectedText: string) {
  //   if (!selectedText) return;
  //   this.clearDropdownHighlights();
  //   // If you want to apply on all sections:
  //   Object.values(HighlightSectionEnum).forEach(section => {
  //     if (this.setRangeFromText(selectedText)) {
  //       this.insertHighlight('yellow', false, 'dropdown');  // just highlight, no save
  //     }
  //   });
  // }

  // private insertHighlight(color: string, save: boolean = true, source: 'user' | 'dropdown' = 'user') {
  //   if (!this.currentRange || !this.selectedCase?.refId) return;

  //   const range = this.currentRange;
  //   const text = range.toString();
  //   if (!text.trim()) return;

  //   // Determine which section the text belongs to
  //   let foundSection: HighlightSectionEnum | null = null;
  //   let startOffset = -1;
  //   let endOffset = -1;

  //   const sections: HighlightSectionEnum[] = ['Actions', 'Reasons', 'Verdict'];
  //   for (const section of sections) {
  //     const rawText = this.rawSectionTextMap[section];
  //     if (!rawText) continue;

  //     const offset = rawText.indexOf(text);
  //     if (offset !== -1) {
  //       foundSection = section;
  //       startOffset = offset;
  //       endOffset = offset + text.length;
  //       break;
  //     }
  //   }

  //   if (!foundSection || startOffset === -1) {
  //     console.warn('Selected text not found in any section:', { text });
  //     return;
  //   }

  //   // 2) Insert highlight span into DOM
  //   const span = document.createElement('span');
  //   span.style.backgroundColor = color;
  //   span.dataset['source'] = source;
  //   span.textContent = text;
  //   span.style.borderRadius = '3px';
  //   span.style.padding = '0 2px';

  //   range.deleteContents();
  //   range.insertNode(span);
  //   window.getSelection()?.removeAllRanges();
  //   this.showColorPicker = false;

  //   // 3) Save if needed
  //   if (save) {
  //     const input: HighlightInput = {
  //       refId: this.selectedCase.refId,
  //       caseId: this.selectedCase.caseId.toString(),
  //       selectedText: text,
  //       sectionId: foundSection,
  //       startOffset,
  //       endOffset,
  //       color,
  //       typeId: FavoriteTypeEnum.Case
  //     };
  //     this.saveHighlight(input);
  //   }
  // }



  // private applyServerHighlights() {
  //   const sectionMap: Record<HighlightSectionEnum, string> = {
  //     Actions: 'actions-text',
  //     Reasons: 'reasons-text',
  //     Verdict: 'verdict-text'
  //   };

  //   // 1) Group your HighlightItems by SectionId
  //   const bySection = this.highlightHistory.reduce(
  //     (acc: Record<HighlightSectionEnum, HighlightItem[]>, hi: HighlightItem) => {
  //       (acc[hi.SectionId] ||= []).push(hi);
  //       return acc;
  //     },
  //     {} as Record<HighlightSectionEnum, HighlightItem[]>
  //   );

  //   // 2) For each section, reset its text-holder and re-apply all highlights
  //   for (const sectionKey of Object.keys(bySection) as HighlightSectionEnum[]) {
  //     const items = bySection[sectionKey]!;
  //     const container = this.containerRef.nativeElement.querySelector(
  //       `[data-section="${sectionKey}"]`
  //     ) as HTMLElement | null;
  //     if (!container) continue;

  //     // find only the text-holder div we wrapped in the template
  //     const textHolder = container.querySelector(
  //       `.${sectionMap[sectionKey]}`
  //     ) as HTMLElement | null;
  //     if (!textHolder) continue;

  //     // reset it back to raw text
  //     textHolder.textContent = this.rawSectionTextMap[sectionKey] || '';

  //     // 3) apply in reverse order so offsets don’t shift
  //     items
  //       .sort((a, b) => b.StartOffset - a.StartOffset)
  //       .forEach((hi: HighlightItem) => {
  //         const raw = this.rawSectionTextMap[sectionKey] || '';
  //         const snippet = raw.slice(hi.StartOffset, hi.EndOffset);

  //         // walk only textHolder’s Text nodes
  //         const walker = document.createTreeWalker(textHolder, NodeFilter.SHOW_TEXT);
  //         let charCount = 0;
  //         let startNode: Text | null = null, endNode: Text | null = null;
  //         let startInNode = 0, endInNode = 0, node: Text | null;

  //         while ((node = walker.nextNode() as Text | null)) {
  //           const len = node.textContent?.length ?? 0;

  //           if (!startNode && hi.StartOffset >= charCount && hi.StartOffset < charCount + len) {
  //             startNode = node;
  //             startInNode = hi.StartOffset - charCount;
  //           }
  //           if (!endNode && hi.EndOffset > charCount && hi.EndOffset <= charCount + len) {
  //             endNode = node;
  //             endInNode = hi.EndOffset - charCount;
  //           }
  //           charCount += len;
  //           if (startNode && endNode) break;
  //         }

  //         if (startNode && endNode) {
  //           const range = document.createRange();
  //           range.setStart(startNode, startInNode);
  //           range.setEnd(endNode, endInNode);

  //           const span = document.createElement('span');
  //           span.style.backgroundColor = hi.Color ?? '';
  //           span.textContent = snippet;
  //           span.style.borderRadius = '3px';
  //           span.style.padding = '0 2px';

  //           range.deleteContents();
  //           range.insertNode(span);
  //           range.detach?.();
  //         } else {
  //           console.warn('Could not map highlight back into DOM nodes', hi);
  //         }
  //       });
  //   }
  // }

}


export class ChatHistoryItemImpl implements ChatHistoryItem {
  role?: string | null;
  content?: string | null;

  constructor(role?: string | null, content?: string | null) {
    this.role = role ?? null;
    this.content = content ?? null;
  }
}
