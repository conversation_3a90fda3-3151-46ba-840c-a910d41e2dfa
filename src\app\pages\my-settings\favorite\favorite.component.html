<!-- <p class="text-center text-primary display-1">
  قريبًا
</p> -->


<ul class="nav nav-tabs mb-3">
  <li class="nav-item" *ngFor="let section of sectionNames">
    <a class="nav-link" [class.active]="selectedSection === section" (click)="selectedSection = section"
      href="javascript:void(0)">
      {{ section }}
    </a>
  </li>
</ul>

<div *ngIf="selectedSection === Section.All">
  <div *ngIf="favorites.length === 0" class="text-center text-muted">
    <p>لا توجد عناصر مضافة للمفضلة</p>
  </div>

  <div *ngIf="favorites.length > 0" class="row" dir="rtl">
    <div class="col-12 mb-2" *ngFor="let entry of favorites">
      <app-favorite-item [entry]="entry" (delete)="onFavoriteDeleted($event)"></app-favorite-item>
    </div>
  </div>
</div>

<div *ngIf="selectedSection === Section.Case">
  <div *ngIf="cases.length === 0" class="text-center text-muted">
    <p>لا توجد قضايا مضافة للمفضلة</p>
  </div>

  <div *ngIf="cases.length > 0" class="row" dir="rtl">
    <div class="col-12 mb-2" *ngFor="let entry of cases">
      <app-favorite-item [entry]="entry"></app-favorite-item>
    </div>
  </div>
</div>

<div *ngIf="selectedSection === Section.Law">
  <div *ngIf="laws.length === 0" class="text-center text-muted">
    <p>لا توجد قوانين مضافة للمفضلة</p>
  </div>

  <div *ngIf="laws.length > 0" class="row" dir="rtl">
    <div class="col-12 mb-2" *ngFor="let entry of laws">
      <app-favorite-item [entry]="entry"></app-favorite-item>
    </div>
  </div>
</div>






<!-- <h3 class="pb-2 text-primary">المفضلة</h3> -->

<!-- <p-dialog header="تفاصيل المحتوى" [(visible)]="dialogVisible" [modal]="true" [style]="{width: '50vw'}">
  <app-case-details-viewer [selectedCase]="selectedResult" *ngIf="isCaseDetailsVisible" />
</p-dialog> -->

<!-- <div *ngIf="favorites.length === 0" class="text-center text-muted">
  <p>لا توجد عناصر مضافة للمفضلة</p>
</div>

<div *ngIf="favorites.length > 0" class="row" dir="rtl">
  <div class="col-12 mb-2" *ngFor="let entry of favorites">
    <div class="card p-3 d-flex flex-row-reverse justify-content-between align-items-center">


      <div class="d-flex align-items-center gap-2 mb-3">
        <button class="btn btn-primary  py-2 px-3  text-white" (click)="showDialog(entry)">{{ 'عرض'}}</button>

        <img src="assets/svg/trash.svg" alt="حذف" width="40" height="40" class="cursor-pointer"
          (click)="confirmingEntry = entry; displayDeleteConfirm = true">
      </div>


      <div class="w-100 ms-3 text-start">
        <div><strong>رقم الحالة:</strong> {{ entry.RefId || 'غير متوفر' }}</div>
        <div><strong>المحتوى:</strong>
          <div class="summary-text">
            {{ entry.summary || 'غير متوفر' }}
          </div>
        </div>
        <div><strong>نوع:</strong>
          {{ entry.TypeId === 1 ? 'قضية' : entry.TypeId === 2 ? 'قانون' : 'غير متوفر' }}
        </div>

        <div class="text-muted"><strong>التاريخ:</strong> {{ entry.creationDate | date: 'MM/dd/yyyy' }}</div>
      </div>

    </div>
  </div>
</div> -->


<!-- <p-dialog header="تأكيد الحذف" [(visible)]="displayDeleteConfirm" [modal]="true" [closable]="true"
  [dismissableMask]="true" [style]="{ width: '350px' }">
  <p>هل أنت متأكد أنك تريد إزالة هذه القضية/القانون من المفضلة؟</p>
  <div class="d-flex justify-content-end gap-2 mt-3">
    <button type="button" class="btn btn-warning text-primary fw-bold d-flex"
      (click)="displayDeleteConfirm = false">إلغاء</button>
    <button type="button" class="btn btn-danger text-primary fw-bold d-flex"
      (click)="removeFavorite(confirmingEntry); displayDeleteConfirm = false">
      حذف
    </button>
  </div>
</p-dialog> -->
