<!-- <div class="card-body py-3">
  <div class="d-flex align-items-center justify-content-between">
    <span class="p-input-icon-left">
      <i class="pi pi-search"></i>
      <input
        type="text"
        class="p-inputtext-sm form-control"
        pInputText
        placeholder="البحث .."
        [(ngModel)]="value"
      />
    </span>
  </div>
</div> -->

@if(selectedLaw){

<div class="card-body py-3">
  <div class="d-flex justify-content-between align-items-center">
    <label class="fw-bold fs-5">
      {{ selectedLaw.systemName }}
    </label>

    <span class="badge bg-secondary p-2">
      {{ selectedLaw.state }}
    </span>
  </div>
  <div class="d-flex gap-4 mt-4 align-items-lg-center flex-column flex-lg-row">
    <small *ngIf="selectedLaw.dateOfCreation">
      <span class="fw-bold"> تاريخ الإنشاء </span>
      <br />
      {{ selectedLaw.dateOfCreation }}
    </small>

    <small *ngIf="selectedLaw.dateOfPublication">
      <span class="fw-bold"> تاريخ النشر </span>
      <br />
      {{ selectedLaw.dateOfPublication }}
    </small>
  </div>
</div>
<div class="card-body py-3">
  <div class="d-inline-flex align-items-center gap-2 my-1">
    <button
      class="tab-button"
      [ngClass]="{ active: title === 'نص النظام و القانون' }"
      (click)="updateContent('نص النظام و القانون')"
      *ngIf="selectedLaw.systemTextHeader"
    >
      نص النظام و القانون
    </button>
    <button
      class="tab-button"
      [ngClass]="{ active: title === 'محتوى النظام و القانون' }"
      (click)="updateContent('محتوى النظام و القانون')"
      *ngIf="selectedLaw.content"
    >
      محتوى النظام و القانون
    </button>
    <button
      class="tab-button"
      [ngClass]="{ active: title === 'ملخص النظام و القانون' }"
      (click)="updateContent('ملخص النظام و القانون')"
      *ngIf="selectedLaw.systemBrief"
    >
      ملخص النظام و القانون
    </button>
    <button
      class="tab-button"
      [ngClass]="{ active: title === 'التعديلات' }"
      (click)="updateContent('التعديلات')"
      *ngIf="selectedLaw.changes"
    >
      التعديلات
    </button>

    <button
      class="tab-button"
      [ngClass]="{ active: title === 'الباب والمادة' }"
      (click)="updateContent('الباب والمادة')"
      *ngIf="selectedLaw.gate || selectedLaw.article"
    >
      الباب والمادة
    </button>
    <a
      *ngIf="isFavorite"
      href="javascript:void(0)"
      (click)="removeFavorite()"
      title="إزالة من المفضلة"
    >
      <i class="pi pi-bookmark-fill fs-5 text-primary"></i>
    </a>

    <a
      *ngIf="!isFavorite"
      href="javascript:void(0)"
      (click)="addToFavorite()"
      title="إضافة إلى المفضلة"
    >
      <i class="pi pi-bookmark fs-5 text-secondary"></i>
    </a>
  </div>
</div>

<div
  class="card-body py-3"
  *ngIf="title === 'نص النظام و القانون' && selectedLaw.systemTextHeader"
>
  <div class="d-flex justify-content-between p-1">
    <label class="fs-5 fw-bold">
      {{ title }}
    </label>
  </div>
  <!-- <div class="mt-3 scroll-lg text-justify pe-1" style="white-space: pre-line;">
    {{ selectedLaw.systemTextHeader }}
  </div> -->

  <app-highlight-section
    #highlightRegulation
    *ngIf="selectedLaw && highlightfetched"
    [section]="'Regulation'"
    [selectedText]="selectedLaw.systemTextHeader || ''"
    [refId]="selectedLaw.refId"
    [header]="'نص الحكم'"
    [contentStyle]="{
      'white-space': 'pre-line',
      'font-size': '14px',
      'line-height': '1.7',
      'text-align': 'justify'
    }"
    [favoriteType]="favoriteType.Regulation"
    [rawText]="selectedLaw.systemTextHeader || ''"
    [highlightHistory]="highlightHistory"
  >
  </app-highlight-section>
</div>
<div
  class="card-body py-3"
  *ngIf="title === 'محتوى النظام و القانون' && selectedLaw.content"
>
  <div class="d-flex justify-content-between p-1">
    <label class="fs-5 fw-bold">
      {{ title }}
    </label>
  </div>
  <div class="mt-3 scroll-lg text-justify pe-1" style="white-space: pre-line">
    {{ selectedLaw.content }}
  </div>
</div>

<div
  class="card-body py-3"
  *ngIf="title === 'ملخص النظام و القانون' && selectedLaw.systemBrief"
>
  <div class="d-flex justify-content-between align-items-center p-1">
    <label class="fs-5 fw-bold text-justify">
      {{ title }}
    </label>
  </div>
  <div class="mt-3 scroll-lg text-justify" style="white-space: pre-line">
    {{ selectedLaw.systemBrief }}
  </div>
</div>

<div
  class="card-body py-3"
  *ngIf="title === 'التعديلات' && selectedLaw.changes"
>
  <div class="d-flex justify-content-between align-items-center p-1">
    <label class="fs-5 fw-bold text-justify">
      {{ title }}
    </label>
  </div>
  <div class="mt-3 text-justify" style="white-space: pre-line">
    {{ selectedLaw.changes }}
  </div>
</div>

<div
  class="card-body py-3"
  *ngIf="title === 'الباب والمادة' && (selectedLaw.gate || selectedLaw.article)"
>
  <div class="mt-3 d-flex justify-content-start gap-4">
    <label class="fs-6" *ngIf="selectedLaw.gate">
      <span class="fw-bold"> الباب </span>
      <br />
      {{ selectedLaw.gate }}
    </label>
    <label class="fs-6" *ngIf="selectedLaw.article">
      <span class="fw-bold"> المادة </span>
      <br />
      {{ selectedLaw.article }}
    </label>
  </div>
</div>

<comments-shared
  *ngIf="selectedLaw"
  [refId]="selectedLaw.refId ?? ''"
  [text]="selectedLaw.systemName ?? ''"
  [typeId]="favoriteType.Regulation"
></comments-shared>

}
