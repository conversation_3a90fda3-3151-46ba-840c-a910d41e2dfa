// .cursor-pointer {
//   cursor: pointer;
// }
// .layout {
//   display: flex;
// }
// .main-content {
//   width: 100%;
// }
// .offcanvas-header {
//   position: absolute;
//   right: 0;
//   top: -8px;
// }
// .sidebar-mobile {
//   transition: 0.5s;
// }
// .nav-brand {
//   padding: 20px 10px 10px 10px;
// }
// .header {
//   border-bottom: 1px solid #e2e7f1;
//   justify-content: space-between;
//   padding: 12px 24px;
//   z-index: 1;
//   position: sticky;
//   top: 0;
// }

// .mobile {
//   img {
//     width: 100%;
//     padding: 10px;
//   }
// }
// .side-nav-content {
//   display: flex;
//   justify-content: space-between;
//   flex-direction: column;
//   top: 0;
//   position: sticky;
//   box-shadow: 2px 0px 4px rgba(28, 43, 101, 0.1);
//   height: 100vh;
//   overflow-y: scroll;
//   li.active {
//     background-color: #e8f6ff;
//     color: #0075c9;
//     transition: all 0.5s;
//   }
//   ul {
//     padding: 0;
//     margin-bottom: 0;
//     li {
//       cursor: pointer;
//       list-style: none;
//       padding: 12px;
//       margin: 8px;
//       border-radius: 6px;
//       span {
//         color: #7c7c7c;
//       }
//       i {
//         padding: 0 10px 0 15px;
//       }
//     }
//   }
// }
// ::ng-deep {
//   .p-inputtext {
//     height: 36px;
//     width: 100%;
//   }
//   .p-menu {
//     border: none;
//   }
//   .p-sidebar {
//     box-shadow: -50px 0px 80px 0px rgba(0, 0, 0, 0.24);
//   }
//   .p-sidebar-right {
//     width: 25rem !important;
//   }
// }
// @media only screen and (max-width: 768px) {
//   .side-nav-content {
//     height: 220vw;
//   }
// }

// @media only screen and (max-width: 768px) {
//   .btn-pos {
//     position: absolute;
//     top: 18px;
//     left: 10px;
//   }
//   .page-title {
//     margin: 0 30px;
//   }
// }
