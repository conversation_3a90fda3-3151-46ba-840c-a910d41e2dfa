import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AppLayoutComponent } from './app-layout/app-layout.component';
import { LangSwitchComponent } from './lang-switch/lang-switch.component';
import { RouterModule, RouterOutlet } from '@angular/router';
import { AngularSvgIconModule } from 'angular-svg-icon';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({

  declarations: [
    LangSwitchComponent,
  ],
  imports: [
    CommonModule,
    AppLayoutComponent,
    RouterModule,
    RouterOutlet,
    AngularSvgIconModule,
    TranslateModule
  ],
  exports: [
    AppLayoutComponent,
    // SideNavbarComponent,
    // LangSwitchComponent,
    // NavigationHeaderComponent,
    // SvgIconComponent
  ]

})
export class LayoutModule { }
