<div #highlightContainer *ngIf="selectedText">
 <!-- class="scroll-lg text-justify pe-1" style="white-space: pre-line;" -->
  <div style="margin:0; padding:0;" (mouseup)="handleTextSelection($event)" [attr.data-section]="section" [ngStyle]="contentStyle">
    <div><label class="fw-bold">{{header}}</label></div>
    <div class="actions-text" [innerHTML]="selectedText">

    </div>
  </div>

  <div *ngIf="showColorPicker" [ngStyle]="{ top: colorPickerPosition.y + 'px', left: colorPickerPosition.x + 'px' }"
    class="color-picker-popup position-absolute bg-white border p-2 rounded shadow">
    <div class="d-flex gap-2">
      <div *ngFor="let color of highlightColors" class="color-circle" [ngStyle]="{ backgroundColor: color }"
        (mousedown)="applyHighlight(color)"></div>
    </div>
  </div>

</div>
