import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IsValidFieldPipe } from '../is-valid-field.pipe';


@Component({
  selector: 'lib-form-field-error-message',
  standalone: true,
  templateUrl: './form-field-error-message.component.html',
  imports: [CommonModule, IsValidFieldPipe]
})
export class FormFieldErrorMessageComponent {
  @Input() form!: FormGroup;
  @Input() formControlKey!: string;
  @Input() formValidatorErrorsIn?: string[];
  @Input() formValidatorErrorsOut?: string[];
  @Input() message!: string;
  @Input() submitted: boolean = false;
}
