/**
 * MOSAED.Internal.Api
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface NamedEntities { 
    person?: Array<string> | null;
    job?: Array<string> | null;
    location?: Array<string> | null;
    organization?: Array<string> | null;
    timeAndDate?: Array<string> | null;
    law?: Array<string> | null;
    hadith?: Array<string> | null;
    quran?: Array<string> | null;
    jursp?: Array<string> | null;
    claims?: Array<string> | null;
    amount?: Array<string> | null;
    nationality?: Array<string> | null;
    decisionNumber?: Array<string> | null;
    contractNumber?: Array<string> | null;
    plaintiff?: Array<string> | null;
    defendant?: Array<string> | null;
    evidenceAndDocuments?: Array<string> | null;
    facts?: Array<string> | null;
    jurisdiction?: Array<string> | null;
    legalAllegations?: Array<string> | null;
    defenses?: Array<string> | null;
    legalBriefs?: Array<string> | null;
    witnesses?: Array<string> | null;
}

