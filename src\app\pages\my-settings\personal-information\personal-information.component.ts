import { Component, OnInit } from '@angular/core';
import { MySettingsBaseComponent } from '../my-settings-base.component';
import { UserProfileService, UserProfileViewResult, UserProfileViewResultPortalResponse } from '../../../services';

@Component({
  selector: 'app-personal-information',
  templateUrl: './personal-information.component.html',
  styleUrls: ['./personal-information.component.css']
})
export class PersonalInformationComponent extends MySettingsBaseComponent implements OnInit {

  constructor(private userProfileService: UserProfileService) {
    super('personalInformation')
  }
  ngOnInit() {
    this.getUserProfile();
  }
  user: UserProfileViewResult | undefined;
  loading = false;
  getUserProfile() {
    this.loading = true;

    this.userProfileService.getUserProfileOfLoggedInUser().subscribe({
      next: (response: UserProfileViewResultPortalResponse) => {
        this.loading = false;
        if (!response) return;

        this.user = response.data || null;
      },
      error: (err: any) => {
        this.loading = false;
        console.error('Error retrieving User Profile:', err);
      },
    });
  }

}
