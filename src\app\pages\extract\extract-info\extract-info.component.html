<!-- Search Results Side Panel -->
<div
  *ngIf="showSearchPanel"
  class="search-results-panel"
  [ngClass]="{ 'show-panel': showSearchPanel }"
>
  <div class="search-results-header">
    <div class="search-tabs">
      <button
        class="search-tab-btn"
        [ngClass]="{ active: searchType === SearchTypeEnum.byLaw }"
        (click)="setSearchType(SearchTypeEnum.byLaw)"
      >
        الأنظمة والقوانين
      </button>
      <button
        class="search-tab-btn"
        [ngClass]="{ active: searchType === SearchTypeEnum.byIssue }"
        (click)="setSearchType(SearchTypeEnum.byIssue)"
      >
        القضايا المشابهة
      </button>
    </div>
  </div>

  <div class="search-results-count">
    تم العثور على نتائج مرتبطة بمستندك. يمكنك التمرير للاطلاع عليها، أو النقر
    على أي منها لعرض التفاصيل.
  </div>

  <div class="search-results-list">
    <app-search-result-list
      #searchResultList
      *ngIf="isSearchResultVisible"
      [isSearchTypeByLaw]="searchType === SearchTypeEnum.byLaw"
      (goBackToSearch)="hideSearchResults()"
      (lawSelected)="onLawSelected($event)"
      (caseSelected)="onCaseSelected($event)"
    />
  </div>

  <!-- Toggle button on the side -->
  <button class="panel-toggle-btn" (click)="toggleSearchPanel()">
    <i
      class="pi"
      [ngClass]="{
        'pi-chevron-left': showSearchPanel,
        'pi-chevron-right': !showSearchPanel
      }"
    ></i>
  </button>
</div>

<!-- Move toggle button outside the panel so it stays visible -->
<button class="panel-toggle-btn" (click)="toggleSearchPanel()">
  <i
    class="pi"
    [ngClass]="{
      'pi-chevron-left': showSearchPanel,
      'pi-chevron-right': !showSearchPanel
    }"
  ></i>
</button>

<main class="pt-1">
  <div class="container-xxl d-flex flex-column vertical-fade-scroll">
    <!-- <h3 class="text-primary fw-bold">البحث الدلالي</h3>
    <nav class="nav list-group nav-pills flex-column fw-bold">
      <ol class="breadcrumb pt-1 pb-3">
        <li class="breadcrumb-item"><a [routerLink]="['/']">الرئيسية</a></li>
        <li class="breadcrumb-item">
          <a [routerLink]="['/search/semantic']">البحث الدلالي</a>
        </li>
        <li class="breadcrumb-item active" aria-current="page">بحث بالمستند</li>
      </ol>
    </nav> -->

    <!-- File Upload Result UI -->
    <div class="file-upload-result align-self-end mb-4 fixed-height-container">
      <!-- Top section (file info) - always visible -->
      <div
        class="fixed-top-section"
        *ngIf="!selectedCaseDetails && !selectedLawDetails"
      >
        <div class="file-card">
          <div class="d-flex justify-content-between align-items-center">
            <div class="file-label">مستند</div>
          </div>
          <div class="file-name-container gap-2 d-flex align-items-center">
            <span class="file-check">
              <img src="../../../../assets/images/check-mark.svg" alt="" />
            </span>
            <span class="file-name">اسم الملف: {{ fileName }}.txt</span>
          </div>
        </div>

        <!-- Classification info -->
        <div class="classification-info text-end mt-3">
          <p class="text-muted small mt-2">شكرا لك لإضافة المستند</p>

          <!--remove this  -->
          <p>
            <span class="fw-bold">التصنيف الرئيسي</span> يبدو أن
            <span class="leading-text">{{ mainCategory }}</span>
            <span class="fw-bold">والتصنيف الفرعي</span>
            <span class="leading-text">{{ subCategory }}</span>
          </p>

          <p class="mt-3">
            هل ترغب في استعراض الأنظمة والقوانين أو القضايا المشابهة؟
          </p>

          <div class="d-flex justify-content-end mt-2 gap-3">
            <button
              class="btn btn-outline-primary"
              (click)="setSearchType(SearchTypeEnum.byLaw)"
            >
              <i class="pi pi-book me-2"></i>
              الأنظمة والقوانين
            </button>
            <button
              class="btn btn-outline-primary"
              (click)="setSearchType(SearchTypeEnum.byIssue)"
            >
              <i class="pi pi-gavel me-2"></i>
              القضايا المشابهة
            </button>
          </div>
        </div>
      </div>

      <!-- Scrollable content section -->
      <div>
        <div class="file-upload-result d-flex flex-column h-100">
          <!-- Top Half -->
          <div class="top-half">
            <app-case-details-viewer
              *ngIf="selectedCaseDetails"
              [selectedCase]="selectedCaseDetails"
            ></app-case-details-viewer>
            <app-law-details-viewer
              *ngIf="selectedLawDetails"
              [selectedLaw]="selectedLawDetails"
            ></app-law-details-viewer>
          </div>

          <!-- Bottom Half (Chatbot) -->
          <div *ngIf="selectedCaseDetails" class="bottom-half">
            <app-chat-pot
              [selectedCase]="selectedCaseDetails"
              class="full-width-chat"
            ></app-chat-pot>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="search-input align-self-end">
      <div class="search-container mt-4 mb-5">
        <div class="search-bar">
          <input type="text" class="form-control search-input" placeholder="البحث .." [(ngModel)]="value"
            [disabled]="searchType !== 1" />
          <button class="search-button" (click)="performSearch()" [disabled]="searchType !== 1">
            ابحث
          </button>
        </div>
      </div>
    </div> -->

    <!-- Original Content -->
    <!-- <div class="row g-4" dir="ltr">
      <p-splitter
        [style]="{ height: 'auto' }"
        styleClass="mb-5"
        [minSizes]="[40, 40]"
      >
        <ng-template pTemplate>
          <div
            dir="rtl"
            class="col-12 flex align-items-center justify-content-center"
          >
            <div class="d-block card border-0 main_height w-100">
              <div class="card-body">
                <app-extract-search-section
                  *ngIf="!isSearchResultVisible"
                  (searchCompleted)="showSearchResults()"
                />
                <app-search-result-list
                  *ngIf="isSearchResultVisible"
                  (goBackToSearch)="hideSearchResults()"
                />
              </div>
            </div>
          </div>
        </ng-template>

        <ng-template pTemplate>
          <div
            dir="rtl"
            class="col-12 flex align-items-center justify-content-center h-100"
          >
            <div class="d-block card main_height border-0 w-100 mb-4">
              <div class="card-body">
                <div
                  class="d-flex align-items-center justify-content-between mb-3"
                >
                  <span class="p-input-icon-left">
                    <i class="pi pi-search"></i>
                    <input
                      type="text"
                      class="p-inputtext-sm form-control"
                      pInputText
                      placeholder="البحث .."
                      [(ngModel)]="value"
                    />
                  </span>
                  <div class="d-flex gap-2 ms-2">
                    <div class="dropdown">
                      <button
                        class="btn btn-outline-primary dropdown-toggle"
                        type="button"
                        id="keywordsDropdown"
                        (click)="keyWordOverlay.toggle($event)"
                      >
                        الكلمات
                      </button>
                      <p-overlayPanel [scrollTop]="true" #keyWordOverlay>
                        <div class="dropdown-overlay-container">
                          <div class="row g-2 row-cols-1">
                            <div
                              class="col"
                              *ngFor="let keyword of predictKeyWords"
                            >
                              <span class="text-primary">
                                - {{ keyword }}
                              </span>
                            </div>
                          </div>
                        </div>
                      </p-overlayPanel>
                    </div>

                    <div class="dropdown">
                      <button
                        class="btn btn-outline-primary dropdown-toggle"
                        type="button"
                        id="nerDropdown"
                        (click)="entitiesOverlay.toggle($event)"
                      >
                        الأعلام
                      </button>
                      <p-overlayPanel [scrollTop]="true" #entitiesOverlay>
                        <div class="dropdown-overlay-container">
                          <div class="row g-2 row-cols-1">
                            <div class="col" *ngFor="let item of nerList">
                              <span class="text-primary"> - {{ item }} </span>
                            </div>
                          </div>
                        </div>
                      </p-overlayPanel>

                      <ul class="dropdown-menu" aria-labelledby="nerDropdown">
                        <li *ngFor="let ner of nerList">
                          <a class="dropdown-item">{{ ner }}</a>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div class="card-body" style="background-color: #f4f4f6">
                <div class="d-inline-flex align-items-center gap-2 my-2">
                  <button
                    class="btn btn-sm btn-outline-primary rounded-5"
                    [ngClass]="{ active: title === 'نص القضية' }"
                    (click)="updateContent('نص القضية', caseText)"
                  >
                    نص القضية
                  </button>
                </div>
              </div>

              <div class="card-body">
                <div class="d-flex justify-content-between p-1">
                  <h5 class="text-primary fw-bold">{{ title }}</h5>
                </div>
                <div
                  class="mt-3 scroll-lg text-justify pe-1"
                  id="case_text"
                  [innerHTML]="content"
                ></div>
              </div>
            </div>
          </div>
          /
        </ng-template>
      </p-splitter>
    </div> -->
  </div>
</main>
