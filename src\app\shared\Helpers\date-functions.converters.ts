// import { NgbDateStruct, NgbTimeStruct } from "@ng-bootstrap/ng-bootstrap";
// import { DevLog } from "../functions/log-functions";
// import { toGregorian } from "hijri-converter";
// import { dateHelper } from "./date-functions";

import { DevLog } from "../public.api";

function today() {
    return new Date();
}

// function toNgbDateStruct(date: Date) {

//     if (date === null || date === undefined) {
//         console.error("date is null or undefined");
//         return {
//             day: today().getDate(),
//             month: today().getMonth() + 1,
//             year: today().getFullYear()
//         };
//     }
//     try {

//         const result: NgbDateStruct = {
//             day: date.getDate(),
//             month: date.getMonth() + 1,
//             year: date.getFullYear()
//         };

//         return result;
//     } catch (error) {
//         console.error("Error in convertToNgbDateStruct passed value => ", date)
//         throw error;

//     }
// }

/**
 * convert .Net DateOnly (2000-12-25) object to Date
 */
function toDateFromDotnetDateOnly(dateString: string) {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(dateString)) {
        console.error('Invalid date string:', dateString);
        return null;
    }

    const dates = dateString.split('-');
    const date = new Date(+dates[0], (+dates[1]) - 1, +dates[2]);

    if (isNaN(date.getTime())) {
        console.error('Invalid date:', dateString);
        return null;
    }
    DevLog.Info(`Value ${dateString} is converted to => ${date}`)
    return date;
}

/**
 * convert ISO string (2000-12-01T00:00:00) to Date object
 * @param isoDate 
 * @returns 
 */
function toDateFromISO(isoDate: string): Date | null {
    const isoDateRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$/;

    if (!isoDateRegex.test(isoDate)) {
        console.error('Invalid ISO date string:', isoDate);
        return null;
    }

    const date = new Date(isoDate);

    if (isNaN(date.getTime())) {
        console.error('Invalid date:', isoDate);
        return null;
    }
    DevLog.Info(`Value ${isoDate} is converted to => ${date}`)
    return date;
}



// function toNgbDateStructFromHijri(hijriDate: string, seperator: string = '-'): NgbDateStruct {
//     if (!hijriDate)
//         throw new Error('hijri date is empty or invalid!')

//     const formattedDateWithDashes = hijriDate.replace(/\//g, seperator).split('-');
//     const day = +formattedDateWithDashes[2];
//     const month = +formattedDateWithDashes[1];
//     const year = +formattedDateWithDashes[0];

//     return { day: day, month: month, year: year } as NgbDateStruct;
// }


// function toUmmAlQura(date: Date): NgbDateStruct {

//     // Create a DateTimeFormat object for the Umm al-Qura calendar
//     const dateFormat = new Intl.DateTimeFormat('en-US-u-ca-islamic-umalqura', {
//         year: 'numeric',
//         month: '2-digit',
//         day: '2-digit'
//     });

//     const formattedDate = dateFormat.format(date);
//     // Replace slashes with dashes
//     const formattedDateWithDashes = formattedDate.replace(/\//g, '-').replace(' AH', '').split('-');
//     const day = +formattedDateWithDashes[1];
//     const month = +formattedDateWithDashes[0];
//     const year = +formattedDateWithDashes[2];

//     return { day, month, year }
// }

// function toGregorianStructFromHijri(hijriDate: string): NgbDateStruct {

//     var hijriStruct = toNgbDateStructFromHijri(hijriDate);

//     var gregorianDate = toGregorian(hijriStruct.year, hijriStruct.month, hijriStruct.day);

//     return { day: gregorianDate.gd, month: gregorianDate.gm, year: gregorianDate.gy } as NgbDateStruct;
// }


// function toGregorianDateFromHijri(hijriDate: string): Date {

//     var hijriStruct = toNgbDateStructFromHijri(hijriDate);

//     var gregorianDate = toGregorian(hijriStruct.year, hijriStruct.month, hijriStruct.day);

//     return new Date(gregorianDate.gy, gregorianDate.gm - 1, gregorianDate.gd);
// }

// function to12HourFormat(selectedTime?: string): string {
//     if (!selectedTime) return '';

//     const time = toTimeStruct(selectedTime)

//     let amPm = 'ص';
//     let convertedHour = time.hour;
//     if (time.hour >= 12) {
//         amPm = 'م';
//         convertedHour = time.hour === 12 ? 12 : time.hour - 12;
//     } else if (time.hour === 0) {
//         convertedHour = 12;
//     }
//     const paddedMinute = time.minute.toString().padStart(2, '0');
//     return `${convertedHour}:${paddedMinute} ${amPm}`;
// }

// function toTimeStruct(timeString: string): NgbTimeStruct {
//     const timeParts = timeString.split(':');
//     const hours = parseInt(timeParts[0], 10);
//     const minutes = parseInt(timeParts[1], 10);
//     const seconds = parseInt(timeParts[2], 10);

//     return { hour: hours, minute: minutes, second: seconds };
// }

function toISOFromDateAny(stringDate: any) {
    return (stringDate as unknown as Date).toISOString();
}

// function toDateOnlyFromDate(date: Date) {
//     return dateHelper.formatDateToString(date, 'yyyy-mm-dd');
// }

const dateHelperConverters = {
    // toUmmAlQura,
    // toNgbDateStructFromHijri,
    // toGregorianDateFromHijri,
    // toGregorianStructFromHijri,
    toDateFromISO,
    toDateFromDotnetDateOnly,
    //toNgbDateStruct,
    //to12HourFormat,
    toISOFromDateAny,
    //toDateOnlyFromDate
}

export { dateHelperConverters }