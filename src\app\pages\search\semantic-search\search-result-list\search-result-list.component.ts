import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  inject,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { TableLazyLoadEvent, TableModule } from 'primeng/table';
// import { SearchFeaturesCreate, SearchFeaturesPublic, SearchFeaturesPublicDataInner, SearchService } from '../../../../services';
import { SearchTypeEnum } from '../../../../shared/models/search-type.enum';
import { ExtractInfoEvents } from '../../../../shared/events/extract.events';
import { SearchEvents } from '../../../../shared/events/public.api';
import { finalize, timeout } from 'rxjs';
import {
  CaseDocumentCommandResultPortalResponse,
  CreateCaseFeedbackInput,
  FavoriteService,
  FavoriteTypeEnum,
  FeedbackService,
  LawDocumentCommandResultPortalResponse,
  SearchByFileCommandResult,
  SemanticService,
} from '../../../../services';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { DecimalPipe } from '@angular/common';
import { BaseComponent } from '../../../../shared/base-component';
import { BaseTranslationKeys } from '../../../../shared/models/base-translation-keys.model';
import { ESearchingType } from '../../../../services/enums/searching-type.enum';

@Component({
  selector: 'app-search-result-list',
  templateUrl: './search-result-list.component.html',
  styleUrls: ['./search-result-list.component.css'],
})
export class SearchResultListComponent
  extends BaseComponent
  implements OnInit, OnChanges
{
  //private readonly SearchService = inject(SearchService);
  protected readonly defaultLimit = 5;
  @Output() goBackToSearch = new EventEmitter<void>();
  @Input() searchResultData: any;
  isCaseDetailsVisible: boolean = false;
  isLawDetailsVisible: boolean = false;
  selectedResult: any = null;
  protected title: string = 'نص القضية';
  protected content: string = '';
  nerPredictWords: string[] = [];
  nerList: string[] = [];
  value: string | undefined;
  @Input() isSearchTypeByLaw: boolean = false;
  isSearchByQuestion: boolean = false;

  protected readonly _feedbackService = inject(FeedbackService);
  showCaseFeedbackDialog = false;
  modifyCaseFeedback = false;
  submitCaseFeedbackInProgress = false;
  formGroup!: FormGroup;
  selectedCasePercentage!: number | any;
  _decimalPipeService = inject(DecimalPipe);
  selectedCaseForFeedback: any;
  createCaseFeedbackInput!: CreateCaseFeedbackInput;

  // Add new output event emitters
  @Output() lawSelected = new EventEmitter<any>();
  @Output() caseSelected = new EventEmitter<any>();

  get showResult() {
    return this.isCaseDetailsVisible || this.isLawDetailsVisible;
  }

  viewCaseDetails(id: number) {
    this.loading = true;
    this.isCaseDetailsVisible = false;
    this.isLawDetailsVisible = false;
    this.selectedResult = null;
    debugger;
    this.semantic.getCaseDocument(id).subscribe({
      next: (response: CaseDocumentCommandResultPortalResponse) => {
        this.loading = false;
        if (!response) return;

        this.selectedResult = response.data || null;
        this.isCaseDetailsVisible = true;
        debugger;
        // Emit the selected case details to parent component
        this.caseSelected.emit(this.selectedResult);
      },
      error: (err) => {
        this.loading = false;
        console.error('Error retrieving case details:', err);
      },
    });
  }

  viewLawDetails(lawId: number) {
    this.loading = true;
    this.isCaseDetailsVisible = false;
    this.isLawDetailsVisible = false;
    this.selectedResult = null;

    this.semantic.getLawDocument(lawId).subscribe({
      next: (response: LawDocumentCommandResultPortalResponse) => {
        this.loading = false;
        if (!response) return;

        this.selectedResult = response.data || null;
        this.isLawDetailsVisible = true;

        // Emit the selected law details to parent component
        this.lawSelected.emit(this.selectedResult);
      },
      error: (err) => {
        this.loading = false;
        console.error('Error retrieving law details:', err);
      },
    });
  }

  goBack() {
    this.isCaseDetailsVisible = false;
    this.isLawDetailsVisible = false;
  }

  protected searchResult = {
    data: [],
    limit: 10,
    offset: 0,
    total: 0,
  };

  totalRecords!: number;
  loading: boolean = false;

  protected searchQuestion!: string;

  constructor(
    private semantic: SemanticService,
    private favorite: FavoriteService
  ) {
    super(new BaseTranslationKeys('semantic', 'SearchResultListComponent'));
  }

  ngOnInit() {
    this.isSearchByQuestion = Boolean(
      JSON.parse(localStorage.getItem('isSearchByQuestion') || 'false')
    );
    this.initCaseFeedbackForm();
    this.searchQuestion = history.state.searchQuestion;
    this.loadSearchResults();
  }

  ngOnChanges(changes: SimpleChanges) {
    // Reload data when isSearchTypeByLaw input changes
    if (
      changes['isSearchTypeByLaw'] &&
      !changes['isSearchTypeByLaw'].firstChange
    ) {
      this.loadSearchResults();
    }
  }

  // Public method to refresh data (can be called from parent)
  refreshData() {
    this.loadSearchResults();
  }

  private loadSearchResults() {
    const response = JSON.parse(
      localStorage.getItem('search-response') ?? '""'
    ) as SearchByFileCommandResult;

    // Use the input property if available, otherwise determine from response
    const searchTypeByLaw =
      this.isSearchTypeByLaw !== undefined
        ? this.isSearchTypeByLaw
        : response?.lawResult != null;

    if (searchTypeByLaw) {
      const result = response?.lawResult?.data ?? [];
      this.searchResult.data = result as any;
      this.searchResult.total = response.lawResult.totalRecords;
    } else {
      const result = response?.caseResult?.caseData ?? [];
      this.searchResult.data = result as any;
      this.searchResult.total = response.caseResult.totalRecords;
    }
  }

  addToFavorite(selectedResult: any) {
    this.favorite
      .storeFavorite({
        refId: this.isSearchTypeByLaw ? selectedResult.id : selectedResult.id,
        typeId: this.isSearchTypeByLaw
          ? FavoriteTypeEnum.Regulation
          : FavoriteTypeEnum.Case,
        summary: this.isSearchTypeByLaw
          ? selectedResult.shortSystemBrief
          : selectedResult.shortCleanText,
      })
      .subscribe({
        next: () => {
          this.loading = false;
          const matchedItem = this.searchResult.data.find(
            (item: any) => item.id === selectedResult.id
          ) as any;
          if (matchedItem) {
            matchedItem.isFavorite = true;
          }
          // this.selectedResult = response.data || null;
        },
        error: (err) => {
          this.loading = false;
          console.error('Error retrieving case details:', err);
        },
      });
  }

  removeFavorite(selectedResult: any) {
    this.favorite
      .removeFavorites({
        refId: selectedResult.id,
        typeId: this.isSearchTypeByLaw
          ? FavoriteTypeEnum.Regulation
          : FavoriteTypeEnum.Case,
      })
      .subscribe({
        next: () => {
          this.loading = false;
          const matchedItem = this.searchResult.data.find(
            (item: any) => item.id === selectedResult.id
          ) as any;
          if (matchedItem) {
            matchedItem.isFavorite = false;
          }
        },
        error: (err) => {
          this.loading = false;
          console.error('Error retrieving case details:', err);
        },
      });
  }

  initCaseFeedbackForm() {
    this.formGroup = new FormGroup({
      feedbackPercentage: new FormControl(
        1,
        Validators.compose([Validators.required])
      ),
      feedbackNotes: new FormControl(
        '',
        Validators.compose([Validators.required])
      ),
    });
  }

  openCaseFeedbackDialog(_case: any) {
    this.showCaseFeedbackDialog = true;
    this.modifyCaseFeedback = false;
    this.selectedCaseForFeedback = _case;
    this.selectedCasePercentage = this._decimalPipeService.transform(
      _case.embeddingScore * 100,
      '1.2-2'
    );
    this.initCaseFeedbackForm();
  }

  onSubmitCaseFeedback(form: any) {
    let searchingType = ESearchingType.ByDocumentInCase;
    let searchedQuery = JSON.parse(
      localStorage.getItem('file_caseText') || '""'
    );
    this.createCaseFeedbackInput = {
      searchedQuery: searchedQuery,
      searchingType: searchingType,
      caseId: this.selectedCaseForFeedback.caseId,
      auditVerdictNumber: this.selectedCaseForFeedback.auditVerdictNumber,
      previousScore: this.selectedCasePercentage,
      feedbackScore: this.selectedCasePercentage,
      feedbackNote: null,
      caseData: JSON.stringify(this.selectedCaseForFeedback),
    };

    if (form) {
      if (form.valid) {
        this.createCaseFeedbackInput.feedbackScore =
          this.formGroup.get('feedbackPercentage')?.value + '';
        this.createCaseFeedbackInput.feedbackNote =
          this.formGroup.get('feedbackNotes')?.value;
      } else {
        return;
      }
    }

    this.submitCaseFeedbackInProgress = true;
    this._feedbackService
      .submiCaseFeedback(this.createCaseFeedbackInput)
      .pipe(
        finalize(() => {
          this.submitCaseFeedbackInProgress = false;
        })
      )
      .subscribe({
        next: (res) => {
          this.showCaseFeedbackDialog = false;
          this._messageService.add({
            severity: 'success',
            summary: 'نجاح',
            detail: 'تم تأكيد نسبة التشابه بنجاح',
          });
        },
        error: (err) => {},
      });
  }
}
