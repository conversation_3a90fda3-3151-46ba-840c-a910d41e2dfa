import { Pipe, PipeTransform } from '@angular/core';
import { Dom<PERSON>anitizer, SafeHtml } from '@angular/platform-browser';
import { marked } from 'marked';

@Pipe({
  name: 'markdown',
  standalone: true,  // <-- This is key
  pure: false
})
export class MarkdownPipe implements PipeTransform {
  constructor(private sanitizer: DomSanitizer) {}

  transform(value: string): SafeHtml {
    const html: string = marked.parse(value || '') as string;
    return this.sanitizer.bypassSecurityTrustHtml(html);
  }
}
