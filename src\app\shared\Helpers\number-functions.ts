const ones: { [key: number]: string } = {
    0: "صفر",
    1: "واحد",
    2: "اثنان",
    3: "ثلاثة",
    4: "أربعة",
    5: "خمسة",
    6: "ستة",
    7: "سبعة",
    8: "ثمانية",
    9: "تسعة",
    10: "عشرة",
    11: "أحد عشر",
    12: "اثنى عشر"
  };
  
  const tens: { [key: number]: string } = {
    1: "عشر",
    2: "عشرون",
    3: "ثلاثون",
    4: "أربعون",
    5: "خمسون",
    6: "ستون",
    7: "سبعون",
    8: "ثمانون",
    9: "تسعون"
  };
  
  const hundreds: { [key: number]: string } = {
    0: "صفر",
    1: "مائة",
    2: "مئتان",
    3: "ثلاثمائة",
    4: "أربعمائة",
    5: "خمسمائة",
    6: "ستمائة",
    7: "سبعمائة",
    8: "ثمانمائة",
    9: "تسعمائة"
  };
  
  const thousands: { [key: number]: string } = {
    1: "ألف",
    2: "ألفان",
    39: "آلاف",
    1199: "ألفًا"
  };
  
  const millions: { [key: number]: string } = {
    1: "مليون",
    2: "مليونان",
    39: "ملايين",
    1199: "مليونًا"
  };
  
  const billions: { [key: number]: string } = {
    1: "مليار",
    2: "ملياران",
    39: "مليارات",
    1199: "مليارًا"
  };
  
  const trillions: { [key: number]: string } = {
    1: "تريليون",
    2: "تريليونان",
    39: "تريليونات",
    1199: "تريليونًا"
  };


  function tafqeet(number: number): string {
    let value = "";
  
    if (
      number.toString().match(/^[0-9]+$/) !== null &&
      number.toString().length <= 14
    ) {
      switch (number.toString().length) {
        case 1:
        case 2:
          value = oneTen(number);
          break;
        case 3:
          value = hundred(number);
          break;
        case 4:
        case 5:
        case 6:
          value = thousand(number);
          break;
        case 7:
        case 8:
        case 9:
          value = million(number);
          break;
        case 10:
        case 11:
        case 12:
          value = billion(number);
          break;
        case 13:
        case 14:
        case 15:
          value = trillion(number);
          break;
      }
    }
  
    return value
      .replace(/وصفر/g, "")
      .replace(/وundefined/g, "")
      .replace(/ +(?= )/g, "")
      .replace(/صفر و/g, "")
      .replace(/صفر/g, "")
      .replace(/مئتان أ/, "مائتا أ")
      .replace(/مئتان م/, "مائتا م");
  }


  function oneTen(number: number): string {
    let value = "صفر";
  
    
    if (number <= 12) {
        value = ones[number]
    } else {
      const first = getNth(number, 0, 0);
      const second = getNth(number, 1, 1);
  
      if (tens[first] === "عشر") {
        value = ones[second] + " " + tens[first];
      } else {
        value = ones[second] + " و" + tens[first];
      }
    }
  
    return value;
  }


  function hundred(number: number): string {
    let value = "";
    
    const first = getNth(number, 0, 0);
  
    value = hundreds[first];
  
    value = value + " و" + oneTen(getNth(number, 1, 2));
    return value;
  }

  function thousand(number: number): string {
    return thousandsTrillions(
      thousands[1],
      thousands[2],
      thousands[39],
      thousands[1199],
      0,
      number,
      getNthReverse(number, 4)
    );
  }

  function million(number: number): string {
    return thousandsTrillions(
      millions[1],
      millions[2],
      millions[39],
      millions[1199],
      3,
      number,
      getNthReverse(number, 7)
    );
  }


  function billion(number: number): string {
    return thousandsTrillions(
      billions[1],
      billions[2],
      billions[39],
      billions[1199],
      6,
      number,
      getNthReverse(number, 10)
    );
  }


  function trillion(number: number): string {
    return thousandsTrillions(
      trillions[1],
      trillions[2],
      trillions[39],
      trillions[1199],
      9,
      number,
      getNthReverse(number, 13)
    );
  }


  function thousandsTrillions(one: string, two: string, three: string, eleven: string, diff: number, number: number, _other: number): string {
    let other = tafqeet(_other);
  
    if (other === "") {
      other = "صفر";
    }
  
    let value = "";
  
    const tens = getNth(number, 0, 1);
    switch (number.toString().length) {
      case 4 + diff:
        const ones = getNth(number, 0, 0);
        switch (ones) {
          case 1:
            value = `${one} و${other}`;
            break;
          case 2:
            value = `${two} و${other}`;
            break;
          default:
            value = `${oneTen(ones)} ${three} و${other}`;
            break;
        }
        break;
  
      case 5 + diff:
        switch (tens) {
          case 10:
            value = `${oneTen(tens)} ${three} و${other}`;
            break;
          default:
            value = `${oneTen(tens)} ${eleven} و${other}`;
            break;
        }
        break;
  
      case 6 + diff:
        const hundreds = getNth(number, 0, 2);
        const _two = getNth(number, 1, 2);
        let th = "";
        switch (_two) {
          case 0:
            th = one;
            break;
          default:
            th = eleven;
            break;
        }
        if (tens >= 100 && tens <= 199) {
            value = `${hundred(hundreds)} ${th} و${other}`;
        } else if (tens >= 200 && tens <= 299) {
            value = `${hundred(hundreds)} ${th} و${other}`;
        } else {
            value = `${hundred(hundreds)} ${th} و${other}`;
        }
        break;
    }
  
    return value;
  }


  function getNth(number: number, first: number, end: number): number {
    let finalNumber = "";
    for (let i = first; i <= end; i++) {
      finalNumber = finalNumber + String(number).charAt(i);
    }
    return parseInt(finalNumber);
  }
  
  function getNthReverse(number: number, limit: number): number {
    let finalNumber = "";
    let x = 1;
    while (x !== limit) {
      finalNumber = String(number).charAt(number.toString().length - x) + finalNumber;
      x++;
    }
  
    return parseInt(finalNumber);
  }

  const numberHelper = {tafqeet}

  export { numberHelper }