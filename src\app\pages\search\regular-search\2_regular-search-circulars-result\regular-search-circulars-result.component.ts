import { Component, OnInit } from '@angular/core';
import { RegularSearchBaseResult } from '../regular-search-base-result';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-regular-search-circulars-result',
  templateUrl: './regular-search-circulars-result.component.html',
  styleUrls: ['./regular-search-circulars-result.component.css']
})
export class RegularSearchCircularsResultComponent extends RegularSearchBaseResult<IRegularSearchCircularsResult[]> implements OnInit {

  protected override getDataObservable(): Observable<IRegularSearchCircularsResult[]> {
    return this._httpClient.get<IRegularSearchCircularsResult[]>(this._basePath + "/document/circulars/" + this.name)
  }


}

interface IRegularSearchCircularsResult {
  name: string;
  description: string;
  url: string;
  date: string;
  type: string;
  refNumber: string;
  state: string;
  source: string;
}
