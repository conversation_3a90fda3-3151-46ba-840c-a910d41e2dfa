// ---------- dropdown
p-dropdown {
  display: block;
  width: 100%;
  .p-dropdown {
    width: 100%;
    min-height: $form-control-height;
    border-radius: 2px;
    .p-dropdown-trigger {
      transform: scale(0.8);
    }
    .p-dropdown-clear-icon {
      [dir="rtl"] & {
        left: 2.357rem;
        right: auto;
      }
    }

  }
}
.p-dropdown:not(.p-disabled).p-focus, .p-dropdown:not(.p-disabled):hover { 
  border-color: #81bcb1;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(2, 121, 98, 0.25);
}
.p-dropdown-panel .p-dropdown-items .p-dropdown-item {
  max-width: 100%;
  // @extend .text-truncate;
  white-space: normal;
}
.p-dropdown-panel .p-dropdown-items .p-dropdown-item.p-highlight {
  background: #f1efec;
  color: $primary;
}
.p-dropdown-panel
  .p-dropdown-items
  .p-dropdown-item:not(.p-highlight):not(.p-disabled):hover {
  color: #495057;
  background: #f8f8f8;
}
@media screen and (min-width: 0) and (max-width: 767px) {
  .p-dropdown.p-dropdown-clearable .p-dropdown-label {
    max-width: calc(100vw - 100px);
  }
  .p-dropdown-panel {
    max-width: 80vw;
  }
}
[dir="rtl"] .p-dropdown-panel .p-dropdown-header {
  .p-dropdown-filter {
    padding-left: 1.5rem;
    padding-right: 0.5rem;
    margin: 0;
  }
  .p-dropdown-filter-icon {
    left: 0.5rem;
    right: auto;
  }
}
.mobile-panel {
  direction: ltr;
  max-width: 89px;
  text-align: left;
  .p-dropdown-header .p-dropdown-filter {
    padding: 0.5rem !important;
  }
  .p-dropdown-header .p-dropdown-filter-icon {
    display: none;
  }
}
.mobile-dropdown {
  direction: ltr;
  .p-dropdown-label {
    display: grid;
    place-items: center;
  }
}
.p-inputtext {
  height: $form-control-height;
  border: 1px solid #D8D8D8;

  padding: 0.5rem;
}
