import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, ElementRef, OnInit, Renderer2, ViewChild, } from '@angular/core';
import {Router, RouterLink, RouterOutlet} from '@angular/router';
import { AngularSvgIconModule } from 'angular-svg-icon';
import { NavigationHeaderComponent } from './navigation-header/navigation-header.component';
import { TranslateModule } from '@ngx-translate/core';
import { SideNavbarComponent } from '../side-navbar/side-navbar.component';
import {AppConfigService} from "../../shared/app-config.service";

@Component({
  selector: 'app-app-layout',
  standalone: true,
  imports: [
    CommonModule, RouterOutlet, AngularSvgIconModule,
    SideNavbarComponent, TranslateModule, NavigationHeaderComponent, RouterLink
  ],
  providers: [],
  templateUrl: './app-layout.component.html',
  styleUrls: ['./app-layout.component.scss'],
})
export class AppLayoutComponent implements OnInit, AfterViewInit {
  // toggle menu in mobile
  menustatus: boolean = false;
  handleToggleMobileSidebar() {
    this.menustatus = !this.menustatus;
    this.toggleMenu();
  }
  @ViewChild('toggleButton') elementRef1: any;
  @ViewChild('sideNav') elementRef2: any;
  @ViewChild('overlay') elementRef3: any;
  @ViewChild('closeButton') elementRef4: any;

  ngOnInit(): void { }
  // ngAfterViewInit(): void { }

  e() {
    (this.elementRef2.nativeElement.style.width = '0'),
      this.elementRef2?.nativeElement?.classList.remove('d-xxl-flex'),
      this.elementRef2?.nativeElement?.classList.add('d-none', 'closed'),
      (this.elementRef3.nativeElement.style.opacity = 0),
      setTimeout(function () {
        // this.elementRef3.nativeElement.style.display = 'none';
      }, 300);
  }

  logout() {
    localStorage.clear();
  }

  // handleToggleMobileSidebar() {
  //   console.log(this.elementRef2?.nativeElement?.classList);
  //   this.elementRef2?.nativeElement?.classList?.contains('d-none')
  //     ? (this.elementRef2?.nativeElemet?.setAttribute('style', 'width:12rem'),
  //       this.elementRef2.nativeElemet?.classList.remove('d-none'),
  //       this.elementRef2.nativeElemet?.classList.add('d-xxl-flex'),
  //       this.elementRef3?.nativeElemet?.setAttribute('style', 'display:block'),
  //       this.elementRef2?.nativeElemet?.classList.remove('closed'),
  //       this.elementRef2?.nativeElemet?.offsetWidth,
  //       this.elementRef3?.nativeElemet?.setAttribute('style', 'opacity:1'))
  //     : console.log('ffefm');
  // }


  constructor(private el: ElementRef, private renderer: Renderer2) {
  }
  // toggle the menu when click on any menu item, works only in small screens
  toggleMenu(){
    if (window.innerWidth < 1000) {
      const body = document.getElementsByTagName('body')[0];
      body.classList.toggle('nav-open');
    }
  }

  // call the function after the view Initiated
  ngAfterViewInit() {
    this.addClickEventToNavLinks();
  }

  // add eventlistner to the menu links to call the toggleMenu function
  addClickEventToNavLinks() {
    const navLinks = this.el.nativeElement.querySelectorAll('.nav-link');
    navLinks.forEach((link: any) => {
      this.renderer.listen(link, 'click', () => {
        this.toggleMenu();
      });
    });
  }
}
