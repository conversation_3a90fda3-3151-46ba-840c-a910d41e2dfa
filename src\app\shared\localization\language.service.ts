import { Inject, Injectable, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Environment } from '../models/environment';
import { KeyValuePair, StorageService } from '../services/storage/storage.service';

@Injectable({
  providedIn: 'root'
})
export class LanguageService implements OnInit {
  public readonly defaultLanguage: string = 'ar';

  private _onLanguageChanges: BehaviorSubject<string | null> = new BehaviorSubject<string | null>(null);

  private rtlLangs = ['ar'];
  private html = document.getElementsByTagName('html')[0];
  public dir = 'rtl';

  constructor(private _translateService: TranslateService, private storageService: StorageService, @Inject(Environment) private environment: Environment) { }

  ngOnInit(): void {
  }


  onLanguageChanges(): Observable<string | null> {
    return this._onLanguageChanges.asObservable();
  }

  getLanguage(): string | null {
    const lang = this.storageService.getItem('lang')
    return lang ?? this.defaultLanguage ?? null;
  }

  setLang(lang: string | null) {
    if (lang === null)
      return;

    this.storageService.setItem({ key: 'lang', value: lang } as KeyValuePair);
    this._translateService.use(lang);
    this.setAttribute(lang);
    this._onLanguageChanges.next(lang);
  }

  setLanguage(): void {
    const currentLang = this.getLanguage();
    const $lang = currentLang ?? this.defaultLanguage;
    this.setLang($lang);
  }

  private setAttribute(lang: string | null) {

    if (lang != null) {
      this.dir = this.rtlLangs.indexOf(lang) !== -1 ? 'rtl' : 'ltr';
      this.html.setAttribute('lang', lang);
    }
    this.html.setAttribute('dir', this.dir);
  }

  hasTranslation(key: string): boolean {
    const translation = this._translateService.instant(key);
    return translation !== key && translation !== '';
  }
  get currentLang() {
    return this._translateService.currentLang
  }

  /**
   * Translate based on passed the key
   * @param key translation key
   * @returns translation result based on the selected language
   */
  translation(key: string): Promise<string> {
    return this._translateService.get(key).toPromise();
  }
}
