import { AbstractControl, FormControl, FormGroup, ValidatorFn, Validators } from "@angular/forms";
//import { environment } from "src/environments/environment";
import { BaseForm } from "./base-form.model";
import { DevLog } from "../log-functions";

export class CustomFormGroup<TBaseFrom extends BaseForm> {
  private _name!: string;
  private _controls!: TBaseFrom;

  private _group!: FormGroup;
  public get group(): FormGroup {
    return this._group;
  }
  public set group(v: FormGroup) {
    this._group = v;
  }

  get FormControls() {
    return this._group.controls;
  }

  get Controls() {
    return this._controls;
  }

  constructor(name: string, controls: TBaseFrom) {
    this._name = name;
    this._controls = controls;
    this._group = controls.form;
  }

  addGroup(parentGroup: FormGroup) {
    parentGroup.removeControl(this._name);
    parentGroup.addControl(this._name, this._group);
    return this;
  }

  removeGroup() {
    var parentGroup = this._group.parent as FormGroup<any> | null;
    if (parentGroup) {
      parentGroup.removeControl(this._name);
      parentGroup.updateValueAndValidity();
    }
  }

  removeControl(key: string, defaultValue: unknown = null) {
    if (!this.isFormControlExist(key)) return;
    if (defaultValue) {
      this._group?.get(key)?.setValue(defaultValue);
    }

    this._group?.get(key)?.setValidators(null);
    this._group.get(key)?.disable();

    this._group.get(key)?.updateValueAndValidity();
    return this;
  }

  addControl(key: string) {
    if (!this.isFormControlExist(key)) return;

    const control = this._controls.form.controls[key];
    this._group.addControl(key, control);
    return this;
  }


  private isFormControlExist(key: string) {
    if (!this._controls || !this._controls.form.controls[key]) {
      DevLog.Log('isFormControlExist:' + key + 'formControl is undefine');
      return false;
    }
    return true
  }

}

export class CustomFormControl {
  private _formControl!: FormControl;
  private _key!: string;
  private _value!: any;
  private _validators!: Validators[];

  private _form !: FormGroup;
  private get form(): FormGroup {
    return this._form;
  }

  public setForm(v: FormGroup) {
    this._form = v;
    this.addControl(this.form);
    return this;
  }

  constructor(key: string, validators: Validators[] = [], value: any = null) {
    this._key = key
    this._value = value;
    this._validators = validators;
    this._formControl = new FormControl();
  }

  private addControl(f: FormGroup) {
    if (this._value)
      this._formControl.setValue(this._value);
    this._formControl.setValidators(this._validators as any);
    f.setControl(this.key, this._formControl);
  }

  private get isFormControlExist() {
    if (!this.form || !this.form?.get(this._key)) {
      DevLog.Error('isFormControlExist:' + this._key + 'formControl is undefine');
      return false;
    }
    return true
  }

  public get key(): string {
    return this._key;
  }

  public get value(): any {
    return this.formControl.value;
  }

  get formControl() {
    if (this._formControl.value == '') {
      this._formControl.setValue(null);
    }
    return this._formControl;
  }

  /**
   * By using this function can add form control
   * @param useDefaultValidation is true by default to load previous validation, or set to false in order to overwrite validation
   * @param validator is null by default, but you should pass array of validator to overwrite old init validation
   * @param defaultValue is generic value which is set to null when control is add
   * <AUTHOR> Aljohani
   * @example
   * (1) array of validators =>this.add([Validators.required, Validators.pattern('xxx')])
   * (2) this.add(); use default validation
   * (3) this.add(null,0)--> add control without validation and set its value by 0
   * (3) this.add(false,null,0)--> add control without validation and set its value by 0
   */
  add(validator: any = 'undefined', defaultValue: any = null) {
    if (!this.isFormControlExist) return;

    if (validator == 'undefined')
      validator = this._validators
    else if (!validator) validator = null;

    if (defaultValue && defaultValue !== null)
      this.form?.get(this._key)?.setValue(defaultValue);

    this.form?.get(this._key)?.setValidators(validator);
    this.form?.get(this._key)?.enable();

    this.form?.get(this._key)?.updateValueAndValidity();
  }

  /**
   * By using this function can remove form control
   * @param defaultValue is generic value which is set to null when control is add
   * <AUTHOR> Aljohani
   * @example
   * (1) this.remove(); not change previous value of control
   * (2) this.remove(null); to set value of control to null
   * (3) this.remove(0); set value of control to 0, you can pass any value.
   */

  remove(defaultValue: unknown = null) {
    if (!this.isFormControlExist) return;
    if (defaultValue) {
      this.form?.get(this._key)?.setValue(defaultValue);
    }

    this.form?.get(this._key)?.setValidators(null);
    this.form.get(this._key)?.disable();

    this.form.get(this._key)?.updateValueAndValidity();
  }
  /**
   * By using this function can remove or add formControl in one line
   * @param arg is optional object with two optional properties that set validation & default value of formControl.
   * @param isAddAction is boolean value which is set to 'true' to add formControl
   * @error validation is not considered in remove action
   * <AUTHOR> Aljohani
   * @example
   * (1) this.addOrRemove(true); if isAddAction is 'true' then formControl will be added otherwise removed
   * (2) this.addOrRemove(true,{defaultValue:'xx',validator:'cc'}); to overwrite validation and defaultValue of formControl
   * (3) this.addOrRemove(true,{defaultValue:null}); to set value of control to null when is added
   * (4) this.addOrRemove(true,{validator:[Validators.required]}); to overwrite validation of control when is added
   * (5) this.addOrRemove(false,{defaultValue:null}); to set value of control to null when removed
   * (6) this.addOrRemove(false); remove control from the form ,
   * @Note
   * (1) this.addOrRemove(false,{validator:[Validators.required]}); this is not correct.
   * (2) @param isAddAction value must be boolean such as:  isAddAction= ture;
   */
  addOrRemove(isAddAction: boolean, arg?: { validator?: any, defaultValue?: any }) {
    if (isAddAction == null || isAddAction == undefined) return;

    if (typeof isAddAction !== 'boolean')
      throw new Error("Dev Note(2): isAddAction value must be boolean such as:  isAddAction= ture");

    if (arg?.validator && !isAddAction)
      throw new Error("Dev Note(1): validation is not considered in removed action, please remove it");

    if (isAddAction)
      this.add(arg?.validator, arg?.defaultValue);
    else
      this.remove(arg?.defaultValue)
  }


  /**
 * By using this function can remove validators
 * @param validator is Validator that you need to remove
 * <AUTHOR> Altayyar
 * @example
 * (1) this.removeValidators(Validators.required); to remove required validator
 */
  removeValidators(validator: Validators) {
    this._validators = this._validators.filter(validators => validators != validator);
    this._formControl.setValidators(this._validators as any);
    this._formControl.updateValueAndValidity();
  }

  /**
* By using this function can remove validators
* @param validator is Validator that you need to remove
* <AUTHOR> Altayyar
*/
  removeAllValidators() {
    this._validators = [];
    this._formControl.clearValidators();
    this._formControl.updateValueAndValidity();
  }

  /**
* By using this function can add validators
* @param validator is Validator that you need to add
* <AUTHOR> Altayyar
* @example
* (1) this.addValidators(Validators.required); to remove required validator
*/
  addValidators(validator: Validators) {
    this._validators.push(validator);
    this._formControl.setValidators(this._validators as any);
    this._formControl.updateValueAndValidity();
  }

  removeAndReset() {
    this.remove();
    this.reset();
  }

  optional(defaultValue: unknown = null) {
    if (!this.isFormControlExist) return;
    if (defaultValue) {
      this.form?.get(this._key)?.setValue(defaultValue);
    }

    this.form?.get(this._key)?.setValidators(null);
    this.form.get(this._key)?.updateValueAndValidity();
  }


  public get hasValue() {
    return this._formControl?.value
  }

  public get isNotNull() {
    let val = this._formControl?.value;
    let checkWhiteSpace = typeof (val) == "string" ? (val?.trim() != '') : true
    return (val != null && val != "" && checkWhiteSpace)
  }

  public get isFalse() {
    return this._formControl?.value == false
  }

  public get isTrue() {
    return this._formControl?.value == true
  }

  compareTo(value: any) {
    if (!value || !this._formControl || !this._formControl?.value) return false;
    return (this._formControl?.value == value);
  }

  public get minRequiredLength() {
    return this.formControl.errors?.["minlength"]?.requiredLength
  }

  public get maxRequiredLength() {
    return this.formControl.errors?.["maxlength"]?.requiredLength
  }

  public get max() {
    return this.formControl.errors?.["max"]?.max;
  }

  public get min() {
    return this.formControl.errors?.["min"]?.min;
  }

  /**
   * @param validatorKey should be string as example
   * @example textEditorMaxLength.requiredLength
   * @example2 minlength?.requiredLength
   * @example3 minlength
   * @returns any or undefind
   *
   * The value should be the end of the validator path otherwise it will return as object
   */
  public getErrorValidationValue(validatorKey: string) {
    let value = this.formControl.errors;
    validatorKey.split('.').forEach(key => {
      value = value ? value[key] : null
    });
    return value;
  }

  reset() {
    this._formControl.reset();
  }

  setValue(value: any) {
    this._formControl.setValue(value);
  }

  get onValueChanges() {
    return this._formControl?.valueChanges
  }

  get isExist(): boolean {
    return this.form.contains(this._key);
  }

  markAsRequired() {
    this.reset()
    this._formControl.markAsDirty();
    this._formControl.markAsTouched();
  }

  addIfNotExist(validator: any = 'undefined', defaultValue = null) {
    if (!this.isExist)
      this.add(validator, defaultValue);
  }

  inValid() {
    if (this._formControl.invalid && this._formControl.touched) {
      return true;
    }
    return false;
  }
}
