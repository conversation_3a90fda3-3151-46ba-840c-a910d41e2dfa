import { CommonModule } from '@angular/common';
import { Component, Injector, OnInit } from '@angular/core';
import { BaseComponent } from '../../shared/base-component';
import { BaseTranslationKeys } from '../../shared/public.api';
import { TranslateModule } from '@ngx-translate/core';
import { AngularSvgIconModule } from 'angular-svg-icon';
import { RouterLink, RouterLinkActive } from '@angular/router';
import { AuthService } from '../../auth/auth.service';

@Component({
  selector: 'app-side-navbar',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    AngularSvgIconModule,
    RouterLinkActive,
    RouterLink,
  ],
  templateUrl: './side-navbar.component.html',
  styleUrl: './side-navbar.component.scss',
})
export class SideNavbarComponent extends BaseComponent implements OnInit {
  menustatus: boolean = false;

  handleToggleMobileSidebar() {
    this.menustatus = !this.menustatus;

    // Notify parent component about sidebar toggle
    const body = document.getElementsByTagName('body')[0];
    if (window.innerWidth < 768) {
      body.classList.toggle('nav-open');
    }
  }

  constructor(public authService: AuthService) {
    super(new BaseTranslationKeys('side_navbar', ''));
  }

  ngOnInit() {
    // Initialize sidebar state based on screen size
    this.menustatus = window.innerWidth >= 768;
    this.authService.checkIsAuthenticated.subscribe((isAuthenticated) => {
      // this.menustatus = isAuthenticated;
    });
  }
}
