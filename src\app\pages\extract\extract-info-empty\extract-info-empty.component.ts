import { Component, inject } from '@angular/core';
import { BASE_PATH } from '../../../services';
import { HttpClient } from '@angular/common/http';
import { UploadFileResult } from '../models/upload-file-result';
import { Router } from '@angular/router';

@Component({
  selector: 'app-extract-info-empty',
  templateUrl: './extract-info-empty.component.html',
  styleUrl: './extract-info-empty.component.scss',
})
export class ExtractInfoEmptyComponent {

  protected readonly httpClient = inject(HttpClient);
  protected readonly basePath = inject(BASE_PATH);
  protected readonly router = inject(Router);


  protected uploadFile(event: any) {
    const file = event.target.files[0];
    if (file) {
      // Check file type
      const allowedTypes = ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'];
      if (!allowedTypes.includes(file.type)) {
        console.log('Invalid file type. Please upload a word or txt file.', file.type);
        return;
      }
      const formData = new FormData();
      formData.append('file', file);
      this.httpClient.post<UploadFileResult>(this.basePath + '/main/upload', formData)
        .subscribe(response => {
          localStorage.setItem('file', JSON.stringify(response));
          this.router.navigate(['/search/semantic/result']);
        });

      // const reader = new FileReader();
      // reader.onload = (e: any) => {
      //   const blob = new Blob([e.target.result], { type: file.type });


      //   this.commonService.uploadFileMainUploadPost(blob).subscribe(response => {
      //     console.log(response);
      //   })
      // };
      // reader.readAsArrayBuffer(file);
    }
  }
}
