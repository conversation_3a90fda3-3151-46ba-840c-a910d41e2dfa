/**
 * MOSAED.Internal.Api
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { HighlightSectionEnum } from './highlight-section-enum';


export interface HighlightItem { 
    RefId?: string | null;
    CaseId?: string | null;
    SectionId: HighlightSectionEnum;
    StartOffset: number;
    EndOffset: number;
    Color?: string | null;
    SelectedText?: string | null;
    CreatedOn?: string | null;
}
export namespace HighlightItem {
}


