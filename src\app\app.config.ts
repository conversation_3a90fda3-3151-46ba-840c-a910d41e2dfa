import {
  APP_INITIALIZER,
  ApplicationConfig,
  importProvidersFrom,
  LOCALE_ID,
  ɵisEnvironmentProviders
} from '@angular/core';
import { provideRouter, withComponentInputBinding } from '@angular/router';

import { routes } from './app.routes';
import { HTTP_INTERCEPTORS, HttpBackend, HttpClient, HttpClientModule, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { AngularSvgIconModule } from 'angular-svg-icon';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { MultiTranslateHttpLoader } from "ngx-translate-multi-http-loader";
import { BASE_PATH } from './services';
import { AppConfigService, apiBaseUrlServiceFactory } from './shared/app-config.service';
import { BlockUIModule } from 'ng-block-ui';
import { BlockUIHttpModule } from 'ng-block-ui/http';
import {AbstractSecurityStorage, AuthModule as SSO_AuthModule, StsConfigLoader} from 'angular-auth-oidc-client';
import { loadOpenIdConfiguration } from './auth/factories/loadOidcConfiguration';
import { AppHttpInterceptorService } from './shared/services/appHttpInterceptor/app-http-interceptor.service';
import { MessageService } from 'primeng/api';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import {BrowserStorageService} from "./shared/services/storage/browser-storage.service";
import { registerLocaleData } from '@angular/common';
import localeAr from '@angular/common/locales/ar';

registerLocaleData(localeAr);
function translateFactory(http: HttpBackend) {
  return new MultiTranslateHttpLoader(http, [
    { prefix: "assets/i18n/", suffix: ".json" },
  ]);
}

function initializeApp(configService: AppConfigService) {
  return () => configService.loadAppConfig();
}


export const appConfig: ApplicationConfig = {
  providers: [
    provideAnimationsAsync(),
    provideRouter(routes, withComponentInputBinding()),
    provideHttpClient(withInterceptorsFromDi()),
    AngularSvgIconModule.forRoot().providers!,
    HttpClientModule,
    BlockUIModule.forRoot().providers!, // Import BlockUIModule
    BlockUIHttpModule.forRoot().providers!, // Import Block UI Http Module
    MessageService,
    {
      provide: APP_INITIALIZER,
      useFactory: (configService: AppConfigService) => () => configService.loadAppConfig(),
      deps: [AppConfigService],
      multi: true,
    },
    SSO_AuthModule.forRoot({
      loader: {
        provide: StsConfigLoader,
        useFactory: loadOpenIdConfiguration,
        deps: [AppConfigService],
      },
    }).providers!,
    TranslateModule.forRoot({
      defaultLanguage: 'ar',
      loader: {
        provide: TranslateLoader,
        useFactory: translateFactory,
        deps: [HttpBackend]
      }
    }).providers!,
    AppConfigService,
    {
      provide: APP_INITIALIZER,
      useFactory: initializeApp,
      deps: [AppConfigService],
      multi: true,
    },
    {
      provide: BASE_PATH,
      deps: [AppConfigService],
      useFactory: (appConfig: AppConfigService) => appConfig.apiBaseUrl
    },
    // {
    //   provide: HTTP_INTERCEPTORS,
    //   useClass: JwtInterceptor,
    //   multi: true,
    // },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AppHttpInterceptorService,
      multi: true
    },
    {
      provide: AbstractSecurityStorage,
      useClass: BrowserStorageService,
    },

    {
      provide: LOCALE_ID,
      useValue: 'ar'
    }
  ]
};
