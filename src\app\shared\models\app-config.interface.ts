export interface IAppConfig {
    apiBaseUrl: string;
    oidc: {
        authority: string,
        clientId: string,
        isMock: boolean,
    };
    globalLoader: {
        delayStart: number,
        delayStop: number
    };
    notification: {
        markAsReadPeriodInSeconds: number;
    },
    useByPass: boolean
}

export interface BackendConfigResponse {
  data: Omit<IAppConfig, 'apiBaseUrl'>;
  build: string;
  isSuccess: boolean;
  error: any;
}
