import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import {
  SearchByQuestionCommandResultPortalResponse,
  SemanticService,
} from '../../../../../services';

@Component({
  selector: 'app-semantic-search-landing-text',
  templateUrl: './semantic-search-landing-text.component.html',
  styleUrls: ['./semantic-search-landing-text.component.scss'],
})
export class SemanticSearchLandingTextComponent implements OnInit {
  question: string = '';
  keywordCount: number = 0;
  searchType: string = 'law'; // Default to law search
  isQuestionValid: boolean = false;

  // Side panel properties
  showSearchPanel: boolean = false;
  showQuestionPanel: boolean = false;
  isSearchResultVisible: boolean = false;
  searchResults: any[] = [];
  selectedLawDetails: any = null;
  selectedCaseDetails: any = null;
  loading: boolean = false;

  constructor(
    private router: Router,
    private semanticService: SemanticService
  ) {}

  ngOnInit(): void {
    // Check if returning from results page
    const isReturning = localStorage.getItem('isReturning') === 'true';
    if (isReturning) {
      this.question = localStorage.getItem('searchQuestion') || '';
      this.searchType = localStorage.getItem('searchType') || 'law';
      this.keywordCount = this.question.trim()
        ? this.question.trim().split(/\s+/).length
        : 0;
      this.isQuestionValid = this.keywordCount > 0 && this.keywordCount <= 4500;

      // Clear the returning flag
      localStorage.setItem('isReturning', 'false');

      // If we have search results stored, show them
      const searchResponse = localStorage.getItem('search-response');
      if (searchResponse) {
        this.isSearchResultVisible = true;
        this.showSearchPanel = true;
      }
    }
  }

  onInput(event: any): void {
    const text = event.target.value;
    // Count words (split by whitespace)
    this.keywordCount = text.trim() ? text.trim().split(/\s+/).length : 0;
    // Validate question
    this.isQuestionValid = this.keywordCount > 0 && this.keywordCount <= 4500;
  }

  submitSearch(): void {
    if (!this.isQuestionValid) return;

    this.loading = true;

    // Store search data in localStorage for the result component
    localStorage.setItem('searchQuestion', this.question);
    localStorage.setItem('searchType', this.searchType);
    localStorage.setItem('isSearchByQuestion', 'true');

    // Call the semantic service to perform the search
    this.semanticService
      .searchByQuestion({
        text: this.question,
        searchSource: this.searchType === 'law' ? 0 : 1, // 0 for law, 1 for case
      })
      .subscribe({
        next: (response: SearchByQuestionCommandResultPortalResponse) => {
          this.loading = false;

          // Store the response for the search result list component
          localStorage.setItem(
            'search-response',
            JSON.stringify(response.data)
          );

          // Show the search results panel
          this.isSearchResultVisible = true;
          this.showSearchPanel = true;
          this.showQuestionPanel = true;
        },
        error: (error) => {
          this.loading = false;
          console.error('Search error:', error);
          // Handle error (show message, etc.)
        },
      });
  }

  performSearch(): void {
    // This method is called when switching tabs
    // We'll reuse the existing search with the new type
    if (this.question && this.isQuestionValid) {
      this.submitSearch();
    }
  }

  toggleSearchPanel(): void {
    this.showSearchPanel = !this.showSearchPanel;
  }

  hideSearchResults(): void {
    this.isSearchResultVisible = false;
  }

  onCaseSelected(caseDetails: any): void {
    this.selectedCaseDetails = caseDetails;
    this.selectedLawDetails = null;
  }

  onLawSelected(lawDetails: any): void {
    this.selectedCaseDetails = null;
    this.selectedLawDetails = lawDetails;
  }
}
