.search-container {
  position: relative;
  display: flex;
  min-height: calc(100vh - 150px);
}

/* Main Content Styles */
.main-content {
  flex: 1;
  padding: 20px;
  transition: margin-left 0.3s ease;
}

.main-content.panel-open {
  margin-left: 70vw;
}

.search-form-container {
  max-width: 800px;
  margin: 0 auto;
  display: block;
  transition: display 0.3s ease;
}

/* Hide search form when panel is open */
.main-content.panel-open .search-form-container {
  display: none;
}

.card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: none;
  overflow: hidden;
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #eaeaea;
  padding: 20px;
}

.card-body {
  padding: 30px;
}

.search-type-selector {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 20px;
}

.form-check-input {
  margin-left: 8px;
}

.custom-textarea {
  border-radius: 8px;
  border: 1px solid #ced4da;
  padding: 15px;
  font-size: 16px;
  direction: rtl;
  background-color: #f9f9f9;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.custom-textarea:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  background-color: #fff;
}

.word-count {
  font-size: 14px;
  color: #6c757d;
}

.search-btn {
  min-width: 120px;
  border-radius: 30px;
  padding: 10px 25px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.search-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.search-instructions {
  color: #6c757d;
  font-size: 14px;
  text-align: center;
}

/* Search Results Side Panel Styles */
.search-results-panel {
  position: fixed;
  top: 0;
  left: -70vw; /* Start off-screen */
  width: 70vw; /* 70% of viewport width */
  height: 100vh;
  background-color: #fff;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow-y: auto;
  transition: left 0.3s ease;
}

.search-results-panel.show-panel {
  left: 0; /* Show panel */
}

.search-results-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.search-tabs {
  display: flex;
  background-color: #f5f5f5;
  border-radius: 30px;
  padding: 5px;
}

.search-tab-btn {
  flex: 1;
  padding: 10px 15px;
  border: none;
  background: none;
  border-radius: 25px;
  font-weight: 600;
  font-size: 14px;
  color: #666;
  text-align: center;
  cursor: pointer;
}

.search-tab-btn.active {
  background-color: #2e6b59;
  color: white;
}

.search-results-count {
  padding: 20px;
  color: #555;
  font-size: 14px;
  line-height: 1.5;
}

.search-results-list {
  padding: 0 20px;
}

/* Make the toggle button stay visible even when panel is hidden */
.panel-toggle-btn {
  position: fixed; /* Change from absolute to fixed */
  top: 50%;
  left: 0; /* Align to left edge of screen */
  transform: translateY(-50%);
  width: 31px;
  height: 60px;
  background-color: #2e6b59;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  transition: left 0.3s ease; /* Add transition to match panel */
}

.panel-toggle-btn:hover {
  background-color: #1e4a3d;
}

/* Move button when panel is visible */
.search-results-panel.show-panel ~ .panel-toggle-btn,
.search-results-panel.show-panel .panel-toggle-btn {
  left: 70vw; /* Move button to right edge of panel */
}

/* Responsive styles */
@media (max-width: 768px) {
  .search-results-panel {
    width: 100%;
    left: -100%;
  }

  .search-results-panel.show-panel {
    left: 0;
  }

  .search-results-panel.show-panel ~ .panel-toggle-btn,
  .search-results-panel.show-panel .panel-toggle-btn {
    left: 100%; /* Move button to right edge of panel on mobile */
  }

  .main-content.panel-open {
    margin-left: 0;
  }
}
