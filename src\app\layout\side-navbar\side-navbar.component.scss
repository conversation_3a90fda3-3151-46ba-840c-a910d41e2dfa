.side-navbar {
  top: 0;
  right: 0;
  width: 250px;
  height: 100%;
  background-color: #2d4f43;
  z-index: 1030;
  overflow-y: auto;
  transition: all 0.3s ease;
}

.side-navbar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.menu-title {
  color: white;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  margin: 15px 10px 10px 10px;
  opacity: 0.5;
}
.side-navbar-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.side-navbar-item {
  a {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;

    &.active {
      background-color: #ffffff4d;
      color: white;
    }

    i {
      margin-left: 10px;
      width: 20px;
      text-align: center;
    }

    span {
      font-size: 14px;
    }
  }
}

.side-navbar-divider {
  height: 1px;
  background-color: rgba(255, 255, 255, 0.1);
  margin: 15px 0;
}

.side-navbar-info {
  padding: 0 20px 10px;

  span {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.5);
  }
}

.toggle-sidebar-btn {
  position: fixed;
  top: 15px;
  right: 15px;
  z-index: 1020;
  background-color: #2d4f43;
  color: white;
  border: none;
  border-radius: 4px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

// Remove hover effects
.side-navbar-item a:hover {
  background-color: transparent;
}

// RTL support
[dir="ltr"] {
  .side-navbar {
    right: auto;
    left: 0;
  }

  .toggle-sidebar-btn {
    right: auto;
    left: 15px;
  }

  .side-navbar-item a i {
    margin-left: 0;
    margin-right: 10px;
  }
}

// side navigation style
.main-nav.side-nav {
  padding: var(--spacing-xl, 16px) var(--spacing-xl, 16px)
    var(--spacing-8xl, 80px) var(--spacing-xl, 16px);

  ul.nav {
    flex-direction: column;
  }
  li {
    .side-nav-link {
      color: #f4f4f6;
      border-radius: var(--radius-sm, 4px);
      gap: var(--spacing-md, 8px);
      padding: var(--spacing-md, 8px) var(--spacing-xl, 16px);
      font-size: 16px;
      line-height: 24px;
      font-weight: 500;
      position: relative;
      &:before {
        content: "";
        position: absolute;
        inset-inline-start: 0px;
        height: 24px;
        width: 6px;
        border-radius: var(--radius-full, 9999px);
        background: var(--background-neutral-400, #ffffff);
        opacity: 0;
        transition: all 0.5s ease-in-out;
      }
      &:hover {
        border-radius: var(--radius-sm, 4px);
        background: #ffffff4d;
        &:before {
          opacity: 1;
        }
      }
      &:focus {
        background-color: transparent;
      }

      &.disabled,
      &:disabled {
        cursor: default;
        color: var(--text-default-disabled, #9da4ae);
      }
      &.active {
        color: var(--background-primary, #ffffff);
        background: var(--button-background-neutral-selected, #ffffff4d);
        font-weight: 600;
        border-radius: var(--radius-sm, 4px);
        &:before {
          opacity: 1;
          background: var(--background-primary, #ffffff);
        }
      }
      &.dropdown-toggle {
        cursor: pointer;
        &:after {
          border: 0;
          width: 14px;
          height: 14px;
          background-size: cover;
          transition: all 0.3s ease-in-out;
        }
        &.dropdown-toggle-active {
          font-weight: 600;
          &:after {
            transform: rotate(180deg);
          }
        }
      }
    }
    &:last-child,
    &:last-child a {
      border: 0;
    }
  }
  .nav-submenu {
    li {
      border: 0;
    }
    li a {
      border: 0;
      padding-inline-start: 2.5rem;
    }
  }
}
