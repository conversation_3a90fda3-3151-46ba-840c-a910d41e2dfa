<!-- Always visible search input -->
<div class="chat-trigger-container" *ngIf="!isOverlayVisible">
  <div class="d-flex align-items-center p-2 border rounded shadow-sm bg-white">
    <input
      #triggerInput
      type="text"
      class="form-control me-2"
      placeholder="البدء بالمحادثة"
      [(ngModel)]="newMessage"
      (focus)="onInputFocus()"
      (input)="onInputChange()"
      (keyup.enter)="sendMessage()"
    />
    <button class="btn btn-primary send-button" (click)="sendMessage()">
      إرسال
    </button>
  </div>
</div>

<p-overlayPanel
  #chatOverlay
  styleClass="shadow-lg"
  [dismissable]="!embedded"
  (onShow)="onOverlayShow()"
  (onHide)="onOverlayHide()"
  [style]="
    embedded
      ? { width: '100%', 'max-width': '100%', padding: '0px' }
      : { width: '634px', 'max-width': '95%', padding: '0px' }
  "
>
  <div class="d-flex flex-column align-content-between p-2">
    <div
      *ngIf="!embedded"
      class="card-header d-flex justify-content-between align-items-center bg-white border-0 px-4 pb-3"
    >
      <p class="m-0 text-primary" style="font-size: 38px; font-weight: 500">
        <svg
          width="31"
          height="44"
          viewBox="0 0 22 18"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M9.78125 12.3438V13.9375V14.4688L12.1719 12.6758L12.6035 12.3438H13.1348H16.6875C16.9531 12.3438 17.2188 12.1113 17.2188 11.8125V6.5V2.25C17.2188 1.98438 16.9531 1.71875 16.6875 1.71875H6.0625C5.76367 1.71875 5.53125 1.98438 5.53125 2.25V6.5V11.8125C5.53125 12.1113 5.76367 12.3438 6.0625 12.3438H8.1875H9.78125ZM3.9375 7.03125H2.70898C2.54297 7.36328 2.17773 7.5625 1.8125 7.5625C1.21484 7.5625 0.75 7.09766 0.75 6.5C0.75 5.93555 1.21484 5.4375 1.8125 5.4375C2.17773 5.4375 2.54297 5.66992 2.70898 5.96875H3.9375V2.25C3.9375 1.08789 4.86719 0.125 6.0625 0.125H16.6875C17.8496 0.125 18.8125 1.08789 18.8125 2.25V5.96875H20.0078C20.1738 5.66992 20.5391 5.4375 20.9375 5.4375C21.502 5.4375 22 5.93555 22 6.5C22 7.09766 21.502 7.5625 20.9375 7.5625C20.5391 7.5625 20.1738 7.36328 20.0078 7.03125H18.8125V11.8125C18.8125 13.0078 17.8496 13.9375 16.6875 13.9375H13.1348L9.78125 16.4609L9.74805 16.4941L9.58203 16.5938L9.01758 17.0254C8.85156 17.1582 8.65234 17.1582 8.45312 17.0918C8.28711 16.9922 8.1875 16.8262 8.1875 16.5938V15.8965V15.6973V15.6641V15.5312V13.9375H6.59375H6.0625C4.86719 13.9375 3.9375 13.0078 3.9375 11.8125V7.03125ZM6.59375 4.375C6.59375 3.81055 7.05859 3.3125 7.65625 3.3125H15.0938C15.6582 3.3125 16.1562 3.81055 16.1562 4.375V8.625C16.1562 9.22266 15.6582 9.6875 15.0938 9.6875H7.65625C7.05859 9.6875 6.59375 9.22266 6.59375 8.625V4.375ZM7.65625 6.5C7.65625 7.09766 8.12109 7.5625 8.71875 7.5625C9.2832 7.5625 9.78125 7.09766 9.78125 6.5C9.78125 5.93555 9.2832 5.4375 8.71875 5.4375C8.12109 5.4375 7.65625 5.93555 7.65625 6.5ZM14.0312 5.4375C13.4336 5.4375 12.9688 5.93555 12.9688 6.5C12.9688 7.09766 13.4336 7.5625 14.0312 7.5625C14.5957 7.5625 15.0938 7.09766 15.0938 6.5C15.0938 5.93555 14.5957 5.4375 14.0312 5.4375Z"
            fill="#488371"
          />
        </svg>
        المحادثة
      </p>
      <div class="d-flex gap-4">
        <span style="cursor: pointer" (click)="chatOverlay.hide()">
          إخفاء
        </span>
        <div class="d-flex align-items-center gap-2">
          <svg
            width="19"
            height="17"
            viewBox="0 0 19 17"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M17.7812 2.28125L11.5312 8.53125L12.75 9.75C12.9062 9.9375 13 10.125 13 10.375C13 10.75 12.75 11.0938 12.375 11.1875L10.625 11.6875V11.7188C10.8438 13.1562 10.375 14.625 9.34375 15.6562C8.46875 16.5312 7.3125 17 6.09375 17H0.6875C0.3125 17 0 16.6875 0 16.3125C0 16.125 0.0625 15.9375 0.21875 15.8125L1.78125 14.375L2.53125 13.6562L3.25 13H2.53125C2.21875 13 2 12.7812 2 12.4688C2 12.3438 2 12.2188 2 12.0938C2.09375 11.3125 2.4375 10.5625 3.03125 9.96875L3.34375 9.65625C4.375 8.625 5.84375 8.15625 7.28125 8.375H7.3125L7.8125 6.625C7.90625 6.28125 8.25 6 8.625 6C8.84375 6 9.0625 6.09375 9.25 6.25L10.4688 7.46875L16.7188 1.25C17 0.9375 17.4688 0.9375 17.7812 1.25C18.0625 1.53125 18.0625 2 17.7812 2.28125ZM8.6875 8.96875L10.0312 10.3125L10.9062 10.0625L8.9375 8.09375L8.6875 8.96875ZM7.0625 9.84375C6.09375 9.6875 5.09375 10.0312 4.40625 10.7188L4.09375 11.0312C3.90625 11.2188 3.78125 11.375 3.6875 11.5938C4.125 11.7188 4.46875 12.0312 4.625 12.4688C4.84375 13.0312 4.71875 13.6875 4.25 14.125L2.75 15.5H6.0625C6.90625 15.5 7.6875 15.1875 8.28125 14.5938C8.96875 13.9062 9.3125 12.9062 9.15625 11.9375L9.09375 11.625L7.375 9.90625L7.0625 9.84375Z"
              fill="#5E5E5E"
            />
          </svg>
          <span style="cursor: pointer" (click)="clearMessages()">مسح</span>
        </div>
      </div>
    </div>

    <div
      #scrollContainer
      class="d-flex flex-column p-2 scrollbar-style"
      [ngClass]="{ 'embedded-chat-container': embedded }"
    >
      <div *ngFor="let group of groupedChatHistory">
        <h6 class="text-muted px-2 mt-3">{{ group.label }}</h6>
        <div
          *ngFor="let entry of group.entries"
          class="my-2 text-start"
          [ngClass]="{
            'justify-content-end': entry.role === 'user',
            'justify-content-start': entry.role === 'assistant'
          }"
        >
          <ng-container [ngSwitch]="entry.role">
            <div *ngSwitchCase="'user'" class="d-flex align-items-center">
              <svg
                class="mx-3"
                width="21"
                height="34"
                viewBox="0 0 16 19"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M10.9375 4.75C10.9375 3.76562 10.375 2.85156 9.53125 2.32422C8.65234 1.83203 7.5625 1.83203 6.71875 2.32422C5.83984 2.85156 5.3125 3.76562 5.3125 4.75C5.3125 5.76953 5.83984 6.68359 6.71875 7.21094C7.5625 7.70312 8.65234 7.70312 9.53125 7.21094C10.375 6.68359 10.9375 5.76953 10.9375 4.75ZM3.625 4.75C3.625 3.16797 4.46875 1.69141 5.875 0.882812C7.24609 0.0742188 8.96875 0.0742188 10.375 0.882812C11.7461 1.69141 12.625 3.16797 12.625 4.75C12.625 6.36719 11.7461 7.84375 10.375 8.65234C8.96875 9.46094 7.24609 9.46094 5.875 8.65234C4.46875 7.84375 3.625 6.36719 3.625 4.75ZM1.97266 16.5625H14.2422C13.9258 14.3477 12.0273 12.625 9.70703 12.625H6.50781C4.1875 12.625 2.28906 14.3477 1.97266 16.5625ZM0.25 17.2305C0.25 13.75 3.02734 10.9375 6.50781 10.9375H9.70703C13.1875 10.9375 16 13.75 16 17.2305C16 17.793 15.5078 18.25 14.9453 18.25H1.26953C0.707031 18.25 0.25 17.793 0.25 17.2305Z"
                  fill="#5E5E5E"
                />
              </svg>

              <div class="user-bubble">
                {{ entry.content }}
              </div>
            </div>

            <div *ngSwitchCase="'assistant'" class="d-flex align-items-center">
              <!-- <div class="assistant-bubble" [innerHTML]="(entry.content ?? '') | markdown"></div> -->

              <div class="assistant-bubble">
                <div
                  class="markdown-body"
                  [innerHTML]="entry.content ?? '' | markdown"
                ></div>
              </div>

              <svg
                class="mx-3"
                width="31"
                height="45"
                viewBox="0 0 22 18"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M9.78125 12.3438V13.9375V14.4688L12.1719 12.6758L12.6035 12.3438H13.1348H16.6875C16.9531 12.3438 17.2188 12.1113 17.2188 11.8125V6.5V2.25C17.2188 1.98438 16.9531 1.71875 16.6875 1.71875H6.0625C5.76367 1.71875 5.53125 1.98438 5.53125 2.25V6.5V11.8125C5.53125 12.1113 5.76367 12.3438 6.0625 12.3438H8.1875H9.78125ZM3.9375 7.03125H2.70898C2.54297 7.36328 2.17773 7.5625 1.8125 7.5625C1.21484 7.5625 0.75 7.09766 0.75 6.5C0.75 5.93555 1.21484 5.4375 1.8125 5.4375C2.17773 5.4375 2.54297 5.66992 2.70898 5.96875H3.9375V2.25C3.9375 1.08789 4.86719 0.125 6.0625 0.125H16.6875C17.8496 0.125 18.8125 1.08789 18.8125 2.25V5.96875H20.0078C20.1738 5.66992 20.5391 5.4375 20.9375 5.4375C21.502 5.4375 22 5.93555 22 6.5C22 7.09766 21.502 7.5625 20.9375 7.5625C20.5391 7.5625 20.1738 7.36328 20.0078 7.03125H18.8125V11.8125C18.8125 13.0078 17.8496 13.9375 16.6875 13.9375H13.1348L9.78125 16.4609L9.74805 16.4941L9.58203 16.5938L9.01758 17.0254C8.85156 17.1582 8.65234 17.1582 8.45312 17.0918C8.28711 16.9922 8.1875 16.8262 8.1875 16.5938V15.8965V15.6973V15.6641V15.5312V13.9375H6.59375H6.0625C4.86719 13.9375 3.9375 13.0078 3.9375 11.8125V7.03125ZM6.59375 4.375C6.59375 3.81055 7.05859 3.3125 7.65625 3.3125H15.0938C15.6582 3.3125 16.1562 3.81055 16.1562 4.375V8.625C16.1562 9.22266 15.6582 9.6875 15.0938 9.6875H7.65625C7.05859 9.6875 6.59375 9.22266 6.59375 8.625V4.375ZM7.65625 6.5C7.65625 7.09766 8.12109 7.5625 8.71875 7.5625C9.2832 7.5625 9.78125 7.09766 9.78125 6.5C9.78125 5.93555 9.2832 5.4375 8.71875 5.4375C8.12109 5.4375 7.65625 5.93555 7.65625 6.5ZM14.0312 5.4375C13.4336 5.4375 12.9688 5.93555 12.9688 6.5C12.9688 7.09766 13.4336 7.5625 14.0312 7.5625C14.5957 7.5625 15.0938 7.09766 15.0938 6.5C15.0938 5.93555 14.5957 5.4375 14.0312 5.4375Z"
                  fill="#488371"
                />
              </svg>
            </div>

            <div *ngSwitchDefault class="alert p-2 w-100">
              {{ entry.content }}
            </div>
          </ng-container>
        </div>
      </div>
    </div>
    <div class="chat-input-container d-flex align-items-center p-2 border-top">
      <div class="d-flex align-items-center w-100">
        <input
          type="text"
          class="form-control me-2"
          placeholder="البدء بالمحادثة"
          [(ngModel)]="newMessage"
          (keyup.enter)="sendMessage()"
        />

        <button class="btn btn-primary send-button" (click)="sendMessage()">
          إرسال
        </button>
      </div>
    </div>
  </div>
</p-overlayPanel>
