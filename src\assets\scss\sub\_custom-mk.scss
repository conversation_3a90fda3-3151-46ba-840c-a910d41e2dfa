// here custom styles added by Mostafa
// $breadcrumb-divider: quote(">");
$breadcrumb-divider: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8'%3E%3Cpath d='M2.5 0L1 1.5 3.5 4 1 6.5 2.5 8l4-4-4-4z' fill='%236c757d'/%3E%3C/svg%3E");
:root{
    --primary-light: #00AE8D;
    --white: #fff;
} 

[dir=rtl] .breadcrumb-item + .breadcrumb-item::before{
    @extend .flip-ltr;
    content: $breadcrumb-divider !important;
}
.targate-icon{
    width: 130px;
}

@keyframes rotateAnimation {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.landing-page .targate:before{
    animation-name: rotateAnimation;
    animation-duration: 150s; /* Animation takes 2 seconds */
    animation-iteration-count: infinite; /* Repeat animation forever */
    animation-timing-function: linear;  /* Smooth rotation speed */
}
.targate-card{
    background: #ffffffc4;
    backdrop-filter: blur(8px);
    box-shadow: 0px 20px 66px #8686863B;
    transition: all 0.5s ease-in-out; 
   .d-block{
        transition: all 0.5s ease-in-out; 
        &:hover{
            background: #fafafa;
        }
   }
}
.landing-accordion{
    p-accordiontab{
        position: relative;
        --decor-color3: #fff; 
        &:before {
            content: "";
            background: var(--decor-color3);
            width: calc(100% - 65px);
            height: 100%;
            position: absolute;
            top: 0px;
            right: 32px;
            z-index: -1;
            box-shadow: 0px 0px 17px 4px rgba(147, 178, 172, 0.3607843137);
        }  
    }
    .p-accordion-tab{
        position: relative;
        --decor-color: #fff;
        margin-inline: 2rem;
        &:before,
        &:after {
            content: "";
            background: var(--decor-color);
            width: 60px;
            height: 60px;
            // transform: rotate(45deg);
            position: absolute;
            top: 14px;
            right: -30px;
            z-index: -1;
            // box-shadow: 0px 4px 26px rgba(147, 178, 172, 0.3607843137);
        }
        &:before { 
            transform: rotate(135deg);
            // left: -20px;
            right: -31px;
            background: var(--decor-color);
            width: 60px;
            height: 60px;
            top: 14px;
            box-shadow: 0px 0px 17px 4px rgba(147, 178, 172, 0.3607843137); 
        }   
        .p-accordion-header{
            position: relative;
            --decor-color2: #fff; 
            // &:before,
            // &:after {
            //     content: "";
            //     background: var(--decor-color2);
            //     width: 80px;
            //     height: 80px;
            //     transform: rotate(45deg);
            //     position: absolute;
            //     bottom: 0;
            //     right: 0;
            //     z-index: -1;
            //     box-shadow: 0px 4px 26px rgba(147, 178, 172, 0.3607843137); 
            // }
            // &:before { 
            //     transform: rotate(135deg); 
            //     left: 0;
            //     right: auto; 
            // }   
            &:before,
            &:after {
                content: "";
                background: var(--decor-color2);
                width: 60px;
                height: 60px;
                // transform: rotate(45deg);
                position: absolute;
                top: 14px;
                left: -30px;
                z-index: -1;
                // box-shadow: 0px 4px 26px rgba(147, 178, 172, 0.3607843137);
            }
            &:before { 
                transform: rotate(135deg);
                // left: -20px;
                left: -31px;
                background: var(--decor-color2);
                width: 60px;
                height: 60px;
                top: 14px;
                box-shadow: 0px 0px 17px 4px rgba(147, 178, 172, 0.3607843137); 
            }   
        }

    }
} 
@keyframes continuesAnimation {
    from { transform: translateX(0); }
    to { transform: translateX(1000px); }
}

.landing-page .FQA .faq-top-pattern:after{
    animation-name: continuesAnimation;
    animation-duration: 50s; /* Animation takes 2 seconds */
    animation-iteration-count: infinite; /* Repeat animation forever */
    animation-timing-function: linear;  /* Smooth rotation speed */
} 
.news-fluid{
    --container-width: 1470px;
    padding-inline-start: calc((100vw - var(--container-width)) / 2 - 25px); 
    @media (max-width: 1350px) {
        &{
            --container-width: 1240px;
        }
    }
    @media (max-width: 950px) {
        &{
            --container-width: 0;
        }
    }
} 
@keyframes basicAnimation {
    from { transform: translateX(-70px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}
// make header animation working only for landing page
.wrapper:has(.hero){ 
    .logo-animation{
        transition-duration: 0.5s;
        animation-delay: .1s;
        @extend .fadeInRight;
        @extend .animate;
    }
    .menu-animation{ 
        transition-duration: 0.5s;
        animation-delay: 0.6s;
        @extend .fadeInRight;
        @extend .animate;
    } 
    .side-header-animation{
        transition-duration: 0.5s;
        animation-delay: 0.9s;
        @extend .fadeInRight;
        @extend .animate;
    }
}
.bg-soft{
    background: transparent linear-gradient(359deg, #F5F0E7 0%, #FFFFFF 100%) 0% 0% no-repeat padding-box;
}