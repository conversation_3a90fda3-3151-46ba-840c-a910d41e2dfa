import { Component, OnInit } from '@angular/core';
import { RegularSearchBaseResult } from '../regular-search-base-result';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-regular-search-judicial-precedents-result',
  templateUrl: './regular-search-judicial-precedents-result.component.html',
  styleUrls: ['./regular-search-judicial-precedents-result.component.css']
})
export class RegularSearchJudicialPrecedentsResultComponent extends RegularSearchBaseResult<IRegularSearchJudicialPrecedents[]> implements OnInit {

  protected override getDataObservable(): Observable<IRegularSearchJudicialPrecedents[]> {
    return this._httpClient.get<IRegularSearchJudicialPrecedents[]>(this._basePath + "/document/judicial_precedents/" + this.name)
  }
}

interface IRegularSearchJudicialPrecedents {
  name: string;
  description: string;
  url: string;
  date: string;
  type: string;
  refNumber: string;
  court: string;
  id: string;
}