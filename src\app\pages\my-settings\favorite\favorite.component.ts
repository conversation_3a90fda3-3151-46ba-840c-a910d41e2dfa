import { Component, OnInit } from '@angular/core';
import { MySettingsBaseComponent } from '../my-settings-base.component';
import { FavoriteItem, FavoriteService, FavoriteTypeEnum } from '../../../services';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { FavoriteItemComponent } from '../favorite-item/favorite-item.component';
import { DialogModule } from 'primeng/dialog';

@Component({
  standalone: true,
  selector: 'app-favorite',
  templateUrl: './favorite.component.html',
  styleUrls: ['./favorite.component.css'],
  imports: [
    CommonModule,          // Needed for *ngIf, *ngFor
    FormsModule,           // Needed if you use [(ngModel)] anywhere
    FavoriteItemComponent , // Needed since you're using <app-favorite-item>
    DialogModule
    // Add any PrimeNG or other components you plan to use
  ]
})
export class FavoriteComponent extends MySettingsBaseComponent implements OnInit {
  displayDeleteConfirm: boolean = false;
  confirmingEntry: any = null;
  constructor(private favoriteService: FavoriteService, private router: Router) {
    super('favorite')
  }
  sectionNames = Object.values(Section);
  selectedSection: Section = Section.All;
  readonly stringLengthShown: number = 100;
  favorites: FavoriteItem[] = [];
  laws: FavoriteItem[] = [];
  cases: FavoriteItem[] = [];
  selectedEntry: FavoriteItem | null = null;
  ngOnInit() {
    this.queryFavorites();
  }

  queryFavorites() {
    this.favoriteService.getFavorites().subscribe({
      next: (response) => {
        this.favorites = response.data.favorites ?? [];
        this.laws = this.favorites.filter(item => item.TypeId === 2);
        this.cases = this.favorites.filter(item => item.TypeId === 1);
      },
      error: (err) => {
        console.error('Error querying chat:', err);
      }
    });
  }

  onFavoriteDeleted(deletedEntry: any) {
    this.queryFavorites();
  }

  Section = Section;
}


export enum Section {
  All = "الكل",
  Case = 'قضية',
  Law = 'قانون',
}
