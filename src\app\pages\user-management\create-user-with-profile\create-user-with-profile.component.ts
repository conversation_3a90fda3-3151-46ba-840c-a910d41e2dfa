import { Component, EventEmitter, inject, OnInit, Output } from '@angular/core';
import { BaseForm, CustomFormControl, EMAIL } from '../../../shared/public.api';
import { Validators } from '@angular/forms';
import { UserManagementBase } from '../user-management-base.component';
import { Router } from '@angular/router';
import { Role } from '../../../shared/enums/role.enum';
import { finalize } from 'rxjs';

@Component({
  selector: 'app-create-user-with-profile',
  templateUrl: './create-user-with-profile.component.html',
  styleUrls: ['./create-user-with-profile.component.css']
})
export class CreateUserWithProfileComponent extends UserManagementBase implements OnInit {

  protected controls = new Control();
  genders: any[] = [];
  roles: any[] = [];
  addUserInProgress = false;
  constructor() {
    super('createUserWithProfile')
  }


  protected showForm: boolean = false;
  @Output('onSave') onSave = new EventEmitter<boolean>(false);


  ngOnInit() {
    this.genders.push({ id: 1, label: "ذكر" });
    this.genders.push({ id: 3, label: "أنثى" });
    this.roles.push({ id: Role.ElmSupportAdmin, label: "مسؤول دعم Elm" });
  }


  back() {
    this._router.navigate(["/user-managements"])
  }

  protected submit() {

    this.controls.name.setValue(this.controls.name?.value?.trim());

    if (this.controls.isInvalid) {
      return;
    }

    this.addUserInProgress = true;
    const isActive = this.controls.isActive.value;

    let model = {
      firstName: this.controls.name.value,
      secondName: null,
      thirdName: null,
      familyName: null,
      gender: this.controls.gender.value,
      email: this.controls.email.value.trim(),
      nin: this.controls.nationalId.value,
      phoneNumber: this.controls.phoneNumber.value,
      roleCode: this.controls.roleCode.value,
      isActive: isActive == null ? false : isActive
    };

    this._userManagementService.createUserWithProfile(model)
      .pipe(finalize(() => {
        this.addUserInProgress = false;
      }))
      .subscribe({
        next: (response) => {
          if (response.isSuccess) {
            this.showForm = false;
            this.controls.form.reset();
            this._messageService.add({
              severity: 'success',
              summary: 'نجحت العملية',
              detail: 'تم إنشاء مستخدم جديد بنجاح'
            });
            this.back();
            
          }
        }
      });
  }
}


class Control extends BaseForm {
  public readonly name = new CustomFormControl('name', [Validators.required, Validators.minLength(2), Validators.maxLength(50), Validators.pattern(/^[a-zA-Z\u0621-\u064A\s]*$/)]).setForm(this.form);
  public readonly email = new CustomFormControl('email', [Validators.required, Validators.minLength(4), Validators.maxLength(200), Validators.pattern(EMAIL)]).setForm(this.form);
  public readonly isActive = new CustomFormControl('isActive', [], false).setForm(this.form);
  public readonly gender = new CustomFormControl('gender', [Validators.required], 1).setForm(this.form);
  public readonly nationalId = new CustomFormControl('nationalId', [Validators.required, Validators.minLength(10), Validators.maxLength(10), Validators.pattern(/^[0-9]/)]).setForm(this.form);
  public readonly phoneNumber = new CustomFormControl('phoneNumber', [Validators.required, Validators.minLength(9), Validators.maxLength(9), Validators.pattern(/^5\d{8}$/)]).setForm(this.form);
  public readonly roleCode = new CustomFormControl('roleCode', [Validators.required], Role.ElmSupportAdmin).setForm(this.form);
}
