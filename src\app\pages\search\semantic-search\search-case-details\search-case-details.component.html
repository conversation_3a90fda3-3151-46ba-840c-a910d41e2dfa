@if (caseDetails) {
<div dir="rtl" class="col-12 flex align-items-center justify-content-center h-100">
  <div class="d-block card main_height border-0 w-100 mb-4">

    <div class="card-body">
      <div class="d-flex align-items-center justify-content-between mb-3">
        <span class="p-input-icon-left">
          <i class="pi pi-search"></i>
          <input type="text" class="p-inputtext-sm form-control" pInputText placeholder="البحث .."
            [(ngModel)]="value" />
        </span>

        <div class="d-flex gap-2 ms-2">

          <!-- Keywords Dropdown -->
          <div class="dropdown">
            <button class="btn btn-outline-primary dropdown-toggle" type="button" id="keywordsDropdown"
              (click)="keyWordOverlay.toggle($event)">
              الكلمات
            </button>
            <p-overlayPanel [scrollTop]="true" #keyWordOverlay>
              <div class="dropdown-overlay-container">
                <div class="row g-2 row-cols-2">

                  <div class="col" *ngFor="let keyword of nerPredictWords">
                    <span class="text-primary">
                      - {{ keyword }}
                    </span>
                  </div>
                </div>
              </div>
            </p-overlayPanel>

          </div>
          <!-- NER Dropdown -->
          <div class="dropdown">
            <button class="btn btn-outline-primary dropdown-toggle" type="button" id="nerDropdown"
              (click)="entitiesOverlay.toggle($event)">
              الأعلام
            </button>
            <p-overlayPanel [scrollTop]="true" #entitiesOverlay>
              <div class="dropdown-overlay-container">
                <div class="row g-2 row-cols-2">

                  <div class="col" *ngFor="let item of nerList">
                    <span class="text-primary">
                      - {{ item }}
                    </span>
                  </div>
                </div>
              </div>
            </p-overlayPanel>

            <ul class="dropdown-menu" aria-labelledby="nerDropdown">
              <li *ngFor="let ner of nerList">
                <a class="dropdown-item">{{ ner }}</a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="card-body" style="background-color: #f4f4f6">
      <div class="d-inline-flex align-items-center gap-2 my-2">
        <button class="btn btn-sm btn-outline-primary rounded-5" [ngClass]="{'active': title === 'نص القضية'}"
          (click)="updateContent('نص القضية', caseDetails?.text)">
          نص القضية
        </button>
        <button class="btn btn-sm btn-outline-primary rounded-5" [ngClass]="{'active': title === 'قانون الجلسة'}"
          (click)="updateContent('قانون الجلسة', caseDetails?.rules)">
          قانون الجلسة
        </button>
        <button class="btn btn-sm btn-outline-primary rounded-5" [ngClass]="{'active': title === 'الحكم'}"
          (click)="updateContent('الحكم', caseDetails?.verdict)">
          الحكم
        </button>
      </div>
    </div>

    <div class="card-body">
      <div class="d-flex justify-content-between p-1">
        <h5 class="text-primary fw-bold">{{ title }}</h5>
      </div>
      <div class="mt-3 scroll-lg" id="case_text">
        {{ content }}
      </div>
    </div>
  </div>
</div>

} @else {
<!--<div class="alert alert-info">-->
<!--  <p>يرجى اختيار تفاصيل القضية من القائمة</p>-->
<!--</div>-->
}
