ARG REGISTRY
#### STAGE BUILD ###
FROM ${REGISTR<PERSON>}/node:20.9.0-buster as build
WORKDIR /code
ARG NPM_CONFIG_REGISTRY
ARG SASS_BINARY_SITE
COPY . .
RUN npm install --cache /cache/npm-cache --force --verbose --no-audit
RUN npm run build

#### STAGE DEV RUNTIME ###
FROM build as dev-runtime
CMD ["npm", "run", "start"]

#### STAGE RUNTIME ###
FROM ${REGISTRY}/nginxinc/nginx-unprivileged:1.28-alpine as runtime
COPY --from=build --chown=1001:0 /code/dist/judicial-assistant/browser /usr/share/nginx/html
USER 1001
EXPOSE 8080