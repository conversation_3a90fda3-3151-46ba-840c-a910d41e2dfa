<div>
  <p-table
    [value]="searchResult.data"
    dataKey="id"
    [paginator]="true"
    [rows]="defaultLimit"
    [totalRecords]="searchResult.total"
    [loading]="loading"
  >
    <ng-template pTemplate="body" let-item>
      @if(isSearchTypeByLaw) {
      <tr class="border-0">
        <td class="p-1">
          <div class="search-item-container p-1">
            <div class="p-1">
              <div class="my-2">
                <div
                  class="fw-bold d-flex justify-content-between align-items-center"
                >
                  <!-- System Name (right in RTL) -->
                  <span>
                    {{ item.systemName || "-" }}
                  </span>

                  <!-- State + Bookmark (left in RTL) -->
                  <div class="d-flex align-items-center gap-2">
                    <span class="badge bg-secondary p-2">
                      {{ item.state }}
                    </span>

                    <!-- <a
                      *ngIf="item.isFavorite"
                      class="nav-link list-group-item-action p-0"
                      href="javascript:void(0)"
                      (click)="removeFavorite(item)"
                    >
                      <i class="pi pi-bookmark-fill pe-1"></i>
                    </a>
                    <a
                      *ngIf="!item.isFavorite"
                      class="nav-link list-group-item-action p-0"
                      href="javascript:void(0)"
                      (click)="addToFavorite(item)"
                    >
                      <i class="pi pi-bookmark pe-1"></i>
                    </a> -->
                  </div>
                </div>
              </div>
              <div class="my-2">
                <label
                  class="fw-bold d-flex justify-content-between align-items-center"
                >
                  <span> ملخص النظام و القانون </span>
                </label>
                <div class="text-break" style="white-space: pre-line">
                  {{ item.shortContent || "-" }}
                </div>
              </div>
              <div class="row row-cols-2">
                <div class="col">
                  <label class="fw-bold">الباب</label>
                  <div class="text-break">{{ item.gate || "-" }}</div>
                </div>
                <div class="col">
                  <label class="fw-bold">المادة</label>
                  <div class="text-break">{{ item.article || "-" }}</div>
                </div>
              </div>
              <div class="row row-cols-2">
                <div class="col">
                  <label class="fw-bold">تاريخ الإنشاء</label>
                  <div>{{ item.dateOfCreation }}</div>
                </div>
                <div class="col">
                  <label class="fw-bold">تاريخ النشر</label>
                  <div>{{ item.dateOfPublication }}</div>
                </div>
              </div>
              <div
                class="d-flex justify-content-start align-items-center gap-4 my-3"
              >
                <button
                  class="btn btn-primary py-2 px-3 text-white"
                  (click)="viewLawDetails(item.id)"
                >
                  تفاصيل النظام و القانون
                </button>
                <div class="border rounded-1 py-2 px-3">
                  نسبة التشابه
                  {{ item.embeddingScore * 100 | number : "1.2-2" }}%
                </div>
              </div>
            </div>
          </div>
        </td>
      </tr>
      } @else {

      <tr>
        <td class="p-1">
          <div class="border-0 p-1">
            <div class="p-1">
              <div class="my-2 position-relative">
                <label class="fw-bold">نص القضية</label>
                <div class="position-absolute end-0 top-0">
                  <a
                    *ngIf="item.isFavorite"
                    class="nav-link list-group-item-action p-0"
                    href="javascript:void(0)"
                    (click)="removeFavorite(item)"
                  >
                    <i class="pi pi-bookmark-fill pe-1"></i>
                  </a>
                  <a
                    *ngIf="!item.isFavorite"
                    class="nav-link list-group-item-action p-0"
                    href="javascript:void(0)"
                    (click)="addToFavorite(item)"
                  >
                    <i class="pi pi-bookmark pe-1"></i>
                  </a>
                </div>
                <div
                  class="text-break text-justify"
                  style="white-space: pre-line"
                >
                  {{ item.shortCleanText }}
                </div>
              </div>
              <div class="row row-cols-3">
                <div class="col">
                  <label class="fw-bold">ر.القضية الابتدائية</label>
                  <div>{{ item.auditVerdictNumber | slice : 0 : 80 }}</div>
                </div>
                <div class="col">
                  <label class="fw-bold">نص الحكم</label>
                  <div>{{ item.verdict }}</div>
                </div>
                <div class="col">
                  <label class="fw-bold">تاريخ الحكم</label>
                  <div>{{ item.verdictDate }}</div>
                </div>
              </div>
              <div class="d-flex justify-content-end align-items-center my-3">
                <button
                  class="btn btn-outline-primary py-2 px-3 d-flex align-items-center"
                  (click)="openCaseFeedbackDialog(item)"
                >
                  الرأي بالقضية
                </button>
                <div
                  class="border rounded-1 py-2 px-3 bg-primary bg-opacity-25 mx-3"
                >
                  نسبة التشابه
                  {{ item.embeddingScore * 100 | number : "1.2-2" }}%
                </div>
                <button
                  class="btn btn-primary py-2 px-3 text-white"
                  (click)="viewCaseDetails(item.id)"
                >
                  تفاصيل القضية
                </button>
              </div>
            </div>
          </div>
        </td>
      </tr>
      }
    </ng-template>
  </p-table>
</div>

@if(selectedResult && showResult){
<div
  class="d-flex justify-content-between me-2 border-bottom align-items-center my-3 pb-4"
>
  <label class="fs-4 fw-bold">
    @if (isCaseDetailsVisible) { تفاصيل القضية } @if (isLawDetailsVisible) {
    تفاصيل الأنظمة و القوانين }
  </label>
  <button class="btn btn-outline-primary me" (click)="goBack()">
    العودة إلى نتائج البحث
  </button>
</div>

<app-case-details-viewer
  [selectedCase]="selectedResult"
  *ngIf="isCaseDetailsVisible"
/>
<app-law-details-viewer
  [selectedLaw]="selectedResult"
  *ngIf="isLawDetailsVisible"
/>
}

<p-dialog
  [(visible)]="showCaseFeedbackDialog"
  [modal]="true"
  [header]="'إبداء الرأي'"
  [style]="{ width: '500px' }"
>
  @if (!modifyCaseFeedback) {

  <p class="mb-4">
    هل تعتقد أن القضية مرتبطة بموضوع البحث بنسبة {{ selectedCasePercentage }}%؟
  </p>

  <div class="row row-cols-md-12">
    <div class="col d-flex justify-content-center">
      <button
        (click)="onSubmitCaseFeedback(null)"
        class="btn btn-primary mx-2 me-3"
      >
        تأكيد نسبة التشابه
      </button>

      <button
        (click)="modifyCaseFeedback = true"
        class="btn btn-outline-primary mx-2"
      >
        تعديل نسبة التشابه
      </button>
    </div>
  </div>

  } @else {
  <p class="mb-4">تعديل نسبة التشابه</p>

  <form
    class="row needs-validation"
    novalidate
    autocomplete="off"
    #form="ngForm"
    (ngSubmit)="onSubmitCaseFeedback(form)"
    [formGroup]="formGroup"
  >
    <div class="col-12 mb-3">
      <div class="d-flex justify-content-between py-2">
        <label for=""
          ><span>نسبة التشابه:</span
          ><span class="fw-bold ms-1">{{
            formGroup.get("feedbackPercentage")?.value
          }}</span>
        </label>
      </div>
      <div class="cutom-primeng pb-3" dir="rtl">
        <input
          type="range"
          formControlName="feedbackPercentage"
          class="form-range"
          min="1"
          max="100"
        />
      </div>
      <div
        class="text-danger"
        *ngIf="
          formGroup.get('feedbackPercentage')?.hasError('required') &&
          form.submitted
        "
      >
        التصنيف الرئيسي مطلوب
      </div>
    </div>

    <div class="col-12 mb-2">
      <textarea
        (keydown.enter)="onSubmitCaseFeedback(form)"
        formControlName="feedbackNotes"
        placeholder="السبب"
        cols="4"
        rows="4"
        id="note"
        name="note"
        class="form-control"
        type="email"
      ></textarea>
      <div
        class="text-danger"
        *ngIf="
          formGroup.get('feedbackNotes')?.hasError('required') && form.submitted
        "
      >
        سبب النسبة مطلوب
      </div>
    </div>

    <div class="row row-cols-md-12 mt-4">
      <div class="col d-flex justify-content-center">
        <button
          [loading]="submitCaseFeedbackInProgress"
          pButton
          type="submit"
          class="btn btn-primary mx-2 me-3"
        >
          ارسال
        </button>

        <button
          (click)="showCaseFeedbackDialog = false"
          class="btn btn-outline-primary mx-2"
        >
          الغاء
        </button>
      </div>
    </div>
  </form>
  }
</p-dialog>
