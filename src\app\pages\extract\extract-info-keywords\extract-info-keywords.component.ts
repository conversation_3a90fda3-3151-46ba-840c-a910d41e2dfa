import { Component, On<PERSON><PERSON>roy, OnInit, inject } from '@angular/core';
import { ExtractInfoEvents } from '../../../shared/events/extract.events';
//import { KWFeatures, KeywordsService } from '../../../services';
import { Subscription, take, timeout } from 'rxjs';
import {KeywordPredict} from "../extract-info-response.model";

@Component({
  selector: 'app-extract-info-keywords',
  templateUrl: './extract-info-keywords.component.html',
  styleUrls: ['./extract-info-keywords.component.css']
})
export class ExtractInfoKeywordsComponent implements OnInit, OnDestroy {

  //private readonly keywordsService = inject(KeywordsService);
  /**
   * @description النتيجة
   */
  protected data: KeywordPredict = { keywords: [], grouped_keywords: [] };
  protected subscription!: Subscription

  constructor() { }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  ngOnInit() {
    ExtractInfoEvents.InfoExtracted.getListener()
      .pipe(take(1))
      .subscribe(events => this.loadData(events!));
  }

  private loadData(text: string) {
    // this.keywordsService.predictKeywordsPredictPost({
    //   case_type: 'bog',
    //   text: text!
    // })
    //   .pipe(timeout(70_000))
    //   .subscribe(response => {
    //     this.data = response
    //   })
    const savedData = localStorage.getItem('file_keywords');

    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData);
        this.data = parsedData || { keywords: [], grouped_keywords: [] };
      } catch (error) {
        this.data = { keywords: [], grouped_keywords: [] };
      }
    } else {
      this.data = { keywords: [], grouped_keywords: [] };
    }
  }

  formatResult() {
    return this.data.grouped_keywords.map(keyword => keyword);
  }

}
