import { ILookupDetailsResult } from "../shared/public.api";


/**
 * See https://wiki.elm.sa/requirements/BJS/BOG-GAP-Roles-002
 */
namespace AccountManagerPermissions {
    export namespace ReportingAndDashboard {
        /**
         * خدمة المؤشرات - مؤشر عدد دعاوى
         * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-AC-019"/>
         */
        export const CasesIndicators: number = 20_19;

        /**
         * خدمة التقارير - تقرير سجل الأحكام
         * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-AC-020"/>
         */
        export const JudgmentRecordReport: number = 20_20;

        /**
         * خدمة التقارير -تقرير سجل مواعيد الدائرة
         * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-AC-021"/>
         */
        export const JudicialCircuitScheduledAppointmentsReport: number = 20_21;

        /**
         * تقرير سجل الدعاوى التي لا تزال بفترة الاعتراض
         * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-AC-022"/>
         */
        export const PendingAppealsRegistryReport: number = 20_22;

        /**
         * الاطلاع على الأحكام
         * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-AC-023"/>
         */
        export const ViewJudgments: number = 20_23;

        /**
         * تسجيلات الدخول
         * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-AC-024"/>
         */
        export const Logins: number = 20_24;

        /**
         * التبليغات
         * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-AC-025"/>
         */
        export const Notifications: number = 20_25;

        /**
         * متابعة أعمال ممثلي الجهة
         * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-AC-026"/>
         */
        export const MonitoringRepresentativesActivities: number = 20_26;
    }
    export namespace ManageAccountManagers {
        /**
         * Add an account manager - Lower Levels
         * See https://wiki.elm.sa/requirements/BJS/BOG-GAP-Permissions-AC-001
         */
        export const AddAccountManager_LowerLevels: number = 2001;

        /**
         * Edit an account manager - Lower Levels
         * See https://wiki.elm.sa/requirements/BJS/BOG-GAP-Permissions-AC-002
         */
        export const EditAccountManager_LowerLevels: number = 2002;

        /**
         * Activate / deactivate an account manager - Lower Levels
         * See https://wiki.elm.sa/requirements/BJS/BOG-GAP-Permissions-AC-003
         */
        export const ActivateDeactivateAccountManager_LowerLevels: number = 2003;

        /**
         * View account manager - same level
         */
        export const ViewAccountManager_SameLevels: number = 20_90;

        /**
         * View account manager - lower level
         */
        export const ViewAccountManager_LowerLevels: number = 20_91;
    }

    export namespace Representative {
        /**
         * Add a representative - Same Level
         * See https://wiki.elm.sa/requirements/BJS/BOG-GAP-Permissions-AC-004
         */
        export const AddRepresentative_SameLevel: number = 2004;

        /**
         * Add a representative - Lower Levels
         * See https://wiki.elm.sa/requirements/BJS/BOG-GAP-Permissions-AC-005
         */
        export const AddRepresentative_LowerLevels: number = 2005;

        /**
         * Edit a representative - Same Level
         * See https://wiki.elm.sa/requirements/BJS/BOG-GAP-Permissions-AC-006
         */
        export const EditRepresentative_SameLevel: number = 2006;

        /**
         * Edit a representative - Lower Levels
         * See https://wiki.elm.sa/requirements/BJS/BOG-GAP-Permissions-AC-007
         */
        export const EditRepresentative_LowerLevels: number = 2007;

        /**
         * Activate / deactivate a representative - Same Level
         * See https://wiki.elm.sa/requirements/BJS/BOG-GAP-Permissions-AC-008
         */
        export const ActivateDeactivateRepresentative_SameLevel: number = 2008;

        /**
         * Activate / deactivate a representative - Lower Levels
         * See https://wiki.elm.sa/requirements/BJS/BOG-GAP-Permissions-AC-009
         */
        export const ActivateDeactivateRepresentative_LowerLevels: number = 2009;

        /**
         * View Representative - Same level
         * See https://wiki.elm.sa/requirements/BJS/BOG-GAP-Permissions-AC-010
         */
        export const ViewRepresentative_SameLevel: number = 2010;

        /**
         * View Representative - Lower levels
         * See https://wiki.elm.sa/requirements/BJS/BOG-GAP-Permissions-AC-011
         */
        export const ViewRepresentative_LowerLevels: number = 2011;

        /**
         * View Logs of Representative
         * See https://wiki.elm.sa/requirements/BJS/BOG-GAP-Permissions-AC-012
         */
        export const ViewLogsOfRepresentative: number = 2012;

        /**
         * Delegate representative - Same level
         * See https://wiki.elm.sa/requirements/BJS/BOG-GAP-Permissions-AC-013
         */
        export const DelegateRepresentative_SameLevel: number = 2013;

        /**
         * Delegate representative - Lower level
         * See https://wiki.elm.sa/requirements/BJS/BOG-GAP-Permissions-AC-014
         */
        export const DelegateRepresentative_LowerLevel: number = 2014;
    }

    export namespace General {
        /**
         * Dawai Service - خدمة دعاواي
         * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-AC-015"/>
         */
        export const MyCases: number = 20_15;

        /**
         * خدمة الاستعلام عن قضايا طرف
         * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-AC-016"/>
         */
        export const CaseParty: number = 20_16;

        /**
         * Manage Profile
         * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-AC-017"/>
         */
        export const ManageProfile: number = 20_17;

        /**
         * خدمة مواعيدي
         * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-AC-018"/>
         */
        export const MyAppointments: number = 20_18;
        /**
       * خدمة الاطلاع على الأحكام والقضايا
       * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-AC-023"/>
       */
        export const RullingCase: number = 20_23;
        /**
        *  خدمة التبليعات
        * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-AC-031"/>
        */
        export const SessionNotification: number = 20_31;

        /**
        *  سجل اجراءات المستخدمين
        * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-AC-032"/>
        */
        export const UserActionLogs: number = 20_32;
    }
}

/**
 * See https://wiki.elm.sa/requirements/BJS/BOG-GAP-Roles-001
 */
namespace AdminPermissions {
    export namespace AccountManager {
        /**
         * Add main account manager
         * See https://wiki.elm.sa/requirements/BJS/BOG-GAP-PERMISSIONS-ADMIN-001
         */
        export const AddMainAccountManager: number = 1001;

        /**
         * See https://wiki.elm.sa/requirements/BJS/BOG-GAP-PERMISSIONS-ADMIN-002
         */
        export const EditMainAccountManager: number = 1002;

        /**
         * See https://wiki.elm.sa/requirements/BJS/BOG-GAP-PERMISSIONS-ADMIN-003
         */
        export const ActivateDeactivateAccountManager: number = 1003;

        /**
         * See https://wiki.elm.sa/requirements/BJS/BOG-GAP-PERMISSIONS-ADMIN-004
         */
        export const ViewAccountManager_All_Levels: number = 1004;

        /**
         * See https://wiki.elm.sa/requirements/BJS/BOG-GAP-PERMISSIONS-ADMIN-005
         */
        export const ViewAccountManagers_logs: number = 1005;
    }

    export namespace Representative {
        /**
         * See https://wiki.elm.sa/requirements/BJS/BOG-GAP-PERMISSIONS-ADMIN-006
         */
        export const ViewRepresentative_AllLevels: number = 1006;

        /**
         * See https://wiki.elm.sa/requirements/BJS/BOG-GAP-PERMISSIONS-ADMIN-007
         */
        export const ViewRepresentative_Logs: number = 1007;
    }

    export namespace General {
        /**
         * See https://wiki.elm.sa/requirements/BJS/BOG-GAP-PERMISSIONS-ADMIN-008
         */
        export const ManageProfile: number = 1008;
    }

    export namespace ReportingAndDashboard {
        /**
         * خدمة المؤشرات - مؤشر عدد دعاوى
         * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-AC-019"/>
         */
        export const CasesIndicators: number = 10_19;
    }
}

/**
* See https://wiki.elm.sa/requirements/BJS/BOG-GAP-Roles-003
*/
namespace RepresentativePermissions {
    export namespace ReportingAndDashboard {
        /**
         * خدمة المؤشرات - مؤشر عدد دعاوى
         * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-LegalRep-004"/>
         */
        export const CasesIndicators: number = 30_04;

        /**
         * خدمة التقارير - تقرير سجل الأحكام
         * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-LegalRep-006"/>
         */
        export const JudgmentRecordReport: number = 30_06;

        /**
         * خدمة التقارير -تقرير سجل مواعيد الدائرة
         * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-LegalRep-007"/>
         */
        export const JudicialCircuitScheduledAppointmentsReport: number = 30_07;

        /**
         * تقرير سجل الدعاوى التي لا تزال بفترة الاعتراض
         * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-LegalRep-008"/>
         */
        export const PendingAppealsRegistryReport: number = 30_08;

        /**
         * الاطلاع على الأحكام
         * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-LegalRep-009"/>
         */
        export const ViewJudgments: number = 30_09;

        /**
         * تسجيلات الدخول
         * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-LegalRep-010"/>
         */
        export const Logins: number = 30_10;

        /**
         * التبليغات
         * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-AC-011"/>
         */
        export const Notifications: number = 30_11;

        /**
         * متابعة أعمال ممثلي الجهة
         * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-LegalRep-012"/>
         */
        export const MonitoringRepresentativesActivities: number = 30_12;
    }

    export namespace General {
        /**
         * Dawai Service - خدمة دعاواي
         * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-LegalRep-001"/>
         */
        export const MyCases: number = 30_01;

        /**
         * Lawsuits Inquiry
         * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-LegalRep-002"/>
         */
        export const ManageProfile: number = 30_02;

        /**
         * خدمة مواعيدي
         * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-LegalRep-003"/>
         */
        export const MyAppointments: number = 30_03;

        /**
         * خدمة الاستعلام عن قضايا طرف
         * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-LegalRep-005"/>
         */
        export const CaseParty: number = 30_05;

        /**
           * خدمة الاطلاع على الأحكام والقضايا
   * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-AC-023"/>
   */
        export const RullingCase: number = 30_09;
        /**
        *  خدمة التبليعات
        * <seealso href="https://wiki.elm.sa/requirements/LRP/BOG-GAP-Permissions-LegalRep-013"/>
        */

        export const SessionNotification: number = 30_13;
    }
}



namespace AttachmentPermissions {
    export const AttachmentsFullControl = [
        AdminPermissions.AccountManager.AddMainAccountManager,
        AdminPermissions.AccountManager.EditMainAccountManager,

        AccountManagerPermissions.ManageAccountManagers.AddAccountManager_LowerLevels,
        AccountManagerPermissions.ManageAccountManagers.EditAccountManager_LowerLevels,

        AccountManagerPermissions.Representative.AddRepresentative_SameLevel,
        AccountManagerPermissions.Representative.EditRepresentative_SameLevel,

        AccountManagerPermissions.Representative.AddRepresentative_LowerLevels,
        AccountManagerPermissions.Representative.EditRepresentative_LowerLevels,
    ];

    export const AttachmentsDownLoad = [
        AdminPermissions.AccountManager.ViewAccountManager_All_Levels,
        AdminPermissions.Representative.ViewRepresentative_AllLevels,

        AccountManagerPermissions.Representative.ViewRepresentative_SameLevel,
        AccountManagerPermissions.Representative.ViewRepresentative_LowerLevels,
    ];

}



export namespace Permissions {
    export const Attachment = AttachmentPermissions;
    export const Admin = AdminPermissions;
    export const Representative = RepresentativePermissions;
    export const AccountManager = AccountManagerPermissions;
}

export class Roles {
    public static readonly Admin: ILookupDetailsResult = { id: 1001, name: 'BOG-System Admin', nameAr: 'مدير النظام' }
    public static readonly AccountManager: ILookupDetailsResult = { id: 2001, name: 'Account Manager', nameAr: 'مدير حسابات' }
    public static readonly LegalRepresentative: ILookupDetailsResult = { id: 2002, name: 'Legal Representative', nameAr: 'ممثل قانوني لجهة حكومية' }
}
