<div class="d-flex gap-2">
  <button
    pButton
    type="button"
    class="btn btn-sm btn-outline-secondary rounded-3"
    (click)="openAddNoteDialog()"
  >
    إضافة ملاحظة
  </button>

  <button
    pButton
    type="button"
    class="btn btn-sm btn-outline-primary rounded-3"
    (click)="openUserUpdatesDialog()"
  >
    عرض الملاحظات
  </button>
</div>

<p-dialog
  header="إضافة ملاحظة"
  [(visible)]="displayNoteDialog"
  [modal]="true"
  [draggable]="false"
  [resizable]="false"
  [closable]="true"
  [style]="{ width: '600px', height: 'auto' }"
  [contentStyle]="{ 'max-height': '500px', 'overflow-y': 'auto' }"
>
  <div class="p-fluid">
    <div class="field">
      <label for="noteText" class="fw-bold">ملاحظتك</label>
      <textarea
        id="noteText"
        rows="6"
        class="form-control"
        [(ngModel)]="noteText"
      ></textarea>
    </div>
  </div>

  <hr />
  <p-footer>
    <button
      type="button"
      class="btn btn-sm btn-secondary me-2"
      (click)="displayNoteDialog = false"
    >
      إلغاء
    </button>
    <button
      type="button"
      class="btn btn-sm btn-primary"
      (click)="saveComment()"
    >
      {{ isEditingNote ? "تحديث" : "حفظ" }}
    </button>
  </p-footer>
</p-dialog>

<p-dialog
  header="قائمة الملاحظات"
  [(visible)]="displayUserUpdatesDialog"
  [modal]="true"
  [draggable]="false"
  [resizable]="false"
  [closable]="true"
  [style]="{ width: '1000px', height: 'auto' }"
  [contentStyle]="{ 'max-height': '600px', 'overflow-y': 'auto' }"
>
  <!-- Pinned Updates -->
  <div *ngIf="userUpdates.length > 0">
    <h5 class="fw-bold text-primary">ملاحظاتي</h5>
    <div
      class="border rounded p-2 mb-2 pin-highlight-background"
      *ngFor="let item of userUpdates"
    >
      <div class="fw-bold d-flex justify-content-between">
        <!--        <span>بواسطة المستخدم: {{ item.createdByUserName }}</span>-->
        <span>{{ item.creationDate | date : "fullDate" }}</span>

        <!-- <button class="btn btn-sm btn-outline-primary rounded-5 mt-2" (click)="openAddNoteDialog()">
          تعديل
        </button> -->

        <img
          src="assets/svg/trash.svg"
          alt="حذف"
          width="40"
          height="40"
          class="cursor-pointer"
          (click)="confirmingEntry = item; displayDeleteConfirm = true"
        />
      </div>

      <div class="text-container">
        {{ item.comment }}
      </div>
    </div>
  </div>

  <!-- Other Updates -->
  <div *ngIf="otherUpdates.length > 0">
    <div class="d-flex justify-content-between align-items-center mb-3">
      <h5 class="fw-bold">ملاحظات المستخدمين</h5>
      <div>
        <button
          class="btn btn-sm btn-outline-primary rounded-5 me-2"
          (click)="sortUserUpdates('DESC')"
        >
          الأحدث أولاً
        </button>
        <button
          class="btn btn-sm btn-outline-primary rounded-5"
          (click)="sortUserUpdates('ASC')"
        >
          الأقدم أولاً
        </button>
      </div>
    </div>

    <div class="border rounded p-2 mb-2" *ngFor="let item of otherUpdates">
      <div class="fw-bold d-flex justify-content-between">
        <span>بواسطة المستخدم {{ item.createdByUserName }}</span>
        <span>{{ item.createdAt | date : "fullDate" }}</span>
      </div>

      <div class="text-container">
        {{ item.summaryText }}
      </div>
    </div>
  </div>

  <div *ngIf="userUpdates.length === 0">لا توجد ملاحظات</div>
</p-dialog>

<p-dialog
  header="تأكيد الحذف"
  [(visible)]="displayDeleteConfirm"
  [modal]="true"
  [closable]="true"
  [dismissableMask]="true"
  [style]="{ width: '350px' }"
>
  <p>هل أنت متأكد أنك تريد ازالة هذا التعليق؟</p>
  <div class="d-flex justify-content-end gap-2 mt-3">
    <button
      type="button"
      class="btn btn-warning text-primary fw-bold d-flex"
      (click)="displayDeleteConfirm = false"
    >
      إلغاء
    </button>
    <button
      type="button"
      class="btn btn-danger text-primary fw-bold d-flex"
      (click)="deleteComment(confirmingEntry); displayDeleteConfirm = false"
    >
      حذف
    </button>
  </div>
</p-dialog>
