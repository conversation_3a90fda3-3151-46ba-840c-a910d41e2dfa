import { Component, <PERSON><PERSON><PERSON>roy, OnInit, inject } from '@angular/core';
import { ExtractInfoEvents } from '../../../shared/events/extract.events';
//import { NERFeatures, NERService } from '../../../services';
import { Subscription, take, timeout } from 'rxjs';
import {ITagResult, NerPredict} from "../extract-info-response.model";

/**
 * @description الأعلام
 */
@Component({
  selector: 'app-extract-info-ner',
  templateUrl: './extract-info-ner.component.html',
  styleUrls: ['./extract-info-ner.component.css']
})
export class ExtractInfoNerComponent implements OnInit, OnDestroy {

  protected subscription!: Subscription

  //private readonly classificationService = inject(NERService);
  /**
   * @description النتيجة
   */
  protected nerPredictData: NerPredict = { tags: [] };

  constructor() { }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  ngOnInit() {
    this.subscription = ExtractInfoEvents.InfoExtracted.getListener()
      .pipe(take(1))
      .subscribe(event => this.loadNerPredict(event!));
  }

  private loadNerPredict(text: string) {
    // this.classificationService.predictNerPredictPost({
    //   case_type: 'bog',
    //   text: text,
    // })
    //   .pipe(timeout(60_000))
    //   .subscribe(data => this.data = data)
    const fileNerData = localStorage.getItem('file_entities');

    if (fileNerData) {
      try {
        const parsedData = JSON.parse(fileNerData);
        this.nerPredictData.tags = parsedData.tags || [];
      } catch (error) {
        this.nerPredictData = { tags: [] };
      }
    } else {
      this.nerPredictData = { tags: [] };
    }
  }

  formatResult() {
    const words: Array<string> = [];

    const result = this.nerPredictData.tags
      ?.filter((tag: ITagResult) => tag.tag == "O")
      .map((tag: ITagResult) => tag.word) ?? [];

    result.forEach((element: string) => {
      if (words.indexOf(element) === -1) {
        words.push(element);
      }
    });

    return words;
  }
}
