import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../auth.service';
import { NotificationService } from '../../shared/services/notification/notification.service';

@Component({
  selector: 'lib-login-callback',
  template: ``,
  standalone: true
})
export class LoginCallbackComponent implements OnInit {

  constructor(
    protected authService: AuthService,
    private router: Router,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    const userProfile = localStorage.getItem("user-profile");

    if (!userProfile || userProfile === "null") {
      this.authService.loginCallback().subscribe((response) => {
        if (!response) {
          this.router.navigate(['/unauthorized']);
          return;
        }

        if (!response.isActive) {
          this.router.navigate(['/deactivated-account']);
          return;
        }

        this.router.navigate(['/my-settings']);
        setTimeout(() => {
          this.notificationService.loginCallbackFinished.next(true);
        }, 1500);
      });
    } else {
      this.router.navigate(['/my-settings']);
    }
  }
}
