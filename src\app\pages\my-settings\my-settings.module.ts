import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MySettingsComponent } from './my-settings.component';
import { RouterModule } from '@angular/router';
import { AccountSettingsComponent } from './account-settings/account-settings.component';
import { CommentsComponent } from './comments/comments.component';
import { FavoriteComponent } from './favorite/favorite.component';
import { PersonalInformationComponent } from './personal-information/personal-information.component';
import { MyActivitiesComponent } from './my-activities/my-activities.component';
import { TableModule } from 'primeng/table';
import { DialogModule } from 'primeng/dialog';
import { SearchModule } from '../search/search.module';
import { FormsModule } from '@angular/forms';
import { FavoriteItemComponent } from './favorite-item/favorite-item.component';
import { MarkdownPipe } from '../../markdown.pipe';

@NgModule({
  declarations: [
    MySettingsComponent,
    AccountSettingsComponent,
    CommentsComponent,

    // FavoriteItemComponent,
    // MyActivitiesComponent,
    PersonalInformationComponent
  ],
  imports: [
    CommonModule,
    TableModule,
    DialogModule,
    SearchModule,
    FormsModule,
    MarkdownPipe,
    FavoriteComponent,
    RouterModule.forChild([
      {
        path: '',
        component: MySettingsComponent
      }
    ])
  ],

})
export class MySettingsModule { }
