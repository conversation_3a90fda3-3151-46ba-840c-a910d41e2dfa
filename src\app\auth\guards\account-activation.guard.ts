import { ActivatedRouteSnapshot, CanActivate, CanActivateChild, RouterStateSnapshot, UrlTree } from "@angular/router";
import { BaseAuthGuard } from "./base-auth.guard";
import { Observable } from "rxjs";
import { UserProfile } from "../models/user-profile.interface";
import { Injectable, Injector } from "@angular/core";


@Injectable({
    providedIn: 'root'
  })
export class AccountActivationGuard extends BaseAuthGuard implements  CanActivate, CanActivateChild {


    constructor(injector: Injector) {
        super(injector);
      }

      
    canActivateChild(childRoute: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean | UrlTree | Observable<boolean | UrlTree> | Promise<boolean | UrlTree> {
        return this.validate();
    }
    
    canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean | UrlTree | Observable<boolean | UrlTree> | Promise<boolean | UrlTree> {
        return this.validate();
    }

    protected validate(): boolean | UrlTree {
        if (this.getProfile && !this.getProfile.isActive)
          return this.router.createUrlTree(['/blocked']);

        return true;
      }

      private get getProfile():UserProfile {
       return this.authService?.UserProfile;
      }
    
}