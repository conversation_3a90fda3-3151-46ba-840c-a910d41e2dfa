.card-body {
  padding: 8px;
  background: transparent;
}

.pin-highlight-background {
  background-color: #dbf6ec;
}

.text-container {
  white-space: pre-wrap;
}

.send-button {
  border-radius: 50px;
  margin-inline-start: 20px;
  width: 124px;
  height: 42px;
}

:host {
  ::ng-deep .p-overlaypanel .p-overlaypanel-content {
    padding: 0px !important;
  }
}

.user-text {
  background-color: rgba(217, 217, 217, 0.22);
  border-radius: 10px;
  margin: 5px 0;
}

/* .systemMsg{
  background-color: #E4F3EE;
   border-radius: 10px;
    margin: 5px 0;
} */

.systemMsg {
  background-color: #e4f3ee; /* Original background color */
  border-radius: 10px; /* Rounded corners */
  margin: 5px 0; /* Margin for spacing between boxes */
  padding: 10px; /* Padding inside the box for spacing */
  word-wrap: break-word; /* Ensure long words wrap to the next line */
  overflow-wrap: break-word; /* Prevent overflow of long words */
  white-space: pre-wrap; /* Ensures whitespace and indentation are respected */
  width: 100%; /* Ensure the box spans the full width of its container */
  box-sizing: border-box; /* Include padding in the box's total width */
  list-style-position: inside; /* Positions the list item markers inside the box */
}

.color-picker-popup {
  z-index: 9999;
}

.color-circle {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  border: 1px solid #ccc;
}

.color-picker-popup {
  /* keep the white background */
  background-color: #fff;

  /* sharper, slightly thicker border */
  border: 2px solid rgba(0, 0, 0, 0.1);

  /* subtle shadow for depth */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);

  /* a tasteful accent stripe on the left edge */
  border-left: 4px solid #488371;

  /* smooth rounded corners */
  border-radius: 6px !important;

  /* padding stays as is */
  padding: 0.5rem;
}

/* keep your circles the same, but add a tiny border to each */
.color-circle {
  width: 24px;
  height: 24px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  cursor: pointer;
  transition: transform 0.1s ease;
}

.color-circle:hover {
  transform: scale(1.15);
}

.tab-button {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.tab-button:hover {
  background-color: #e9ecef;
}

.tab-button.active {
  background-color: #fff;
  border-color: #adb5bd;
  color: #212529;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}
