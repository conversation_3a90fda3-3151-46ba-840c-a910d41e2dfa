import { Component, EventEmitter, inject, OnInit, Output } from '@angular/core';
import { ApiResponse, BaseComponent, BaseForm, BaseTranslationKeys, CustomFormControl, UploadAttachmentInput, UploadAttachmentResult } from '../../../../../shared/public.api';
import { Validators } from '@angular/forms';
import { BASE_PATH, SemanticService } from '../../../../../services';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Router } from "@angular/router";
import { UploadFileResult } from "../../../../extract/models/upload-file-result";
import { ExtractInfoResponse, SemanticExtractSearchResult } from "../../../../extract/extract-info-response.model";
import { ExtractInfoService } from "../../../../extract/extract-info.service";

@Component({
  selector: 'app-semantic-search-landing-file',
  templateUrl: './semantic-search-landing-file.component.html',
  styleUrls: ['./semantic-search-landing-file.component.css']
})
export class SemanticSearchLandingFileComponent extends BaseComponent implements OnInit {
  private readonly baseBath = inject(BASE_PATH);
  private readonly _httpClient = inject(HttpClient);
  protected readonly router = inject(Router);
  private readonly semantic = inject(SemanticService);

  @Output() onSemanticSearch = new EventEmitter<any>();

  fileError: string | null = null;

  constructor() {
    super(new BaseTranslationKeys('semantic', 'files'))
  }


  ngOnInit() {
  }

  toFormData<T>(formValue: any) {
    const formData = new FormData();
    for (const key of Object.keys(formValue)) {
      const value = formValue[key];
      formData.append(key, value);
    }
    return formData;
  }

  private upload(input: any): Observable<ApiResponse<SemanticExtractSearchResult>> {
    return this._httpClient.post<ApiResponse<SemanticExtractSearchResult>>(`${this.baseBath}/api/semantic/extract/file`, this.toFormData(input));
    this.semantic.extractFromFile
  }

  submitSearch(event: any) {
    const file = event.target.files[0];

    const allowedTypes = ['text/plain'];
    if (!allowedTypes.includes(file.type)) {
      this.fileError = 'يُسمح فقط بملفات .txt';
      return;
    }

    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
      this.fileError = 'حجم الملف يتجاوز 5 ميجابايت يرجى تحميل ملف أصغر';
      return;
    }

    this.fileError = null;
    const input = this.getInput(file)
    if (input) {
      this.upload({
        content: input.content,
        fileMetadata: this.toJson(input.fileMetadata)
      }).subscribe((response: ApiResponse<SemanticExtractSearchResult>) => {
        console.log(response);
        const data = response.data;
        localStorage.setItem('file', JSON.stringify(response));
        localStorage.setItem('file_caseDate', JSON.stringify(response.data?.caseDate));
        localStorage.setItem('file_caseText', JSON.stringify(response.data?.caseText));
        localStorage.setItem('file_law', JSON.stringify(response.data?.law));
        localStorage.setItem('file_verdict', JSON.stringify(response.data?.verdict));
        localStorage.setItem('file_entities', JSON.stringify(response.data?.entities));
        localStorage.setItem('file_category', JSON.stringify(response.data?.category));
        localStorage.setItem('file_subCategory', JSON.stringify(response.data?.subCategory));
        localStorage.setItem('file_keywords', JSON.stringify(response.data?.keyWords));

        // localStorage.setItem('file', JSON.stringify(response));
        this.router.navigate(['/search/semantic/result']);
      });
    }
  }

  private getInput(file: any): UploadAttachmentInput {
    return {
      fileMetadata: {
        Name: this.getFileName(file?.name),
      },
      content: file
    };
  }

  getFileName(name: any): string {
    let fileNameSplitted: string[] = name.split('.');
    if (fileNameSplitted.length > 2) {
      fileNameSplitted.pop();
      return fileNameSplitted.join(".");
    }
    else {
      return name.split('.')[0];
    }
  }

}

export class Controls extends BaseForm {
  public readonly fileMetadata = new CustomFormControl('fileMetadata', [Validators.required]).setForm(this.form);
  public readonly content = new CustomFormControl('content', [Validators.required]).setForm(this.form);
  constructor() {
    super();
  }
}
