import { Injectable } from "@angular/core";
import { BehaviorSubject, Observable, Subject } from "rxjs";

@Injectable({
    providedIn: 'root'
})
export class NotificationService {

    private readonly readNotificationItemsListner: Subject<any> = new Subject<any>();
    public readonly onReadNotification$: Observable<any> = this.readNotificationItemsListner.asObservable();


    public loginCallbackFinished: Subject<boolean> = new Subject<boolean>();
    public loginCallbackFinishedObs = this.loginCallbackFinished.asObservable();

    public makeNotificationRead(items: any): void {
        this.readNotificationItemsListner.next(items);
    }
}