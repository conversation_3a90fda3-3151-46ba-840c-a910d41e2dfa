.option-card {
  background-color: #fff;
  border-radius: 10px;
  border-radius: 16px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #d7d8db;
}

.option-icon {
  font-size: 2rem;
  color: #2d4f43;
  background-color: rgba(45, 79, 67, 0.1);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.option-card-title {
  font-weight: 600;
  color: #333;
}

.option-card-text {
  color: #666;
  font-size: 0.95rem;
}

.btn-outline-primary {
  color: #2d4f43;
  border: 1px solid #d2d6db;
  padding: 8px 16px;
  border-radius: 4px;
}

.btn-outline-primary:hover {
  background-color: #2d4f43;
  color: #fff;
}

.file-upload-btn {
  position: relative;
  overflow: hidden;
  display: inline-block;
}
