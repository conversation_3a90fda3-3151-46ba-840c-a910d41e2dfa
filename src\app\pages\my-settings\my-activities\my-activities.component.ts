import { Component, Inject, LOCALE_ID, OnInit } from '@angular/core';
import { MySettingsBaseComponent } from '../my-settings-base.component';
import { CaseDocumentCommandResultPortalResponse, ChatHistoryItem, ChatService, CommentInput, CommentService, FavoriteTypeEnum, HighlightInput, HighlightSectionEnum, HighlightService, SemanticChatInput, SemanticService, UserCaseHistory } from '../../../services';
import { AuthService } from '../../../auth/auth.service';
import { Router } from '@angular/router';
import { forkJoin, map, Observable, tap } from 'rxjs';
import { CommonModule } from '@angular/common';
import { DialogModule } from 'primeng/dialog';
import { MarkdownPipe } from '../../../markdown.pipe';


@Component({
  standalone: true,
  selector: 'app-my-activities',
  templateUrl: './my-activities.component.html',
  imports: [CommonModule,DialogModule, MarkdownPipe],
})
export class MyActivitiesComponent extends MySettingsBaseComponent implements OnInit {
  chatHistory: UserCaseHistory[] = [];
  commentHistory: UserCaseHistory[] = [];
  highlightHistory: UserCaseHistory[] = [];
  allHistory: UserCaseHistory[] = [];
  dialogVisible = false;
  displayDeleteConfirm: boolean = false;
  displayDeleteChatConfirm: boolean = false;
  confirmingEntry: any = null;
  favoriteType = FavoriteTypeEnum;
  constructor(
    private chatService: ChatService,
    private commentService: CommentService,
    private highlightService: HighlightService,
    private router: Router,
    @Inject(LOCALE_ID) private locale: string
  ) {
    super('my-activities')
  }

  readonly stringLengthShown: number = 120;

  sectionNames = Object.values(Section);
  selectedSection: Section = Section.All;


  currentNetworkUser!: string | null;
  selectedEntry: ChatHistoryItem | null = null;
  ngOnInit() {
    this.fetchAll();
    // this.authService.currentNetworkUser$.subscribe({
    //   next: (networkUser) => {
    //     this.currentNetworkUser = networkUser;
    //     this.queryChat();
    //   },
    //   error: (err) => console.error('Error fetching network user:', err),
    // });
  }

  fetchAll() {
    forkJoin({
      chats: this.queryChat(),
      comments: this.getComments(),
      highlights: this.getHighlights()
    }).subscribe({
      next: ({ chats, comments, highlights }) => {
        // both arrays arrived—build allHistory
        const chatMarked = chats.map(item => ({ ...item, source: 'chat' as const }));
        const commentMarked = comments.map(item => ({ ...item, source: 'comment' as const }));
        const highlightsMarked = highlights.map(item => ({ ...item, source: 'highlight' as const }));

        this.allHistory = [...chatMarked, ...commentMarked, ...highlightsMarked]
          .sort((a, b) => {
            const tA = a.creationDate ? new Date(a.creationDate).getTime() : 0;
            const tB = b.creationDate ? new Date(b.creationDate).getTime() : 0;
            return tA - tB;
          });
      },
      error: err => {
        console.error('Error loading histories', err);
        this.loading = false;       // if you have a loading flag
      },
      complete: () => {
        console.log('All history streams complete');
      }
    });
  }

  showDialog(entry: UserCaseHistory): void {
    const numericCaseId = entry.RefId;
    let ischat = entry.Source == 'chat';
    if (entry.TypeId == FavoriteTypeEnum.Case) {
      this.viewCaseDetails(numericCaseId, ischat); // assuming this expects a number
    }
    else if (entry.TypeId == FavoriteTypeEnum.Regulation) {
      this.viewRegulationDetails(numericCaseId); // assuming this expects a number
    }

    this.selectedEntry = entry;
    this.dialogVisible = true;
  }

  // Make sure these return the raw arrays, not void:
  queryChat(): Observable<UserCaseHistory[]> {
    const input: SemanticChatInput = {
      // document: this.selectedCase.cleanTxt ?? '',
      // messages: this.newMessage,
      // chatHistory: this.chatHistory,
      // caseId: this.selectedCase.caseId.toString()
    };
    return this.chatService
      .getCasesWithChat(input)                                    // returns Observable<UserCaseHistoryResultPortalResponse>
      .pipe(
        map(resp => resp.data.userHistory ?? []),             // pluck out the array
        tap(data => this.chatHistory = data)                  // still populate this.chatHistory if you need it
      );
  }

  getComments(): Observable<UserCaseHistory[]> {
    return this.commentService
      .getCasesWithComments()                                    // returns Observable<UserCaseHistoryResultPortalResponse>
      .pipe(
        map(resp => resp.data.userHistory ?? []),
        tap(data => this.commentHistory = data)
      );
  }

  getHighlights(): Observable<UserCaseHistory[]> {
    return this.highlightService
      .getHighlights()                                    // returns Observable<UserCaseHistoryResultPortalResponse>
      .pipe(
        map(resp => resp.data.userHistory ?? []),
        tap(data => this.highlightHistory = data)
      );
  }

  selectedResult: any = null;
  loading = false;
  isCaseDetailsVisible = false;
  viewCaseDetails(id: any, isComment: boolean = false) {
    // this.router.navigate(['/users-case-viewer', id])
    this.router.navigate(
      ['/users-case-viewer', id],
      { queryParams: { openChat: isComment } }
    );
  }

  viewRegulationDetails(id: any) {
    // this.router.navigate(['/users-case-viewer', id])
    this.router.navigate(
      ['/users-law-viewer', id]
    );
  }

  getSectionTitle() {
    switch (this.selectedSection) {
      case Section.MyChats:
        return 'محادثاتي';
      case Section.MyComments:
        return 'تعليقاتي';
      case Section.MyComments:
        return 'الكل';
      default:
        return '';
    }
  }

  getHistory() {
    switch (this.selectedSection) {
      case Section.MyChats:
        return this.chatHistory;
      case Section.MyComments:
        return this.commentHistory;
      default:
        return [];
    }
  }

  deleteComment(selectedResult: any) {
    const input: CommentInput = {
      caseId: selectedResult.CaseId,
      refId: selectedResult.RefId,
      typeId: FavoriteTypeEnum.Case
    };
    this.commentService.deleteComment(input).subscribe({
      next: () => {
        this.loading = false;
        this.fetchAll();
      },
      error: (err) => {
        this.loading = false;
        console.error('Error retrieving case details:', err);
      },
    });
  }

  deleteItem(selectedResult: any) {
    if (selectedResult.Source === 'comment') {
      this.deleteComment(selectedResult);
    } else if (selectedResult.Source === 'chat') {
      this.deleteMessages(selectedResult);
    }
    else if (selectedResult.Source === 'highlight') {
      this.deleteHighlight(selectedResult);
    }
  }

  deleteHighlight(selectedResult: UserCaseHistory): void {
    const input: HighlightInput = {
      caseId: selectedResult.CaseId,
      refId: selectedResult.RefId,
      endOffset: selectedResult.endOffset!,
      startOffset: selectedResult.startOffset!,
      sectionId: HighlightSectionEnum.Actions,
      typeId: FavoriteTypeEnum.Case
    };

    this.highlightService.deleteHighlights(input).subscribe({
      next: (response) => {
        // let responseString = response.data.response?.toString() || '';
        // this.messages = [];
        this.fetchAll();

      },
      error: (err) => {
        console.error('Error querying chat:', err);
      }
    });
  }

  deleteMessages(selectedResult: any): void {
    const input: SemanticChatInput = {
      caseId: selectedResult.CaseId,
      refId: selectedResult.RefId
    };
    this.chatService.deleteChat(input).subscribe({
      next: (response) => {
        // let responseString = response.data.response?.toString() || '';
        // this.messages = [];
        this.fetchAll();

      },
      error: (err) => {
        console.error('Error querying chat:', err);
      }
    });
  }

  confirmRemoveFavorite(entry: any) {
    const confirmed = window.confirm('هل أنت متأكد أنك تريد إزالة هذه القضية/القانون من المفضلة؟');
    if (confirmed) {
      this.deleteComment(entry);
    }
  }

  Section = Section;
}


export enum Section {
  All = "الكل",
  MyChats = 'محادثاتي',
  MyComments = 'تعليقاتي',
  Highlights = 'تمييزاتي'
}
