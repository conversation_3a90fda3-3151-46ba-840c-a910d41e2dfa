import { Injector } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../auth.service';


export class BaseAuthGuard {

  protected authService!: AuthService;
  protected router!: Router;
  constructor(injector: Injector) {
    this.authService = injector.get(AuthService);
    this.router = injector.get(Router);

  }

  protected get isAuthenticated() {
    return localStorage.getItem('accessToken');
  }


}
