import { Component, Input, <PERSON><PERSON><PERSON>roy, OnInit, inject } from '@angular/core';
import { AngularSvgIconModule } from 'angular-svg-icon';
import { SplitterModule } from 'primeng/splitter';
import { SearchFullScreenComponent } from './semantic-search/search-full-screen/search-full-screen.component';
import { SearchSmallScreenComponent } from './semantic-search/search-small-screen/search-small-screen.component';
import { SearchResultListComponent } from './semantic-search/search-result-list/search-result-list.component';
import { SearchTypeEnum } from '../../shared/models/search-type.enum';
import { ActivatedRoute, Router } from '@angular/router';
import { SearchEvents } from '../../shared/events/search.events';

@Component({
  selector: 'app-search',
  templateUrl: './search.component.html',
  styleUrl: './search.component.scss',
})
export class SearchComponent implements OnInit {
  @Input() searchType!: SearchTypeEnum;
  category: { mainCategory: string, subCategory: string } | undefined = undefined;

  private readonly router = inject(Router)
  ngOnInit(): void {

    const state = history.state ?? localStorage.getItem('selected_category');
    if (!state) {
      this.router.navigateByUrl('/search/semantic/result');
    }
    this.category = state.category;
    localStorage.setItem('selected_category', JSON.stringify(this.category));

    SearchEvents.SearchCategorySelected.pushEvent({
      searchType: this.searchType,
      mainCategory: this.category?.mainCategory!,
      subCategory: this.category?.subCategory!,
    }!)
  }

}
