/**
 * MOSAED.Internal.Api
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface CaseData {
    recordId: number;
    refId: string;
    caseId: number;
    category?: string | null;
    subCategory?: string | null;
    text?: string | null;
    verdict?: string | null;
    rules?: Array<string> | null;
    keywords?: Array<string> | null;
    summary?: string | null;
    auditVerdictNumber?: string | null;
    auditVerdictDate?: string | null;
    hearingDate?: string | null;
    verdictNumber?: string | null;
    verdictDate?: string | null;
    namedEntities?: Array<string> | null;
    groupedKeywords?: Array<string> | null;
    groupedEntities?: Array<string> | null;
    groupedNamedEnities?: Array<string> | null;
    cleanTxt?: string | null;
    embedding_score?: number | null;
    rerankedScore?: number | null;
    [key: string]: any;
    actions: string | null;
    reasons: string | null;
}

