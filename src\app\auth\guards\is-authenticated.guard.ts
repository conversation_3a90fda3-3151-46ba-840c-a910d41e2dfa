import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, CanActivateChild, Router, RouterStateSnapshot, UrlTree } from '@angular/router';
import { AuthService } from "../auth.service";

@Injectable({
  providedIn: 'root',
})
export class IsAuthenticatedGuard implements CanActivate, CanActivateChild {
  constructor(private authService: AuthService, private router: Router) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): boolean | UrlTree {
    return this.checkIsAuthenticated();
  }

  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): boolean | UrlTree {
    return this.checkIsAuthenticated();
  }

  private checkIsAuthenticated(): boolean | UrlTree {
    const isAuthenticated = localStorage.getItem('isAuthenticated');
    if (isAuthenticated === 'false') {
      return this.router.createUrlTree(['/unauthorized']);
    }
    return true;
  }
}
