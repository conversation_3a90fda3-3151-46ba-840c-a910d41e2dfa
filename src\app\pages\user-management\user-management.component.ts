
import { AuthService } from './../../auth/auth.service';
import { Component, OnInit, ViewChild, inject, viewChild } from '@angular/core';
import { Router } from '@angular/router';
import { UserDetailsResult, UserManagementService, UsersListQueryInput, UsersListQueryResult, UsersListQueryResultPagedResult } from "../../services";
import { BaseComponent } from '../../shared/base-component';
import { UserManagementBase } from './user-management-base.component';
import { ConfirmationService, MenuItem } from "primeng/api";
import { ViewUserDetailsComponent } from './view-user-details/view-user-details.component';
import { UpdateUserComponent } from './update-user/update-user.component';
import { Table } from "primeng/table";
import { UserProfile } from '../../auth/public-api';
import { Role } from '../../shared/enums/role.enum';
import { CreateUserWithProfileComponent } from './create-user-with-profile/create-user-with-profile.component';

@Component({
  selector: 'app-user-management',
  templateUrl: './user-management.component.html',
  styleUrls: ['./user-management.component.scss'],
  providers: [ConfirmationService]
})
export class UserManagementComponent extends UserManagementBase implements OnInit {
  @ViewChild('userTable') userTable!: Table;
  users: any[] = [];
  selectedUser: any;
  menuItems: MenuItem[] = [];
  selectedUserDetails: any = null;
  showDetails: boolean = false;
  queryInput: UsersListQueryInput = {
    pageNumber: 1,
    pageSize: 10
  };
  protected userInformation: UserProfile | null = null;
  userRoleId: any;
  bogSuperAdmin = Role.SuperAdmin;
  showEditDialog: boolean = false;
  editedUser: any = {};

  private readonly confirmationService = inject(ConfirmationService);
  protected readonly authService = inject(AuthService);

  @ViewChild('userDetail') userDetail!: ViewUserDetailsComponent;
  @ViewChild('updateUser') updateUser!: UpdateUserComponent;
  constructor() {
    super('')
  }

  ngOnInit(): void {
    this.authService.userProfileObservable.subscribe(user => {
      this.userInformation = user;
      this.userRoleId = user?.profiles[0]?.role?.id;

    });
  }

  initializeMenu(user: UsersListQueryResult) {
    this.selectedUser = user;

    this.menuItems = [
      {
        label: ' تعديل ',
        icon: 'pi pi-pencil',
        command: () => this.editUser(user),
        visible: !user.loggedIn
      },
      {
        label: ' تفعيل ',
        icon: 'pi pi-check',
        command: () => this.activateUser(user),
        visible: !user.isActive
      },
      {
        label: ' تعطيل ',
        icon: 'pi pi-times',
        command: () => this.deactivateUser(user),
        visible: user.isActive
      },
      {
        label: ' عرض التفاصيل ',
        icon: 'pi pi-eye',
        command: () => this.viewUser(user),
        visible: user.loggedIn,

      }
    ];
  }

  editUser(user: any) {
    this.updateUser.open(user);
  }

  activateUser(user: UsersListQueryResult) {
    this.confirmationService.confirm({
      message: '<h3>هل تريد التفعيل؟</h3>',
      acceptLabel: 'نعم',
      rejectLabel: 'لا',
      acceptIcon: "none",
      rejectIcon: "none",
      acceptButtonStyleClass: 'btn btn-primary mx-2',
      rejectButtonStyleClass: 'btn btn-outline-danger mx-2',
      accept: () => {
        this._userManagementService.updateStatus({
          id: user.id,
          isActive: true
        }).subscribe(response => {
          if (response.isSuccess) {
            this.getUsersList();
          }
          this._messageService.add({
            severity: 'success',
            summary: 'نجحت العملية',
            detail: 'تم تفعيل المستخدم بنجاح'
          });
        })
      },
      reject: () => {
        console.log('User activation canceled:', user);
      }
    });
  }

  deactivateUser(user: any) {
    this.confirmationService.confirm({
      message: '<h3>هل تريد إلغاء التفعيل؟</h3>',
      acceptLabel: 'نعم',
      rejectLabel: 'لا',
      acceptIcon: "none",
      rejectIcon: "none",
      acceptButtonStyleClass: 'btn btn-primary mx-2',
      rejectButtonStyleClass: 'btn btn-outline-danger mx-2',
      accept: () => {
        this._userManagementService.updateStatus({
          id: user.id,
          isActive: false
        }).subscribe(response => {
          if (response.isSuccess) {
            this.getUsersList();
          }
          this._messageService.add({
            severity: 'success',
            summary: 'نجحت العملية',
            detail: 'تم تعطيل المستخدم بنجاح',
            life: 50000
          });
        })
      },
      reject: () => {
        console.log('User deactivation canceled:', user);
      }
    });
  }


  viewUser(user: any) {

    this._userManagementService.userDetails(user.id)
      .subscribe({
        next: (response) => {
          if (response.isSuccess == false) {
            console.error('Failed to get user details', response.error);
            return;
          }

          this.userDetail.open(response.data);
        },
        error: (error) => {
          console.error('Error getting user details', error);

        }
      })
  }

  protected loadData(event: any) {
    this.setPagination<UsersListQueryInput>(event, this.queryInput);
    this.getUsersList();
  }

  protected usersResponse: UsersListQueryResultPagedResult = {
    pageNumber: 1,
    pageSize: 10,
    records: [],
    totalRecords: 0
  };

  getUsersList() {
    this._userManagementService.listUsers(this.queryInput).subscribe(
      (response: any) => {
        if (response.isSuccess) {
          this.usersResponse = response.data;
          this.users = response.data.records;
        } else {
          console.error('Failed to load users', response.error);
        }
      },
      (error: any) => {
        console.error('Error loading users', error);
      }
    );
  }

  reloadList() {
    if (this.userTable) {
      this.userTable.first = 0;
      this.loadData({ first: 0, rows: this.usersResponse?.pageSize });
    }
    this.queryInput = {
      pageNumber: 1,
      pageSize: 10
    }
    this.getUsersList();
  }
}
