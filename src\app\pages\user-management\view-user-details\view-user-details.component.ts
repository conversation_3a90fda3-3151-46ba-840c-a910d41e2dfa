import { Component, inject, OnInit } from '@angular/core';
import { UserDetailsResult } from '../../../services';
import { UserManagementBase } from '../user-management-base.component';

@Component({
  selector: 'app-view-user-details',
  templateUrl: './view-user-details.component.html',
})
export class ViewUserDetailsComponent extends UserManagementBase implements OnInit {


  constructor() {
    super('viewUserDetails')
  }

  ngOnInit() {
  }

  protected showDetails = false;
  protected userData!: UserDetailsResult;

  open(userData: UserDetailsResult) {
    this.showDetails = true;
    this.userData = userData;
  }

}
