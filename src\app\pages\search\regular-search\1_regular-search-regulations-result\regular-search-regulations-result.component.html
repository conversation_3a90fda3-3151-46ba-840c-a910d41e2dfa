@if(data){

@for (item of data; track $index) {


<div class="d-block card border-0 mb-4">
  <div class="card-body">
    <div class="d-flex">
      <h5 class="fw-bold pe-2 text-primary text-nowrap">{{$index+1}} -</h5>
      <div>
        <div class="pb-3">
          <div class="d-md-flex justify-content-between">
            <p class="text-primary text-decoration-underline fw-bold">
              <a href="{{item.url}}" target="_blank">{{item.name}}</a>
            </p>

            <p class="fw-bold">
              {{item.issueDate}}
            </p>
          </div>


          <div class="border-bottom border-secondary mt-1 mb-3 pb-3 d-flex justify-content-start">

            <div class=" border rounded-1 p-2 bg-secondary text-primary bg-opacity-25 me-2">
              <label class="w-100">
                الأداة
              </label>
              <b>
                {{item.issueTool}}
              </b>
            </div>

            <div class=" border rounded-1 p-2 bg-secondary text-primary bg-opacity-25 me-2">
              <label class="w-100">
                المنطقة
              </label>
              <b>
                {{item.state}}
              </b>
            </div>

            <div class=" border rounded-1 p-2 bg-secondary text-primary bg-opacity-25 me-2">
              <label class="w-100">
                رقم الإنشاء
              </label>
              <b>
                {{item.issueNumber}}
              </b>
            </div>

            <div class=" border rounded-1 p-2 bg-secondary text-primary bg-opacity-25 me-2">
              <label class="w-100">
                اخر تعديل                
              </label>
              <b>
                {{item.lastUpdated}}
              </b>
            </div>

          </div>


          <p>
            {{item.description}}
          </p>
        </div>

      </div>
    </div>

  </div>
</div>
}
}