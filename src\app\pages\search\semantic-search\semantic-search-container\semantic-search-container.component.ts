import { Component, Input, OnInit, inject } from '@angular/core';
import { Router } from '@angular/router';
import { SearchEvents } from '../../../../shared/events/search.events';
import { SearchTypeEnum } from '../../../../shared/models/search-type.enum';

@Component({
  selector: 'app-semantic-search-container',
  templateUrl: './semantic-search-container.component.html',
  styleUrls: ['./semantic-search-container.component.css']
})
export class SemanticSearchContainerComponent implements OnInit {

  @Input() searchType!: SearchTypeEnum;
  category: { mainCategory: string, subCategory: string } | undefined = undefined;

  private readonly router = inject(Router)
  ngOnInit(): void {

    const state = history.state ?? localStorage.getItem('selected_category');
    if (!state) {
      this.router.navigateByUrl('/search/semantic/result');
    }
    this.category = state.category;
    localStorage.setItem('selected_category', JSON.stringify(this.category));

    SearchEvents.SearchCategorySelected.pushEvent({
      searchType: this.searchType,
      mainCategory: this.category?.mainCategory!,
      subCategory: this.category?.subCategory!,
    }!)
  }
}
