// scss-docs-start caret-mixins
@mixin caret-down($width: $caret-width) {
  border-top: $width solid;
  border-inline-end: $width solid transparent;
  border-bottom: 0;
  border-inline-start: $width solid transparent;
}

@mixin caret-up($width: $caret-width) {
  border-top: 0;
  border-inline-end: $width solid transparent;
  border-bottom: $width solid;
  border-inline-start: $width solid transparent;
}

@mixin caret-end($width: $caret-width) {
  border-top: $width solid transparent;
  border-inline-end: 0;
  border-bottom: $width solid transparent;
  border-inline-start: $width solid;
}

@mixin caret-start($width: $caret-width) {
  border-top: $width solid transparent;
  border-inline-end: $width solid;
  border-bottom: $width solid transparent;
}

@mixin caret(
  $direction: down,
  $width: $caret-width,
  $spacing: $caret-spacing,
  $vertical-align: $caret-vertical-align
) {
  @if $enable-caret {
    &::after {
      display: inline-block;
      margin-inline-start: $spacing;
      vertical-align: $vertical-align;
      content: "";
      @if $direction == down {
        @include caret-down($width);
      } @else if $direction == up {
        @include caret-up($width);
      } @else if $direction == end {
        @include caret-end($width);
      }
    }

    @if $direction == start {
      &::after {
        display: none;
      }

      &::before {
        display: inline-block;
        margin-inline-end: $spacing;
        vertical-align: $vertical-align;
        content: "";
        @include caret-start($width);
      }
    }

    &:empty::after {
      margin-inline-start: 0;
    }
  }
}
// scss-docs-end caret-mixins
