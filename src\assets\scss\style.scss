$primary: #365a4e;
$primary-dark: #365a4e;
$primary-light: #00ae8d;
$secondary: #c9b84d;
$light: #afafaf;
$gray: #6b6b6b;
// $blue: #211259;
$dark: #027962;
//$blue: #2d6cb5;
$body-bg: #fff;
$border-radius: 2px;
$alert-bg-scale: -90%;
$alert-border-scale: -50%;
$alert-color-scale: 50%;
$control-height: 46px;
$enable-negative-margins: true;
$container-max-widths: (
  sm: 540px,
  md: 720px,
  lg: 960px,
  xl: 1200px,
  xxl: 1470px,
);
$headings-margin-bottom: 1rem;
$enable-negative-margins: true;
$bg-edit-Button:#D8EAE5;
$bg-edit-Button-hover:#9fcabe;

// Bootstrap v5.0.2
@import "bootstrap/bootstrap";
@import "sub/rtl";

// ------------------------------ \-/ -------------------------------- //

@import "sub/reset";

@import "sub/animation";

@import "sub/_layout";

@import "sub/primeng";
@import "sub/primeng/flags";

// @import "sub/landing";

// @import "sub/faq";

@import "sub/custom";
@import "sub/custom-mk";

@import "sub/media-queries";


// control theme colors from here - you can add more variables from "_variables.scss"
//@import "../scss/color-schema.scss";

// @font-face {
//   font-family: "DIN Next LT Arabic Light";
//   src: url("../../assets/fonts/DIN Next LT Arabic Light.ttf");
// }
// @font-face {
//   font-family: "DIN Next LT Arabic Medium";
//   src: url("../../assets/fonts/DIN\ Next\ LT\ Arabic\ Medium.ttf");
// }
// @font-face {
//   font-family: "DIN Next LT Arabic Regular";
//   src: url("../../assets/fonts/DINNextLTArabic-Regular-3.ttf");
// }

$family: "DIN Next LT Arabic Regular", sans-serif;
$familyBold: "DIN Next LT Arabic Medium", sans-serif;
$familyLight: "DIN Next LT Arabic Light", sans-serif;

body {
  background: #f4f4f6 !important;
  font-size: 16px;
  font-family: "DIN Next LT Arabic Light", Courier, monospace !important;
}
.p-component {
  font-family: "DIN Next LT Arabic Light", Courier, monospace !important;
}
// @import "bootstrap/bootstrap.scss"; // v5.3.0 // local version so you can control imported components - please remove any unused components u don't need.
// @import "animate.css";
// @import "sub/primeng";

// @import "sub/rtl"; // to enable two directions support (using logical properties) - remove this file if u use english only
// @import "sub/layout";
// @import "sub/custom";
