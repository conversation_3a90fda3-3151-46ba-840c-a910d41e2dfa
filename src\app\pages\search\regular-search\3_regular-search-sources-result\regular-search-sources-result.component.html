@if(data){
<div class="d-block card border-0 mb-4" *ngFor="let item of data; let index = index">
  <div class="card-body">
    <div class="d-flex">
      <h5 class="fw-bold pe-2 text-primary text-nowrap">{{index+1}} -</h5>
      <div>
        <div class="pb-3">
          <div class="d-md-flex justify-content-between">
            <p class="text-primary text-decoration-underline fw-bold">
              <a href="{{item.url}}" target="_blank">{{item.name}}</a>
            </p>
          </div>


          <div class="border-bottom border-secondary mt-1 mb-3 pb-3 d-flex justify-content-start">

            <div class=" border rounded-1 p-2 bg-secondary text-primary bg-opacity-25 me-2">
              <label class="w-100">
                entity
              </label>
              <b>
                {{item.entity}}
              </b>
            </div>

            <div class=" border rounded-1 p-2 bg-secondary text-primary bg-opacity-25 me-2">
              <label class="w-100">
                النظام الأساسي
              </label>
              <b>
                {{item.mainSystem}}
              </b>
            </div>



          </div>


          <p>
            {{item.description}}
          </p>
        </div>

      </div>
    </div>
    <!-- <div class="d-flex justify-content-end">
        <svg-icon role="button" class="mx-3" src="../../../assets/svg/file-symlink.svg" pTooltip=" إضافة للمفضلة "
          tooltipPosition="top"></svg-icon>
        <svg-icon role="button" src="../../../assets/svg/bookmark.svg" pTooltip=" إضافة للمفضلة "
          tooltipPosition="top"></svg-icon>
      </div> -->
  </div>
</div>

}