@font-face {
  font-family: icomoon;
  src: url(../../font-icon/icomoon.eot);
  src: url(../../font-icon/icomoon.eot) format("embedded-opentype"),
    url(../../font-icon/icomoon.ttf) format("truetype"),
    url(../../font-icon/icomoon.woff) format("woff"),
    url(../../font-icon/icomoon.svg) format("svg");
  font-weight: 400;
  font-style: normal;
  font-display: block;
}
[class*=" icon-"],
[class^="icon-"] {
  font-family: icomoon !important;
  speak: never;
  font-style: normal;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon-trash:before {
  content: "\e90f";
}
.icon-comments:before {
  content: "\e90b";
}
.icon-id:before {
  content: "\e90c";
}
.icon-set:before {
  content: "\e90d";
}
.icon-book:before {
  content: "\e900";
}
.icon-bookmark:before {
  content: "\e901";
}
.icon-brand-google-analytics:before {
  content: "\e902";
}
.icon-burger-menu:before {
  content: "\e903";
}
.icon-file-search:before {
  content: "\e904";
}
.icon-file-symlink:before {
  content: "\e905";
}
.icon-files:before {
  content: "\e906";
}
.icon-form-arrow:before {
  content: "\e907";
}
.icon-list-details:before {
  content: "\e908";
}
.icon-list-search:before {
  content: "\e909";
}
.icon-search:before {
  content: "\e90a";
}
.icon-user-circle:before {
  content: "\e90e";
}
