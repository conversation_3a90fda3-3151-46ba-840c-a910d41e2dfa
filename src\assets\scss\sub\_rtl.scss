/*!
 * Bootstrap v5.0.2 (https://getbootstrap.com/)
   Rtl plugin by <PERSON><PERSON><PERSON>
 */
[dir="rtl"] {
  body {
    direction: rtl;
    text-align: right;
  }
  ol,
  ul {
    padding-right: 0;
    padding-left: 0;
  }

  dd {
    margin-right: 0;
    margin-left: 0;
  }

  caption {
    text-align: right;
  }

  legend {
    float: right;
  }
  legend + * {
    clear: right;
  }

  // [type="tel"],
  // [type="url"],
  // [type="number"],
  // [type="email"] {
  //   direction: ltr;
  // }

  .list-unstyled {
    padding-right: 0;
    padding-left: 0;
  }
  .list-inline {
    padding-right: 0;
    padding-left: 0;
  }

  .list-inline-item:not(:last-child) {
    margin-left: 0.5rem;
    margin-right: 0;
  }

  @media (min-width: 576px) {
    .offset-sm-0 {
      margin-right: 0;
    }

    .offset-sm-1 {
      margin-right: 8.33333333%;
      margin-left: 0;
    }

    .offset-sm-2 {
      margin-right: 16.66666667%;
      margin-left: 0;
    }

    .offset-sm-3 {
      margin-right: 25%;
      margin-left: 0;
    }

    .offset-sm-4 {
      margin-right: 33.33333333%;
      margin-left: 0;
    }

    .offset-sm-5 {
      margin-right: 41.66666667%;
      margin-left: 0;
    }

    .offset-sm-6 {
      margin-right: 50%;
      margin-left: 0;
    }

    .offset-sm-7 {
      margin-right: 58.33333333%;
      margin-left: 0;
    }

    .offset-sm-8 {
      margin-right: 66.66666667%;
      margin-left: 0;
    }

    .offset-sm-9 {
      margin-right: 75%;
      margin-left: 0;
    }

    .offset-sm-10 {
      margin-right: 83.33333333%;
      margin-left: 0;
    }

    .offset-sm-11 {
      margin-right: 91.66666667%;
      margin-left: 0;
    }
  }
  @media (min-width: 768px) {
    .offset-md-0 {
      margin-right: 0;
      margin-left: 0;
    }

    .offset-md-1 {
      margin-right: 8.33333333%;
      margin-left: 0;
    }

    .offset-md-2 {
      margin-right: 16.66666667%;
      margin-left: 0;
    }

    .offset-md-3 {
      margin-right: 25%;
      margin-left: 0;
    }

    .offset-md-4 {
      margin-right: 33.33333333%;
      margin-left: 0;
    }

    .offset-md-5 {
      margin-right: 41.66666667%;
      margin-left: 0;
    }

    .offset-md-6 {
      margin-right: 50%;
      margin-left: 0;
    }

    .offset-md-7 {
      margin-right: 58.33333333%;
      margin-left: 0;
    }

    .offset-md-8 {
      margin-right: 66.66666667%;
      margin-left: 0;
    }

    .offset-md-9 {
      margin-right: 75%;
      margin-left: 0;
    }

    .offset-md-10 {
      margin-right: 83.33333333%;
      margin-left: 0;
    }

    .offset-md-11 {
      margin-right: 91.66666667%;
      margin-left: 0;
    }
  }
  @media (min-width: 992px) {
    .offset-lg-0 {
      margin-right: 0;
      margin-left: 0;
    }

    .offset-lg-1 {
      margin-right: 8.33333333%;
      margin-left: 0;
    }

    .offset-lg-2 {
      margin-right: 16.66666667%;
      margin-left: 0;
    }

    .offset-lg-3 {
      margin-right: 25%;
      margin-left: 0;
    }

    .offset-lg-4 {
      margin-right: 33.33333333%;
      margin-left: 0;
    }

    .offset-lg-5 {
      margin-right: 41.66666667%;
      margin-left: 0;
    }

    .offset-lg-6 {
      margin-right: 50%;
      margin-left: 0;
    }

    .offset-lg-7 {
      margin-right: 58.33333333%;
      margin-left: 0;
    }

    .offset-lg-8 {
      margin-right: 66.66666667%;
      margin-left: 0;
    }

    .offset-lg-9 {
      margin-right: 75%;
      margin-left: 0;
    }

    .offset-lg-10 {
      margin-right: 83.33333333%;
      margin-left: 0;
    }

    .offset-lg-11 {
      margin-right: 91.66666667%;
      margin-left: 0;
    }
  }
  @media (min-width: 1200px) {
    .offset-xl-0 {
      margin-right: 0;
      margin-left: 0;
    }

    .offset-xl-1 {
      margin-right: 8.33333333%;
      margin-left: 0;
    }

    .offset-xl-2 {
      margin-right: 16.66666667%;
      margin-left: 0;
    }

    .offset-xl-3 {
      margin-right: 25%;
      margin-left: 0;
    }

    .offset-xl-4 {
      margin-right: 33.33333333%;
      margin-left: 0;
    }

    .offset-xl-5 {
      margin-right: 41.66666667%;
      margin-left: 0;
    }

    .offset-xl-6 {
      margin-right: 50%;
      margin-left: 0;
    }

    .offset-xl-7 {
      margin-right: 58.33333333%;
      margin-left: 0;
    }

    .offset-xl-8 {
      margin-right: 66.66666667%;
      margin-left: 0;
    }

    .offset-xl-9 {
      margin-right: 75%;
      margin-left: 0;
    }

    .offset-xl-10 {
      margin-right: 83.33333333%;
      margin-left: 0;
    }

    .offset-xl-11 {
      margin-right: 91.66666667%;
      margin-left: 0;
    }
  }
  @media (min-width: 1400px) {
    .offset-xxl-0 {
      margin-right: 0;
      margin-left: 0;
    }

    .offset-xxl-1 {
      margin-right: 8.33333333%;
      margin-left: 0;
    }

    .offset-xxl-2 {
      margin-right: 16.66666667%;
      margin-left: 0;
    }

    .offset-xxl-3 {
      margin-right: 25%;
      margin-left: 0;
    }

    .offset-xxl-4 {
      margin-right: 33.33333333%;
      margin-left: 0;
    }

    .offset-xxl-5 {
      margin-right: 41.66666667%;
      margin-left: 0;
    }

    .offset-xxl-6 {
      margin-right: 50%;
      margin-left: 0;
    }

    .offset-xxl-7 {
      margin-right: 58.33333333%;
      margin-left: 0;
    }

    .offset-xxl-8 {
      margin-right: 66.66666667%;
      margin-left: 0;
    }

    .offset-xxl-9 {
      margin-right: 75%;
      margin-left: 0;
    }

    .offset-xxl-10 {
      margin-right: 83.33333333%;
      margin-left: 0;
    }

    .offset-xxl-11 {
      margin-right: 91.66666667%;
      margin-left: 0;
    }
  }
  .form-select {
    padding: 0.375rem 0.75rem 0.375rem 2.25rem;
    background-position: left 0.75rem center;
  }

  .form-select[multiple],
  .form-select[size]:not([size="1"]) {
    padding-left: 0.75rem;
    padding-right: inherit;
  }

  .form-select-sm {
    padding-right: 0.5rem;
    padding-left: inherit;
  }

  .form-select-lg {
    padding-right: 1rem;
    padding-left: inherit;
  }

  .form-check {
    padding-right: 1.5em;
    padding-left: inherit;
  }
  .form-check .form-check-input {
    float: right;
    margin-right: -1.5em;
    margin-left: 0;
  }
  .form-switch {
    padding-right: 2.5em;
    padding-left: inherit;
  }
  .form-switch .form-check-input {
    margin-right: -2.5em;
    margin-left: 0;
    background-position: right center;
    border-radius: 2em;
    transition: background-position 0.15s ease-in-out;
  }

  .form-switch .form-check-input:checked {
    background-position: left center;
  }

  .form-check-inline {
    margin-left: 1rem;
    margin-right: inherit;
  }

  .form-floating > label {
    right: 0;
    left: auto;
  }

  .input-group-lg > .form-select,
  .input-group-sm > .form-select {
    padding-left: 3rem;
    padding-right: inherit;
  }

  .input-group:not(.has-validation)
    > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu),
  .input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n + 3) {
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
  }
  .input-group.has-validation
    > :nth-last-child(n + 3):not(.dropdown-toggle):not(.dropdown-menu),
  .input-group.has-validation > .dropdown-toggle:nth-last-child(n + 4) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  .input-group
    > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(
      .valid-feedback
    ):not(.invalid-tooltip):not(.invalid-feedback) {
    margin-right: -1px;
    margin-left: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .was-validated .form-control:valid,
  .form-control.is-valid {
    padding-left: calc(1.5em + 0.75rem);
    padding-right: inherit;
    background-position: left calc(0.375em + 0.1875rem) center;
  }

  .was-validated textarea.form-control:valid,
  textarea.form-control.is-valid {
    padding-left: calc(1.5em + 0.75rem);
    padding-right: inherit;
    background-position: top calc(0.375em + 0.1875rem) left
      calc(0.375em + 0.1875rem);
  }
  .was-validated .form-select:valid:not([multiple]):not([size]),
  .was-validated .form-select:valid:not([multiple])[size="1"],
  .form-select.is-valid:not([multiple]):not([size]),
  .form-select.is-valid:not([multiple])[size="1"] {
    padding-left: 4.125rem;
    padding-right: inherit;
    background-position: left 0.75rem center, center left 2.25rem;
  }

  .form-check-inline .form-check-input ~ .valid-feedback {
    margin-right: 0.5em;
    margin-left: inherit;
  }

  .was-validated .form-control:invalid,
  .form-control.is-invalid {
    padding-left: calc(1.5em + 0.75rem);
    padding-right: inherit;
    background-position: left calc(0.375em + 0.1875rem) center;
  }

  .was-validated textarea.form-control:invalid,
  textarea.form-control.is-invalid {
    padding-left: calc(1.5em + 0.75rem);
    padding-right: inherit;
    background-position: top calc(0.375em + 0.1875rem) left
      calc(0.375em + 0.1875rem);
  }

  .was-validated .form-select:invalid:not([multiple]):not([size]),
  .was-validated .form-select:invalid:not([multiple])[size="1"],
  .form-select.is-invalid:not([multiple]):not([size]),
  .form-select.is-invalid:not([multiple])[size="1"] {
    padding-left: 4.125rem;
    padding-right: inherit;
    background-position: left 0.75rem center, center left 2.25rem;
  }

  .form-check-inline .form-check-input ~ .invalid-feedback {
    margin-right: 0.5em;
    margin-left: inherit;
  }

  .dropdown-toggle::after {
    margin-right: 0.255em;
    margin-left: 0;
  }
  .dropdown-toggle:empty::after {
    margin-right: 0;
    margin-left: 0;
  }

  .dropdown-menu {
    text-align: right;
    padding: 0;
  }

  .dropdown-menu[data-bs-popper] {
    left: auto;
    right: 0;
  }

  .dropdown-menu-start[data-bs-popper] {
    left: auto;
    right: 0;
  }

  .dropdown-menu-end[data-bs-popper] {
    left: 0;
    right: auto;
  }

  @media (min-width: 576px) {
    .dropdown-menu-sm-start[data-bs-popper] {
      left: auto;
      right: 0;
    }
    .dropdown-menu-sm-end[data-bs-popper] {
      left: 0;
      right: auto;
    }
  }
  @media (min-width: 768px) {
    .dropdown-menu-md-start[data-bs-popper] {
      left: auto;
      right: 0;
    }

    .dropdown-menu-md-end[data-bs-popper] {
      left: 0;
      right: auto;
    }
  }
  @media (min-width: 992px) {
    .dropdown-menu-lg-start[data-bs-popper] {
      left: auto;
      right: 0;
    }

    .dropdown-menu-lg-end[data-bs-popper] {
      left: 0;
      right: auto;
    }
  }
  @media (min-width: 1200px) {
    .dropdown-menu-xl-start[data-bs-popper] {
      left: auto;
      right: 0;
    }

    .dropdown-menu-xl-end[data-bs-popper] {
      left: 0;
      right: auto;
    }
  }
  @media (min-width: 1400px) {
    .dropdown-menu-xxl-start[data-bs-popper] {
      left: auto;
      right: 0;
    }

    .dropdown-menu-xxl-end[data-bs-popper] {
      left: 0;
      right: auto;
    }
  }

  .dropup .dropdown-toggle::after {
    margin-right: 0.255em;
    margin-left: 0;
    border-left: 0.3em solid transparent;
    border-right: 0.3em solid transparent;
  }
  .dropup .dropdown-toggle:empty::after {
    margin-right: 0;
    margin-left: 0;
  }

  .dropend .dropdown-menu[data-bs-popper] {
    left: auto;
    right: 100%;
    margin-right: 0.125rem;
    margin-left: 0;
  }
  .dropend .dropdown-toggle::after {
    margin-right: 0.255em;
    margin-left: 0;
    border-left: 0;
    border-right: 0.3em solid;
  }
  .dropend .dropdown-toggle:empty::after {
    margin-right: 0;
    margin-left: 0;
  }

  .dropstart .dropdown-menu[data-bs-popper] {
    left: 100%;
    right: auto;
    margin-left: 0.125rem;
    margin-right: 0;
  }
  .dropstart .dropdown-toggle::after {
    margin-right: 0.255em;
    margin-left: 0;
  }

  .dropstart .dropdown-toggle::before {
    margin-left: 0.255em;
    margin-right: 0;
    border-left: 0.3em solid;
    border-right: 0;
    border-bottom: 0.3em solid transparent;
  }
  .dropstart .dropdown-toggle:empty::after {
    margin-right: 0;
    margin-left: 0;
  }

  .btn-group > .btn:not(:first-child),
  .btn-group > .btn-group:not(:first-child) {
    margin-right: -1px;
    margin-left: 0;
  }
  .btn-group > .btn:not(:last-child):not(.dropdown-toggle),
  .btn-group > .btn-group:not(:last-child) > .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
  }
  .btn-group > .btn:nth-child(n + 3),
  .btn-group > :not(.btn-check) + .btn,
  .btn-group > .btn-group:not(:first-child) > .btn {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
  }

  .dropdown-toggle-split {
    padding-left: 0.5625rem;
    padding-right: 0.5625rem;
  }
  .dropdown-toggle-split::after,
  .dropup .dropdown-toggle-split::after,
  .dropend .dropdown-toggle-split::after {
    margin-left: 0;
    margin-right: 0;
  }
  .dropstart .dropdown-toggle-split::before {
    margin-right: 0;
    margin-left: 0;
  }

  .btn-sm + .dropdown-toggle-split,
  .btn-group-sm > .btn + .dropdown-toggle-split {
    padding-left: 0.375rem;
    padding-right: 0.375rem;
  }

  .btn-lg + .dropdown-toggle-split,
  .btn-group-lg > .btn + .dropdown-toggle-split {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .nav {
    padding-right: 0;
    padding-left: 0;
  }

  .navbar-brand {
    margin-left: 1rem;
    margin-right: 0;
  }
  .navbar-nav {
    padding-right: 0;
    padding-left: 0;
  }

  .card-link + .card-link {
    margin-right: 1rem;
    margin-left: 0;
  }

  @media (min-width: 576px) {
    .card-group > .card:not(:last-child) {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
    .card-group > .card:not(:last-child) .card-img-top,
    .card-group > .card:not(:last-child) .card-header {
      border-top-left-radius: 0;
    }
    .card-group > .card:not(:last-child) .card-img-bottom,
    .card-group > .card:not(:last-child) .card-footer {
      border-bottom-left-radius: 0;
    }
    .card-group > .card:not(:first-child) {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
    .card-group > .card:not(:first-child) .card-img-top,
    .card-group > .card:not(:first-child) .card-header {
      border-top-right-radius: 0;
    }
    .card-group > .card:not(:first-child) .card-img-bottom,
    .card-group > .card:not(:first-child) .card-footer {
      border-bottom-right-radius: 0;
    }
  }

  .accordion-button {
    text-align: right;
  }

  .accordion-button::after {
    margin-right: auto;
    margin-left: 0;
  }

  .accordion-item:first-of-type {
    border-top-right-radius: 0.25rem;
    border-top-left-radius: 0.25rem;
  }
  .accordion-item:first-of-type .accordion-button {
    border-top-right-radius: calc(0.25rem - 1px);
    border-top-left-radius: calc(0.25rem - 1px);
  }

  .accordion-item:last-of-type {
    border-bottom-left-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
  }
  .accordion-item:last-of-type .accordion-button.collapsed {
    border-bottom-left-radius: calc(0.25rem - 1px);
    border-bottom-right-radius: calc(0.25rem - 1px);
  }
  .accordion-item:last-of-type .accordion-collapse {
    border-bottom-left-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
  }
  .breadcrumb {
    padding-right: 0;
    padding-left: 0;
  }
  .breadcrumb-item + .breadcrumb-item {
    padding: 0;
  }
  .breadcrumb-item + .breadcrumb-item::before {
    float: right;
    padding-left: 0.5rem;
    content: var(--bs-breadcrumb-divider, ">");
  }

  .pagination {
    padding-right: 0;
    padding-left: 0;
  }

  .page-item:not(:first-child) .page-link {
    margin-right: -1px;
    margin-left: -1px;
  }

  .page-item:first-child .page-link,
  .page-item:last-child .page-link,
  .pagination-lg .page-item:first-child .page-link,
  .pagination-lg .page-item:last-child .page-link,
  .pagination-sm .page-item:first-child .page-link,
  .pagination-sm .page-item:last-child .page-link {
    border-radius: 0;
  }
  .alert-dismissible {
    padding-left: 3rem;
    padding-right: 1rem;
  }
  .alert-dismissible .btn-close {
    left: 0;
    right: auto;
  }

  .progress-bar-striped {
    background-image: linear-gradient(
      -45deg,
      rgba(255, 255, 255, 0.15) 25%,
      transparent 25%,
      transparent 50%,
      rgba(255, 255, 255, 0.15) 50%,
      rgba(255, 255, 255, 0.15) 75%,
      transparent 75%,
      transparent
    );
    background-size: 1rem 1rem;
  }

  .list-group {
    padding-right: 0;
    padding-left: 0;
  }

  .list-group-numbered > li::before {
    content: counters(section, ".") ". ";
    counter-increment: section;
  }

  .list-group-item:first-child {
    border-top-right-radius: inherit;
    border-top-left-radius: inherit;
  }
  .list-group-item:last-child {
    border-bottom-left-radius: inherit;
    border-bottom-right-radius: inherit;
  }
  .list-group-item.disabled,
  .list-group-item:disabled {
    color: #6c757d;
    pointer-events: none;
    background-color: #fff;
  }

  .list-group-horizontal > .list-group-item:first-child {
    border-bottom-right-radius: 0.25rem;
    border-top-left-radius: 0;
  }
  .list-group-horizontal > .list-group-item:last-child {
    border-top-left-radius: 0.25rem;
    border-bottom-right-radius: 0;
  }

  .list-group-horizontal > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-right-width: 0;
  }
  .list-group-horizontal > .list-group-item + .list-group-item.active {
    margin-right: -1px;
    border-right-width: 1px;
  }

  @media (min-width: 576px) {
    .list-group-horizontal-sm > .list-group-item:first-child {
      border-bottom-right-radius: 0.25rem;
      border-top-left-radius: 0;
    }
    .list-group-horizontal-sm > .list-group-item:last-child {
      border-top-left-radius: 0.25rem;
      border-bottom-right-radius: 0;
    }

    .list-group-horizontal-sm > .list-group-item + .list-group-item {
      border-top-width: 1px;
      border-right-width: 0;
    }
    .list-group-horizontal-sm > .list-group-item + .list-group-item.active {
      margin-right: -1px;
      border-right-width: 1px;
    }
  }
  @media (min-width: 768px) {
    .list-group-horizontal-md > .list-group-item:first-child {
      border-bottom-right-radius: 0.25rem;
      border-top-left-radius: 0;
    }
    .list-group-horizontal-md > .list-group-item:last-child {
      border-top-left-radius: 0.25rem;
      border-bottom-right-radius: 0;
    }
    .list-group-horizontal-md > .list-group-item + .list-group-item {
      border-top-width: 1px;
      border-right-width: 0;
    }
    .list-group-horizontal-md > .list-group-item + .list-group-item.active {
      margin-right: -1px;
      border-right-width: 1px;
    }
  }
  @media (min-width: 992px) {
    .list-group-horizontal-lg > .list-group-item:first-child {
      border-bottom-right-radius: 0.25rem;
      border-top-left-radius: 0;
    }
    .list-group-horizontal-lg > .list-group-item:last-child {
      border-top-left-radius: 0.25rem;
      border-bottom-right-radius: 0;
    }
    .list-group-horizontal-lg > .list-group-item + .list-group-item {
      border-top-width: 1px;
      border-right-width: 0;
    }
    .list-group-horizontal-lg > .list-group-item + .list-group-item.active {
      margin-right: -1px;
      border-right-width: 1px;
    }
  }
  @media (min-width: 1200px) {
    .list-group-horizontal-xl > .list-group-item:first-child {
      border-bottom-right-radius: 0.25rem;
      border-top-left-radius: 0;
    }
    .list-group-horizontal-xl > .list-group-item:last-child {
      border-top-left-radius: 0.25rem;
      border-bottom-right-radius: 0;
    }
    .list-group-horizontal-xl > .list-group-item + .list-group-item {
      border-top-width: 1px;
      border-right-width: 0;
    }
    .list-group-horizontal-xl > .list-group-item + .list-group-item.active {
      margin-right: -1px;
      border-right-width: 1px;
    }
  }
  @media (min-width: 1400px) {
    .list-group-horizontal-xxl > .list-group-item:first-child {
      border-bottom-right-radius: 0.25rem;
      border-top-left-radius: 0;
    }
    .list-group-horizontal-xxl > .list-group-item:last-child {
      border-top-left-radius: 0.25rem;
      border-bottom-right-radius: 0;
    }
    .list-group-horizontal-xxl > .list-group-item + .list-group-item {
      border-top-width: 1px;
      border-right-width: 0;
    }
    .list-group-horizontal-xxl > .list-group-item + .list-group-item.active {
      margin-right: -1px;
      border-right-width: 1px;
    }
  }

  .toast-header .btn-close {
    margin-left: -0.375rem;
    margin-right: 0.75rem;
  }

  .modal {
    left: auto;
    right: 0;
  }

  .modal-backdrop {
    left: auto;
    right: 0;
  }

  .modal-header .btn-close {
    margin: -0.5rem auto -0.5rem -0.5rem;
  }

  .tooltip {
    text-align: right;
    text-align: start;
  }

  .bs-tooltip-end .tooltip-arrow,
  .bs-tooltip-auto[data-popper-placement^="right"] .tooltip-arrow {
    right: 0;
    left: auto;
  }
  .bs-tooltip-end .tooltip-arrow::before,
  .bs-tooltip-auto[data-popper-placement^="right"] .tooltip-arrow::before {
    left: -1px;
    right: auto;
  }

  .bs-tooltip-start .tooltip-arrow,
  .bs-tooltip-auto[data-popper-placement^="left"] .tooltip-arrow {
    left: 0;
    right: auto;
  }
  .bs-tooltip-start .tooltip-arrow::before,
  .bs-tooltip-auto[data-popper-placement^="left"] .tooltip-arrow::before {
    right: -1px;
    border-width: 0.4rem 0.4rem 0.4rem 0;
    left: auto;
  }

  .popover {
    right: auto;
    left: 0;
    //font-family: var(--bs-font-sans-serif);
    text-align: right;
    text-align: start;
    word-wrap: break-word;
  }

  .bs-popover-end > .popover-arrow,
  .bs-popover-auto[data-popper-placement^="right"] > .popover-arrow {
    right: calc(-0.5rem - 1px);
    left: auto;
  }
  .bs-popover-end > .popover-arrow::before,
  .bs-popover-auto[data-popper-placement^="right"] > .popover-arrow::before {
    right: 0;
    border-width: 0.5rem 0 0.5rem 0.5rem;
    border-left-color: rgba(0, 0, 0, 0.25);
    left: auto;
  }
  .bs-popover-end > .popover-arrow::after,
  .bs-popover-auto[data-popper-placement^="right"] > .popover-arrow::after {
    right: 1px;
    border-width: 0.5rem 0 0.5rem 0.5rem;
    border-left-color: #fff;
    left: auto;
  }

  .bs-popover-bottom .popover-header::before,
  .bs-popover-auto[data-popper-placement^="bottom"] .popover-header::before {
    right: 50%;
    margin-right: -0.5rem;
  }

  .bs-popover-start > .popover-arrow,
  .bs-popover-auto[data-popper-placement^="left"] > .popover-arrow {
    left: calc(-0.5rem - 1px);
    right: auto;
  }
  .bs-popover-start > .popover-arrow::before,
  .bs-popover-auto[data-popper-placement^="left"] > .popover-arrow::before {
    left: 0;
    border-right-color: rgba(0, 0, 0, 0.25);
  }
  .bs-popover-start > .popover-arrow::after,
  .bs-popover-auto[data-popper-placement^="left"] > .popover-arrow::after {
    right: auto;
    left: 1px;
    border-width: 0.5rem 0.5rem 0.5rem 0;
    border-right-color: #fff;
  }

  .carousel-item {
    float: right;
    margin-left: -100%;
    margin-right: 0;
  }

  .carousel-item-next:not(.carousel-item-start),
  .active.carousel-item-end {
    transform: translateX(100%);
  }

  .carousel-item-prev:not(.carousel-item-end),
  .active.carousel-item-start {
    transform: translateX(-100%);
  }

  .carousel-control-prev {
    transform: rotate(180deg);
    right: 0;
    left: auto;
  }

  .carousel-control-next {
    transform: rotate(180deg);
    left: 0;
    right: auto;
  }

  .carousel-indicators [data-bs-target] {
    margin-left: 3px;
    margin-right: 3px;
    text-indent: -999px;
  }

  .offcanvas-header .btn-close {
    margin-left: -0.5rem;
    margin-right: inherit;
  }

  .offcanvas-start {
    left: auto;
    right: 0;
    border-left: 1px solid rgba(0, 0, 0, 0.2);
    border-right: 0;
    transform: translateX(100%);
  }

  .offcanvas-end {
    right: auto;
    left: 0;
    border-right: 1px solid rgba(0, 0, 0, 0.2);
    border-left: 0;
    transform: translateX(-100%);
  }

  .ratio > * {
    right: 0;
    left: auto;
  }

  .float-start {
    float: right !important;
  }

  .float-end {
    float: left !important;
  }

  .start-0 {
    right: 0 !important;
    left: auto !important;
  }

  .start-50 {
    right: 50% !important;
    left: auto !important;
  }

  .start-100 {
    right: 100% !important;
    left: auto !important;
  }

  .end-0 {
    left: 0 !important;
    right: auto !important;
  }

  .end-50 {
    left: 50% !important;
    right: auto !important;
  }

  .end-100 {
    left: 100% !important;
    right: auto !important;
  }

  .border-end {
    border-left: 1px solid #dee2e6 !important;
    border-right: inherit !important;
  }

  .border-end-0 {
    border-left: 0 !important;
    border-right: inherit0 !important;
  }

  .border-start {
    border-right: 1px solid #dee2e6 !important;
    border-left: inherit !important;
  }

  .border-start-0 {
    border-right: 0 !important;
    border-left: inherit !important;
  }

  .me-0 {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  .me-1 {
    margin-left: 0.25rem !important;
    margin-right: 0 !important;
  }

  .me-2 {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
  }

  .me-3 {
    margin-left: 1rem !important;
    margin-right: 0 !important;
  }

  .me-4 {
    margin-left: 1.5rem !important;
    margin-right: 0 !important;
  }

  .me-5 {
    margin-left: 3rem !important;
    margin-right: 0 !important;
  }

  .me-auto {
    margin-left: auto !important;
    margin-right: 0 !important;
  }

  .ms-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  .ms-1 {
    margin-right: 0.25rem !important;
    margin-left: 0 !important;
  }

  .ms-2 {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
  }

  .ms-3 {
    margin-right: 1rem !important;
    margin-left: 0 !important;
  }

  .ms-4 {
    margin-right: 1.5rem !important;
    margin-left: 0 !important;
  }

  .ms-5 {
    margin-right: 3rem !important;
    margin-left: 0 !important;
  }

  .ms-auto {
    margin-right: auto !important;
    margin-left: 0 !important;
  }

  .pe-0 {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  .pe-1 {
    padding-left: 0.25rem !important;
    padding-right: 0 !important;
  }

  .pe-2 {
    padding-left: 0.5rem !important;
    padding-right: 0 !important;
  }

  .pe-3 {
    padding-left: 1rem !important;
    padding-right: 0 !important;
  }

  .pe-4 {
    padding-left: 1.5rem !important;
    padding-right: 0 !important;
  }

  .pe-5 {
    padding-left: 3rem !important;
    padding-right: 0 !important;
  }

  .ps-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }

  .ps-1 {
    padding-right: 0.25rem !important;
    padding-left: 0 !important;
  }

  .ps-2 {
    padding-right: 0.5rem !important;
    padding-left: 0 !important;
  }

  .ps-3 {
    padding-right: 1rem !important;
    padding-left: 0 !important;
  }

  .ps-4 {
    padding-right: 1.5rem !important;
    padding-left: 0 !important;
  }

  .ps-5 {
    padding-right: 3rem !important;
    padding-left: 0 !important;
  }

  .text-start {
    text-align: right !important;
  }

  .text-end {
    text-align: left !important;
  }

  .rounded-end {
    border-top-left-radius: 0.25rem !important;
    border-bottom-left-radius: 0.25rem !important;
  }

  .rounded-start {
    border-bottom-right-radius: 0.25rem !important;
    border-top-right-radius: 0.25rem !important;
  }

  @media (min-width: 576px) {
    .float-sm-start {
      float: right !important;
    }

    .float-sm-end {
      float: left !important;
    }

    .me-sm-0 {
      margin-left: 0 !important;
      margin-right: 0 !important;
    }

    .me-sm-1 {
      margin-left: 0.25rem !important;
      margin-right: 0 !important;
    }

    .me-sm-2 {
      margin-left: 0.5rem !important;
      margin-right: 0 !important;
    }

    .me-sm-3 {
      margin-left: 1rem !important;
      margin-right: 0 !important;
    }

    .me-sm-4 {
      margin-left: 1.5rem !important;
      margin-right: 0 !important;
    }

    .me-sm-5 {
      margin-left: 3rem !important;
      margin-right: 0 !important;
    }

    .me-sm-auto {
      margin-left: auto !important;
      margin-right: 0 !important;
    }

    .ms-sm-0 {
      margin-right: 0 !important;
      margin-left: 0 !important;
    }

    .ms-sm-1 {
      margin-right: 0.25rem !important;
      margin-left: 0 !important;
    }

    .ms-sm-2 {
      margin-right: 0.5rem !important;
      margin-left: 0 !important;
    }

    .ms-sm-3 {
      margin-right: 1rem !important;
      margin-left: 0 !important;
    }

    .ms-sm-4 {
      margin-right: 1.5rem !important;
      margin-left: 0 !important;
    }

    .ms-sm-5 {
      margin-right: 3rem !important;
      margin-left: 0 !important;
    }

    .ms-sm-auto {
      margin-right: auto !important;
      margin-left: 0 !important;
    }

    .pe-sm-0 {
      padding-left: 0 !important;
      padding-right: 0 !important;
    }

    .pe-sm-1 {
      padding-left: 0.25rem !important;
      padding-right: 0 !important;
    }

    .pe-sm-2 {
      padding-left: 0.5rem !important;
      padding-right: 0 !important;
    }

    .pe-sm-3 {
      padding-left: 1rem !important;
      padding-right: 0 !important;
    }

    .pe-sm-4 {
      padding-left: 1.5rem !important;
      padding-right: 0 !important;
    }

    .pe-sm-5 {
      padding-left: 3rem !important;
      padding-right: 0 !important;
    }

    .ps-sm-0 {
      padding-right: 0 !important;
      padding-left: 0 !important;
    }

    .ps-sm-1 {
      padding-right: 0.25rem !important;
      padding-left: 0 !important;
    }

    .ps-sm-2 {
      padding-right: 0.5rem !important;
      padding-left: 0 !important;
    }

    .ps-sm-3 {
      padding-right: 1rem !important;
      padding-left: 0 !important;
    }

    .ps-sm-4 {
      padding-right: 1.5rem !important;
      padding-left: 0 !important;
    }

    .ps-sm-5 {
      padding-right: 3rem !important;
      padding-left: 0 !important;
    }

    .text-sm-start {
      text-align: right !important;
    }

    .text-sm-end {
      text-align: left !important;
    }
  }
  @media (min-width: 768px) {
    .float-md-start {
      float: right !important;
    }

    .float-md-end {
      float: left !important;
    }

    .float-md-none {
      float: none !important;
    }

    .me-md-0 {
      margin-left: 0 !important;
      margin-right: 0 !important;
    }

    .me-md-1 {
      margin-left: 0.25rem !important;
      margin-right: 0 !important;
    }

    .me-md-2 {
      margin-left: 0.5rem !important;
      margin-right: 0 !important;
    }

    .me-md-3 {
      margin-left: 1rem !important;
      margin-right: 0 !important;
    }

    .me-md-4 {
      margin-left: 1.5rem !important;
      margin-right: 0 !important;
    }

    .me-md-5 {
      margin-left: 3rem !important;
      margin-right: 0 !important;
    }

    .me-md-auto {
      margin-left: auto !important;
      margin-right: 0 !important;
    }

    .ms-md-0 {
      margin-right: 0 !important;
      margin-left: 0 !important;
    }

    .ms-md-1 {
      margin-right: 0.25rem !important;
      margin-left: 0 !important;
    }

    .ms-md-2 {
      margin-right: 0.5rem !important;
      margin-left: 0 !important;
    }

    .ms-md-3 {
      margin-right: 1rem !important;
      margin-left: 0 !important;
    }

    .ms-md-4 {
      margin-right: 1.5rem !important;
      margin-left: 0 !important;
    }

    .ms-md-5 {
      margin-right: 3rem !important;
      margin-left: 0 !important;
    }

    .ms-md-auto {
      margin-right: auto !important;
      margin-left: 0 !important;
    }

    .pe-md-0 {
      padding-left: 0 !important;
      padding-right: 0 !important;
    }

    .pe-md-1 {
      padding-left: 0.25rem !important;
      padding-right: 0 !important;
    }

    .pe-md-2 {
      padding-left: 0.5rem !important;
      padding-right: 0 !important;
    }

    .pe-md-3 {
      padding-left: 1rem !important;
      padding-right: 0 !important;
    }

    .pe-md-4 {
      padding-left: 1.5rem !important;
      padding-right: 0 !important;
    }

    .pe-md-5 {
      padding-left: 3rem !important;
      padding-right: 0 !important;
    }

    .ps-md-0 {
      padding-right: 0 !important;
      padding-left: 0 !important;
    }

    .ps-md-1 {
      padding-right: 0.25rem !important;
      padding-left: 0 !important;
    }

    .ps-md-2 {
      padding-right: 0.5rem !important;
      padding-left: 0 !important;
    }

    .ps-md-3 {
      padding-right: 1rem !important;
      padding-left: 0 !important;
    }

    .ps-md-4 {
      padding-right: 1.5rem !important;
      padding-left: 0 !important;
    }

    .ps-md-5 {
      padding-right: 3rem !important;
      padding-left: 0 !important;
    }

    .text-md-start {
      text-align: right !important;
    }

    .text-md-end {
      text-align: left !important;
    }
  }
  @media (min-width: 992px) {
    .float-lg-start {
      float: right !important;
    }

    .float-lg-end {
      float: left !important;
    }

    .float-lg-none {
      float: none !important;
    }

    .me-lg-0 {
      margin-left: 0 !important;
      margin-right: 0 !important;
    }

    .me-lg-1 {
      margin-left: 0.25rem !important;
      margin-right: 0 !important;
    }

    .me-lg-2 {
      margin-left: 0.5rem !important;
      margin-right: 0 !important;
    }

    .me-lg-3 {
      margin-left: 1rem !important;
      margin-right: 0 !important;
    }

    .me-lg-4 {
      margin-left: 1.5rem !important;
      margin-right: 0 !important;
    }

    .me-lg-5 {
      margin-left: 3rem !important;
      margin-right: 0 !important;
    }

    .me-lg-auto {
      margin-left: auto !important;
      margin-right: 0 !important;
    }

    .ms-lg-0 {
      margin-right: 0 !important;
      margin-left: 0 !important;
    }

    .ms-lg-1 {
      margin-right: 0.25rem !important;
      margin-left: 0 !important;
    }

    .ms-lg-2 {
      margin-right: 0.5rem !important;
      margin-left: 0 !important;
    }

    .ms-lg-3 {
      margin-right: 1rem !important;
      margin-left: 0 !important;
    }

    .ms-lg-4 {
      margin-right: 1.5rem !important;
      margin-left: 0 !important;
    }

    .ms-lg-5 {
      margin-right: 3rem !important;
      margin-left: 0 !important;
    }

    .ms-lg-auto {
      margin-right: auto !important;
      margin-left: 0 !important;
    }

    .pe-lg-0 {
      padding-left: 0 !important;
      padding-right: 0 !important;
    }

    .pe-lg-1 {
      padding-left: 0.25rem !important;
      padding-right: 0 !important;
    }

    .pe-lg-2 {
      padding-left: 0.5rem !important;
      padding-right: 0 !important;
    }

    .pe-lg-3 {
      padding-left: 1rem !important;
      padding-right: 0 !important;
    }

    .pe-lg-4 {
      padding-left: 1.5rem !important;
      padding-right: 0 !important;
    }

    .pe-lg-5 {
      padding-left: 3rem !important;
      padding-right: 0 !important;
    }

    .ps-lg-0 {
      padding-right: 0 !important;
      padding-left: 0 !important;
    }

    .ps-lg-1 {
      padding-right: 0.25rem !important;
      padding-left: 0 !important;
    }

    .ps-lg-2 {
      padding-right: 0.5rem !important;
      padding-left: 0 !important;
    }

    .ps-lg-3 {
      padding-right: 1rem !important;
      padding-left: 0 !important;
    }

    .ps-lg-4 {
      padding-right: 1.5rem !important;
      padding-left: 0 !important;
    }

    .ps-lg-5 {
      padding-right: 3rem !important;
      padding-left: 0 !important;
    }

    .text-lg-start {
      text-align: right !important;
    }

    .text-lg-end {
      text-align: left !important;
    }
  }
  @media (min-width: 1200px) {
    .me-xl-0 {
      margin-left: 0 !important;
      margin-right: 0 !important;
    }

    .me-xl-1 {
      margin-left: 0.25rem !important;
      margin-right: 0 !important;
    }

    .me-xl-2 {
      margin-left: 0.5rem !important;
      margin-right: 0 !important;
    }

    .me-xl-3 {
      margin-left: 1rem !important;
      margin-right: 0 !important;
    }

    .me-xl-4 {
      margin-left: 1.5rem !important;
      margin-right: 0 !important;
    }

    .me-xl-5 {
      margin-left: 3rem !important;
      margin-right: 0 !important;
    }

    .me-xl-auto {
      margin-left: auto !important;
      margin-right: 0 !important;
    }

    .mb-xl-0 {
      margin-bottom: 0 !important;
    }

    .mb-xl-1 {
      margin-bottom: 0.25rem !important;
    }

    .mb-xl-2 {
      margin-bottom: 0.5rem !important;
    }

    .mb-xl-3 {
      margin-bottom: 1rem !important;
    }

    .mb-xl-4 {
      margin-bottom: 1.5rem !important;
    }

    .mb-xl-5 {
      margin-bottom: 3rem !important;
    }

    .mb-xl-auto {
      margin-bottom: auto !important;
    }

    .ms-xl-0 {
      margin-right: 0 !important;
      margin-left: 0 !important;
    }

    .ms-xl-1 {
      margin-right: 0.25rem !important;
      margin-left: 0 !important;
    }

    .ms-xl-2 {
      margin-right: 0.5rem !important;
      margin-left: 0 !important;
    }

    .ms-xl-3 {
      margin-right: 1rem !important;
      margin-left: 0 !important;
    }

    .ms-xl-4 {
      margin-right: 1.5rem !important;
      margin-left: 0 !important;
    }

    .ms-xl-5 {
      margin-right: 3rem !important;
      margin-left: 0 !important;
    }

    .ms-xl-auto {
      margin-right: auto !important;
      margin-left: 0 !important;
    }

    .px-xl-0 {
      padding-left: 0 !important;
      padding-right: 0 !important;
    }

    .px-xl-1 {
      padding-left: 0.25rem !important;
      padding-right: 0.25rem !important;
    }

    .px-xl-2 {
      padding-left: 0.5rem !important;
      padding-right: 0.5rem !important;
    }

    .px-xl-3 {
      padding-left: 1rem !important;
      padding-right: 1rem !important;
    }

    .px-xl-4 {
      padding-left: 1.5rem !important;
      padding-right: 1.5rem !important;
    }

    .px-xl-5 {
      padding-left: 3rem !important;
      padding-right: 3rem !important;
    }

    .pe-xl-0 {
      padding-left: 0 !important;
      padding-right: 0 !important;
    }

    .pe-xl-1 {
      padding-left: 0.25rem !important;
      padding-right: 0 !important;
    }

    .pe-xl-2 {
      padding-left: 0.5rem !important;
      padding-right: 0 !important;
    }

    .pe-xl-3 {
      padding-left: 1rem !important;
      padding-right: 0 !important;
    }

    .pe-xl-4 {
      padding-left: 1.5rem !important;
      padding-right: 0 !important;
    }

    .pe-xl-5 {
      padding-left: 3rem !important;
      padding-right: 0 !important;
    }

    .ps-xl-0 {
      padding-right: 0 !important;
      padding-left: 0 !important;
    }

    .ps-xl-1 {
      padding-right: 0.25rem !important;
      padding-left: 0 !important;
    }

    .ps-xl-2 {
      padding-right: 0.5rem !important;
      padding-left: 0 !important;
    }

    .ps-xl-3 {
      padding-right: 1rem !important;
      padding-left: 0 !important;
    }

    .ps-xl-4 {
      padding-right: 1.5rem !important;
      padding-left: 0 !important;
    }

    .ps-xl-5 {
      padding-right: 3rem !important;
      padding-left: 0 !important;
    }

    .text-xl-start {
      text-align: right !important;
    }

    .text-xl-end {
      text-align: left !important;
    }
  }
  @media (min-width: 1400px) {
    .float-xxl-start {
      float: right !important;
    }

    .float-xxl-end {
      float: left !important;
    }

    .me-xxl-0 {
      margin-left: 0 !important;
      margin-right: 0 !important;
    }

    .me-xxl-1 {
      margin-left: 0.25rem !important;
      margin-right: 0 !important;
    }

    .me-xxl-2 {
      margin-left: 0.5rem !important;
      margin-right: 0 !important;
    }

    .me-xxl-3 {
      margin-left: 1rem !important;
      margin-right: 0 !important;
    }

    .me-xxl-4 {
      margin-left: 1.5rem !important;
      margin-right: 0 !important;
    }

    .me-xxl-5 {
      margin-left: 3rem !important;
      margin-right: 0 !important;
    }

    .me-xxl-auto {
      margin-left: auto !important;
      margin-right: 0 !important;
    }

    .ms-xxl-0 {
      margin-right: 0 !important;
      margin-left: 0 !important;
    }

    .ms-xxl-1 {
      margin-right: 0.25rem !important;
      margin-left: 0 !important;
    }

    .ms-xxl-2 {
      margin-right: 0.5rem !important;
      margin-left: 0 !important;
    }

    .ms-xxl-3 {
      margin-right: 1rem !important;
      margin-left: 0 !important;
    }

    .ms-xxl-4 {
      margin-right: 1.5rem !important;
      margin-left: 0 !important;
    }

    .ms-xxl-5 {
      margin-right: 3rem !important;
      margin-left: 0 !important;
    }

    .ms-xxl-auto {
      margin-right: auto !important;
      margin-left: 0 !important;
    }

    .pe-xxl-0 {
      padding-left: 0 !important;
      padding-right: 0 !important;
    }

    .pe-xxl-1 {
      padding-left: 0.25rem !important;
      padding-right: 0 !important;
    }

    .pe-xxl-2 {
      padding-left: 0.5rem !important;
      padding-right: 0 !important;
    }

    .pe-xxl-3 {
      padding-left: 1rem !important;
      padding-right: 0 !important;
    }

    .pe-xxl-4 {
      padding-left: 1.5rem !important;
      padding-right: 0 !important;
    }

    .pe-xxl-5 {
      padding-left: 3rem !important;
      padding-right: 0 !important;
    }

    .ps-xxl-0 {
      padding-right: 0 !important;
      padding-left: 0 !important;
    }

    .ps-xxl-1 {
      padding-right: 0.25rem !important;
      padding-left: 0 !important;
    }

    .ps-xxl-2 {
      padding-right: 0.5rem !important;
      padding-left: 0 !important;
    }

    .ps-xxl-3 {
      padding-right: 1rem !important;
      padding-left: 0 !important;
    }

    .ps-xxl-4 {
      padding-right: 1.5rem !important;
      padding-left: 0 !important;
    }

    .ps-xxl-5 {
      padding-right: 3rem !important;
      padding-left: 0 !important;
    }

    .text-xxl-start {
      text-align: right !important;
    }

    .text-xxl-end {
      text-align: left !important;
    }
  }
  .float-left {
    float: right;
  }
  .float-right {
    float: left;
  }
}
