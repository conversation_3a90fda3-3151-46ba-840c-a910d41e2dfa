<div class="mk-wrapper d-flex min-vh-100">
  <app-side-navbar />

  <div class="w-100">
    <header class="position-relative z-3">
      <nav class="d-block d-md-none">
        <div class="d-flex justify-content-between py-3 mx-3 border-bottom">
          <div>
            <a class="text-white me-4 d-flex d-xxl-none" href="javascript:;">
              <svg-icon src="../../assets/svg/hamburger_mobile.svg"></svg-icon>
            </a>
          </div>
          <div>
            <p class="fw-bold mb-0">المساند</p>
          </div>
          <div>
            <a
              class="nav-link dropdown-toggle text-white"
              href="#"
              role="button"
              data-bs-toggle="dropdown"
              aria-expanded="false"
            >
              <svg-icon src="../../assets/svg/user-circle.svg"></svg-icon>
            </a>
            <ul class="dropdown-menu dropdown-menu-end">
              <li><a class="dropdown-item" href="#">Another action</a></li>
              <li>
                <a class="dropdown-item" href="#">Something else here</a>
              </li>
            </ul>
          </div>
        </div>
      </nav>
      <div class="container my-4 d-none d-md-block">
        <app-navigation-header />
      </div>
    </header>

    <main class="d-flex flex-grow-1 justify-content-center align-items-center">
      <div class="container">
        <div
          class="text-center d-flex justify-content-center align-items-center min-vh-50"
        >
          <div
            class="enhanced-login-form p-md-4 m-md-3 p-3 border rounded shadow bg-white"
          >
            <form
              [formGroup]="controls.form"
              (ngSubmit)="onLogin()"
              class="d-flex flex-column gap-3"
            >
              <div class="mb-3">
                <label for="email" class="form-label fw-semibold"
                  >البريد الإلكتروني</label
                >
                <div class="input-group enhanced-input-group">
                  <input
                    id="email"
                    class="form-control enhanced-input"
                    type="email"
                    [formControlName]="controls.email.key"
                    placeholder="أدخل بريدك الإلكتروني"
                    required
                  />
                  <span class="input-group-text enhanced-input-icon">
                    <i class="pi pi-envelope"></i>
                  </span>
                </div>
                <lib-form-control-validations
                  [formCtrl]="controls.email"
                  label="البريد الإلكتروني"
                ></lib-form-control-validations>
              </div>

              <div class="d-grid gap-2">
                <button
                  type="submit"
                  class="btn btn-primary btn-lg"
                  [disabled]="controls.isInvalid"
                >
                  <i class="pi pi-sign-in me-2"></i>
                  تسجيل الدخول
                </button>
                <button
                  type="button"
                  (click)="backToHomePage()"
                  class="btn btn-outline-secondary"
                >
                  <i class="pi pi-arrow-left me-2"></i>
                  العودة للصفحة الرئيسية
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </main>

    <footer>
      <div class="container">
        <div
          class="d-flex justify-content-between align-items-center mt-4 border-top py-4 flex-column flex-md-row"
        >
          <div>جميع الحقوق محفوظه لصالح ديوان المظالم السعودية</div>
          <div class="d-flex justify-content-between align-items-center">
            <a class="mx-2 fw-bold text-muted" aria-label="السياسات"
              >السياسات</a
            >
            <div class="mx-2 fw-bold text-muted">الدعم</div>
          </div>
        </div>
      </div>
    </footer>
  </div>
</div>
