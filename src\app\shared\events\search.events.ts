// import { SearchFeaturesPublicDataInner } from "../../services";
import { SearchTypeEnum } from "../models/search-type.enum";
import { SystemContinuesEvent, SystemEvent } from "./event";

export interface SearchFeaturesPublicDataInner {
    id: any;
    caseId: any;
    category: any;
    subCategory: any;
    text: Text;
    verdict: any;
    rules: any;
    keywords: any;
    summary: any;
    auditVerdictNumber: any;
    auditVerdictDate: any;
    hearingDate: any;
    verdictNumber: any;
    verdictDate: any;
    namedEntities: any;
    lawCategorySubcategory: any;
    lawCategory: any;
    lawSubcategory: any;
    lawText: any;
    groupedKeywords: any;
    groupedEntities: any;
    clean_txt: any;
    bonds: any;
    score: any;
    HadithNum: any;
    BookNum: any;
    BookName: any;
    Narrated: any;
    Hadith: any;
    Collector: any;
    Grad: any;
    strip_hadith: any;
    Category: any;
    Subcategory: any;
    SystemEditionToolsPath: any;
    SystemEditionToolsResolution: any;
    Status: any;
    DateOfPublication: any;
    DateOfRelease: any;
    SystemBrief: any;
    SystemTextHeader: any;
    SystemTextIntro: any;
    Section: any;
    Article: any;
    Content: any;
    Changes: any;
    ChangesContent: any;
    ChangesDataArticleId: any;
    ChangesHref: any;
    Deleted: any;
    SectionNum: any;
    SectionName: any;
    ChapterNum: any;
    ChapterName: any;
    ArticleNum: any;
    ArticleName: any;
    article_text: any;
    courtOfFirstInstance: any;
    resolutionDate: any;
    resolutionNumber: any;
    actDate: any;
    actNumber: any;
    appealCourt: any;
    appealResolutionDate: any;
    appealResolutionId: any;
    caseYear: any;
    surahName: any;
    surahType: any;
    aboutSurah: any;
    aya: any;
    ayaNumber: any;
    ayaExplanation: any;
    stripSurahName: any;
    stripSurahType: any;
    stripAboutSurah: any;
    stripAya: any;
    stripAyaExplanation: any;
}
export namespace SearchEvents {
    /**
     * @description this event is fired when the user start the search
     */
    export const SearchStarted = new SystemContinuesEvent<SearchStartedMessage>('SearchStarted');

    export interface SearchStartedMessage {
        text: string,
        searchType: SearchTypeEnum
        mainCategory: string,
        subCategory: string
    }


    /**
     * @description this event is fired when the user start the search
     */
    export const SearchCaseSelected = new SystemContinuesEvent<SearchFeaturesPublicDataInner>('SearchCaseSelected');

    export const SearchCategorySelected =
        new SystemEvent<{
            searchType: SearchTypeEnum
            mainCategory: string,
            subCategory: string
        }>('SearchCategorySelected');


    /**
     * @description this event is fired when the user start the regular search
     */
    export const RegularSearchTriggered = new SystemEvent<string>('RegularSearchTriggered');

}