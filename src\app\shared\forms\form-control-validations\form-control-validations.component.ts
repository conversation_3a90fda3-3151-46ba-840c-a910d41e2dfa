import { Component, Input } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';

import { TranslateModule } from '@ngx-translate/core';
import { FormFieldErrorMessageComponent } from '../form-error-message/form-field-error-message.component';
import { CustomFormControl } from '../custom-form-control.model';

@Component({
  selector: 'lib-form-control-validations',
  standalone: true,
  templateUrl: './form-control-validations.component.html',
  imports: [FormFieldErrorMessageComponent, TranslateModule]
})
export class FormControlValidationsComponent {

  @Input() formCtrl!: CustomFormControl;
  @Input() label!: string;
  @Input() comparingLabel!: string;

  @Input() patternMsg!: string;
  @Input() minLengthMsg!: string;
  @Input() minRangeMsg!: string;
  @Input() minDateMsg!: string;
  @Input() maxDateMsg!: string;

  constructor(private translate: TranslateService) {

  }

  ngOnInit() {
    if (!this.patternMsg) {
      this.patternMsg = this.getKey('invalidField');
    }
  }

  getMinLengthErrorMsg() {
    if (this.minLengthMsg)
      return this.minLengthMsg;

    return this.translate.instant(this.getKey('textMinLength'), { field: this.label, $: this.formCtrl.minRequiredLength });
  }

  getMinRangeErrorMsg() {
    if (this.minRangeMsg)
      return this.minRangeMsg;

    return this.translate.instant(this.getKey('minRang'), { field: this.label, $: this.formCtrl.min });
  }

  getDateErrorMsg() {
    if (this.formCtrl.formControl.errors?.['ngbDate']?.minDate)
      return this.getMinDateErrorMsg();

    if (this.formCtrl.formControl.errors?.['ngbDate']?.maxDate)
      return this.getMaxDateErrorMsg();

    return '';
  }

  getMinDateErrorMsg() {
    if (this.minDateMsg)
      return this.minDateMsg;

    return this.translate.instant(this.getKey('dateMinErrorMsg'), { field: this.label, $: this.comparingLabel });
  }

  getMaxDateErrorMsg() {
    if (this.maxDateMsg)
      return this.maxDateMsg;

    return this.translate.instant(this.getKey('dateMaxErrorMsg'), { field: this.label, $: this.comparingLabel });
  }

  get form(): FormGroup<any> {
    return this.formCtrl?.formControl?.parent as FormGroup<any>;
  }

  getKey(key: any) {
    return 'shared.msg.' + key;
  }
}
