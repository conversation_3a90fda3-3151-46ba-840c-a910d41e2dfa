// ---------- Accordion

//----------- landing-accordion
.landing-accordion {
  // :host ::ng-deep .p-accordion p {
  //   line-height: 1.5;
  //   margin: 0;
  // }
  .p-accordion-tab {
    margin-bottom: 1rem;
    // box-shadow: 0px 4px 26px #93B2AC5C;
    // border-radius: 20px;
    &:not(.p-accordion-tab-active):hover{
      --decor-color: #f7fffd !important;
      .p-accordion-header-link {
        background-color: #f7fffd;
      }
      .p-accordion-header,
      .p-accordion-tab{
        --decor-color:#f7fffd !important;
        --decor-color2:#f7fffd !important;
      }
    }
    .p-highlight {
      .p-accordion-toggle-icon {
        transform: rotate(180deg) !important;
      }
    }
    .p-accordion-header .p-accordion-header-link .p-accordion-toggle-icon {
      margin: 0;
      position: absolute;
      right: 2rem;
      color: #8D8D8D;
     // top: 1.5rem;
      [dir="rtl"] & {
        left: 2rem;
        right: auto;
        transform: rotate(90deg);
      }
    }
     .p-accordion-header .p-accordion-header-link,
    .p-accordion-header:not(.p-disabled).p-highlight:hover
      .p-accordion-header-link,
    .p-accordion-header:not(.p-highlight):not(.p-disabled):hover
      .p-accordion-header-link,
    .p-accordion-header:not(.p-disabled).p-highlight .p-accordion-header-link {
      padding: 1rem 1rem 1.5rem;
      font-size: 1.5rem;
      color: $primary;
      border-radius: 0px; 
      border: unset;
      font-weight: 400;
      padding-inline-end: 3rem;
      min-height: 87px;
    }
    
    .p-accordion-content {
      border: 0;
      padding: 1rem 2.8rem 2rem;
      color: #676767;
      font-size: 18px;
      background-color: #FFFFFF;
      border-radius: 0px;
      [dir="rtl"] & {
        .p-accordion-toggle-icon {
          left: 2rem;
          right: auto;
          transform: rotate(90deg);
        }
      }
    }
  //   .table {
  //     table-layout: fixed;
  //   }
  //   body hr {
  //     margin: 2rem 0;
  //   }
   }
}