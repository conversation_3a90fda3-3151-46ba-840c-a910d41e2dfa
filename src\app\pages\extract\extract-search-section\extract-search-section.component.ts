import { Component, OnD<PERSON>roy, OnInit, inject, Output, EventEmitter } from '@angular/core';
import { ExtractInfoEvents } from '../../../shared/events/extract.events'; 
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { SearchTypeEnum } from '../../../shared/models/search-type.enum'; 
import { Router } from '@angular/router';
import { finalize, Subscription, take, timeout } from 'rxjs';
import { BASE_PATH, CaseDataByFile, ClassificationAndSubClassificationLookupResult, CreateClassificationFeedbackInput, FeedbackService, LookupDetailsResult, LookupService, SearchSource, SemanticExtractResult, SemanticService } from "../../../services";
import { HttpClient } from "@angular/common/http"; 
import { BaseComponent } from "../../../shared/base-component";
import { BaseTranslationKeys } from "../../../shared/models/base-translation-keys.model";  

@Component({
  selector: 'app-extract-search-section',
  templateUrl: './extract-search-section.component.html',
  styleUrls: ['./extract-search-section.component.css']
})
export class ExtractSearchSectionComponent extends BaseComponent implements OnInit, OnDestroy {
  @Output() searchCompleted = new EventEmitter<void>();
  showClassificationFeedbackDialog = false;
  modifyClassification = false;
  mainClassificationList?: Array<LookupDetailsResult>;
  subClassificationList?: Array<LookupDetailsResult>;
  classificationAndSubClassificationLookupResult!: ClassificationAndSubClassificationLookupResult;
  createClassificationFeedback!: CreateClassificationFeedbackInput;
  createClassificationFeedbackInProgress: boolean = false;
  formGroup!: FormGroup;
  private readonly baseBath = inject(BASE_PATH);
  private readonly router = inject(Router);
  private readonly _httpClient = inject(HttpClient);
  protected readonly semanticService = inject(SemanticService);
  protected readonly _feedbackService = inject(FeedbackService);
  protected readonly _lookupService = inject(LookupService);
  //private readonly classificationService = inject(ClassificationService);

  /**
   * @description النتيجة
   */
  protected data: { classificationMainCategory?: string; classificationSubCategory?: string; dataset?: string } = {};


  protected searchType: SearchTypeEnum = SearchTypeEnum.byLaw;
  protected SearchTypeEnum = SearchTypeEnum;
  protected subscription!: Subscription
  caseText!: string;


  constructor() {
    super(new BaseTranslationKeys('semantic', 'extractSearchSectionComponent'));
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  ngOnInit() { 
    this.initClassificationFeedbackForm();
    this.subscription = ExtractInfoEvents.InfoExtracted.getListener()
      .pipe(take(1))
      .subscribe(events => this.loadData(events!));

    this.caseText = localStorage.getItem('file_caseText') || '';

    this.getClassificationAndSubClassificationLookup();
  }

  private loadData(text: string) {
    this.data = {
      classificationMainCategory: localStorage.getItem('file_category') || '',
      classificationSubCategory: localStorage.getItem('file_subCategory') || ''
    };
  }

  initClassificationFeedbackForm() {
    this.formGroup = new FormGroup({
      mainClassification: new FormControl('',
        Validators.compose([
          Validators.required
        ])
      ),
      subClassification: new FormControl('',
        Validators.compose([
          Validators.required
        ])
      ),
      feedbackNotes: new FormControl('',
        Validators.compose([
          Validators.required
        ])
      )
    });
  }

  openClassificationFeedbackModel() {
    this.showClassificationFeedbackDialog = true;
    this.modifyClassification = false;
    this.subClassificationList = [];
    this.createClassificationFeedbackInProgress = false;
    this.initClassificationFeedbackForm();
  }

  filterSubClassification() {
    if (this.formGroup.get("mainClassification")?.value) {
      this.subClassificationList = this.classificationAndSubClassificationLookupResult.subClassifications?.filter(x => x.classificationId == this.formGroup.get("mainClassification")?.value);  
    
      try{
        this.subClassificationList =  this.subClassificationList?.sort( (n1: any , n2: any) => { return n2.nameAr - n1.nameAr });
      }catch(erro){
        this.subClassificationList = this.classificationAndSubClassificationLookupResult.subClassifications?.filter(x => x.classificationId == this.formGroup.get("mainClassification")?.value);
      }   
    
    } else {
      this.subClassificationList = [];
    }

  }

  getClassificationAndSubClassificationLookup() {
    this._lookupService.getClassificationAndSubClassificationList().subscribe(
      (response: any) => {
        if (response.isSuccess) {
          this.classificationAndSubClassificationLookupResult = response.data;
          this.mainClassificationList = this.classificationAndSubClassificationLookupResult?.classifications ?? [];
          try{
            this.mainClassificationList = this.mainClassificationList.sort( (n1: any , n2: any) => { return n2.nameAr - n1.nameAr });
          }catch(erro){
            this.mainClassificationList = this.classificationAndSubClassificationLookupResult?.classifications ?? [];
          }         
          
          console.log(response);
        } else {
          console.error('Failed to load users', response.error);
        }
      },
      (error: any) => {
        console.error('Error loading users', error);
      }
    );

  }

  onSubmitClassificationFeedback(form: any) {

    if (form) {
      if (form.valid) {
        this.createClassificationFeedback = {
          classificationId: this.formGroup.get("mainClassification")?.value, 
          previousClassification: JSON.parse(localStorage.getItem('file_category') || '""'),
          subClassificationId: this.formGroup.get("subClassification")?.value,
          previousSubClassification: JSON.parse(localStorage.getItem('file_subCategory') || '""'),
          feedbackNote: this.formGroup.get("feedbackNotes")?.value == '' ? null : this.formGroup.get("feedbackNotes")?.value,
          searchedQuery: JSON.parse(localStorage.getItem('file_caseText') || '""')
        };
      } else {
        return;
      }

    } else {
      this.createClassificationFeedback = {
        classificationId: 0,
        previousClassification: JSON.parse(localStorage.getItem('file_category') || '""'),
        classification: JSON.parse(localStorage.getItem('file_category') || '""'),
        subClassificationId: 0,
        subClassification: JSON.parse(localStorage.getItem('file_subCategory') || '""'),
        previousSubClassification: JSON.parse(localStorage.getItem('file_subCategory') || '""'),
        searchedQuery: JSON.parse(localStorage.getItem('file_caseText') || '""')
      };
    }

    this.createClassificationFeedbackInProgress = true;
    this._feedbackService.submitClassificationFeedback(this.createClassificationFeedback)
      .pipe(finalize(() => {
        this.createClassificationFeedbackInProgress = false;
      }))
      .subscribe({
        next: (res => {
          this.showClassificationFeedbackDialog = false;
          this._messageService.add({
            severity: 'success',
            summary: 'نجاح',
            detail: 'تم تأكيد نسبة التشابه بنجاح'
          });
        }),
        error: (err => {
          this._messageService.add({
            severity: 'error',
            summary: 'خطأ',
            detail: err.error?.error?.message

          });
        })
      })
  }

  // searchResult: any;
  // startSearch: boolean = false;
  // search() {
  //   // this.startSearch = true;
  //   //
  //   // const category = {
  //   //   mainCategory: this.data.classificationMainCategory as string,
  //   //   subCategory: this.data.classificationSubCategory as string,
  //   // }
  //   // this.router.navigateByUrl(`/search/${this.searchType}`, {
  //   //   state: { category }
  //   // });
  //   //
  //   // SearchEvents.SearchStarted.pushEvent({
  //   //   text: this.data.dataset as string,
  //   //   mainCategory: this.data.classificationMainCategory as string,
  //   //   subCategory: this.data.classificationSubCategory as string,
  //   //   searchType: this.searchType
  //   // });
  //   this.semantic.search({
  //     pageNumber: 1,
  //     pageSize: 10,
  //   }).subscribe((response: SemanticSearchByQuestionResultPortalResponse) => {
  //
  //     console.log(response);
  //     localStorage.setItem('file', JSON.stringify(response));
  //
  //     this.router.navigate(['/search/semantic/question-result']);
  //
  //   });
  // }

  isSearchResultComponentVisible: boolean = false;

  search() {
    const requestBody = {
      pageNumber: 1,
      pageSize: 5,
      text: this.caseText,
      category: this.data.classificationMainCategory as string,
      subCategory: this.data.classificationSubCategory as string
    };

    this.semanticService.searchByFile({
      text: this.caseText,
      searchSource: this.searchType
    }).subscribe({
      next: (response) => {
        if (response.isSuccess) {
          const caseResult = response.data.caseResult;
          const lawResult = response.data.lawResult;

          // Handle the case where caseResult is null
          const casesData: Array<CaseDataByFile> = caseResult?.caseData ?? [];
          const lawsData = lawResult?.data ?? [];

          // const casesData: Array<CaseData> = response.data.caseResult.caseData as Array<CaseData>;

          localStorage.setItem('search-response', this.toJson(response.data));
          localStorage.setItem('searchType', this.searchType.toString());

          console.log('CasesData:', casesData);
          console.log('LawsData:', lawsData);

          this.displaySearchResultComponent();
          this.searchCompleted.emit();

          // this.router.navigate(['/search/semantic/question-result']);
        } else {
          console.error('Search failed:', response.error);
        }
      },
      error: (error) => {
        console.error('An error occurred:', error);
      }
    });
  }

  displaySearchResultComponent() {
    this.isSearchResultComponentVisible = true;
  }

}
