<main class="welcome-page">
  <div class="container">
    <div class="welcome-content text-center">
      <h2 class="welcome-title text-success mb-2">مرحبًا بك</h2>
      <h3 class="welcome-subtitle mb-4">في المساعد الذكي</h3>

      <p class="welcome-description mb-5">
        أداة مدعومة بالذكاء الاصطناعي لمساعدتك في اتخاذ قرارات قانونية دقيقة
        وسريعة.
      </p>

      <p class="option-title mb-4">
        اختر الطريقة التي تفضلها للحصول على المساعدة القانونية.
      </p>

      <div class="row justify-content-center">
        <div class="col-md-5">
          <app-semantic-search-landing-file
            (onSemanticSearch)="searchByFile($event)"
          ></app-semantic-search-landing-file>
        </div>

        <div class="col-md-5">
          <div class="option-card h-100">
            <div class="card-body text-start p-4">
              <h5 class="option-card-title mb-3">طرح سؤال قانوني</h5>
              <p class="option-card-text">
                اكتب سؤالك القانوني للحصول على إجابة مدعومة بالتحليل النظامي
                والمراجع ذات العلاقة.
              </p>
              <button
                class="btn btn-outline-primary mt-3"
                (click)="setSearchType('text')"
              >
                متابعة
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Hidden containers for the search functionality -->
  <!-- <div class="">
    <ng-container *ngIf="searchType === 'text'">
      <app-semantic-search-landing-text
        (onSemanticSearch)="searchResult($event)"
      ></app-semantic-search-landing-text>
    </ng-container>
  </div> -->
</main>
