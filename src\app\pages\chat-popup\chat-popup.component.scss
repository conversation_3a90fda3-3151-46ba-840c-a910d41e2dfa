/* Make sure the bubble itself is a block box that includes padding in its width */
.user-bubble,
.assistant-bubble {
  display: block;
  box-sizing: border-box;
  padding: 0.5rem 1rem;
  max-width: 75%;
  border-radius: 16px;
  word-wrap: break-word;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* Color schemes */
.user-bubble {
  background-color: #f0f0f0; /* light grey */
  color: #333;               /* dark grey text */
}
.assistant-bubble {
  background-color: #d4edda; /* soft green */
  color: #155724;            /* dark green text */
}

/* Reset any default margins/padding inside your rendered markdown
   so lists, paragraphs, headings don’t escape the bubble */
.user-bubble .markdown-body,
.assistant-bubble .markdown-body {
  margin: 0;
  padding: 0;
}

/* Indent lists via padding, not margin, so bullets stay inside */
.user-bubble .markdown-body ul,
.assistant-bubble .markdown-body ul {
  padding-inline-start: 1rem; /* indent the bullets */
  margin: 0;                  /* no extra outside margin */
}
.user-bubble .markdown-body li,
.assistant-bubble .markdown-body li {
  margin-bottom: 0.25rem;     /* spacing between list items */
}

/* Optional: thin, custom scrollbar in your chat container */
.scrollbar-style::-webkit-scrollbar {
  width: 6px;
}
.scrollbar-style::-webkit-scrollbar-thumb {
  background-color: rgba(0, 128, 0, 0.3);
  border-radius: 3px;
}
/* For Firefox */
.scrollbar-style {
  scrollbar-width: thin;
  scrollbar-color: rgba(0,128,0,0.3) transparent;
}
