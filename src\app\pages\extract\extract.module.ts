import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ExtractInfoEmptyComponent } from './extract-info-empty/extract-info-empty.component';
import { AngularSvgIconModule } from 'angular-svg-icon';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ExtractInfoComponent } from './extract-info/extract-info.component';
import { SkeletonModule } from 'primeng/skeleton';
import { ExtractInfoHighlightComponent } from './extract-info-highlight/extract-info-highlight.component';
import { RouterModule } from '@angular/router';
import { ExtractSearchSectionComponent } from './extract-search-section/extract-search-section.component';
import { ExtractInfoNerComponent } from './extract-info-ner/extract-info-ner.component';
import { ExtractInfoKeywordsComponent } from './extract-info-keywords/extract-info-keywords.component';
import { SharedModule } from 'primeng/api';
import { SplitterModule } from 'primeng/splitter';
import { DropdownModule } from 'primeng/dropdown';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { SearchModule } from '../search/search.module';
import { DialogModule } from 'primeng/dialog';
import { ButtonModule } from 'primeng/button';
import { ChatPotComponent } from '../chat-pot/chat-pot.component';

@NgModule({
  imports: [
    CommonModule,
    AngularSvgIconModule,
    FormsModule,
    SkeletonModule,
    DropdownModule,
    OverlayPanelModule,
    SearchModule,
    DialogModule,
    ButtonModule,
    ReactiveFormsModule,
    ChatPotComponent,
    RouterModule.forChild([
      {
        path: 'info-empty',
        component: ExtractInfoEmptyComponent,
      },
      // {
      //     path: "info",
      //     component: ExtractInfoComponent
      // },
      {
        path: 'info-highlight',
        component: ExtractInfoHighlightComponent,
      },
    ]),
    SharedModule,
    SplitterModule,
  ],
  declarations: [
    ExtractInfoComponent,
    ExtractInfoEmptyComponent,
    ExtractInfoHighlightComponent,

    ExtractInfoKeywordsComponent,
    ExtractInfoNerComponent,

    ExtractSearchSectionComponent,
  ],
})
export class ExtractModuleModule {}
