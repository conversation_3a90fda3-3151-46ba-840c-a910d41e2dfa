import { Component, OnInit } from '@angular/core';
import { SearchCategory } from '../_models/search-category.model';
import { SearchEvents } from '../../../../shared/events/search.events';

@Component({
  selector: 'app-regular-search-result',
  templateUrl: './regular-search-result.component.html',
  styleUrls: ['./regular-search-result.component.scss']
})
export class RegularSearchResultComponent implements OnInit {
  value: string | undefined;
  protected title: string = 'نص القضية';
  protected content: string = '';
  nerPredictWords: string[] = [];
  nerList: string[] = [];
  protected caseText!: string;
  protected verdict!: string;
  protected law!: string;
  updateContent(title: string, content: string) {
    this.title = title;
    this.content = content;
  }

  readonly categories = {
    regulations: {
      id: 'regulations',
      label: 'أنظمة',
      selected: false,
      opened: false
    },
    judicial_precedents: {
      id: 'judicial_precedents',
      label: 'السوابق القضائية',
      selected: false,
      opened: false
    },
    circulars: {
      id: 'circulars',
      label: 'التعاميم',
      selected: false,
      opened: false
    },
    sources: {
      id: 'sources',
      label: 'مصادر',
      selected: false,
      opened: false
    },
    forms: {
      id: 'forms',
      label: 'نماذج',
      selected: false,
      opened: false
    },
  }

  allCategories = [
    this.categories.regulations,
    this.categories.circulars,
    this.categories.sources,
    this.categories.judicial_precedents,
    this.categories.forms
  ]

  get selectedCategory() {
    return this.allCategories.filter(x => x.selected);
  }

  searchText!: string;
  isSearched: boolean = false;

  constructor() { }

  ngOnInit() {
  }

  resetSearch() {
    this.isSearched = false;
  }

  search() {
    this.resetOpen();
    if (this.searchText && this.searchText.length > 0) {
      this.isSearched = true;
    }
  }


  resetOpen() {
    this.categories.regulations.opened =
      this.categories.judicial_precedents.opened =
      this.categories.forms.opened =
      this.categories.sources.opened =
      this.categories.circulars.opened =
      false;
  }

  canSearch() {
    return (this.selectedCategory.length > 0) && (this.searchText && this.searchText.trim().length > 3)
  }

}
