import { HttpErrorResponse, HttpEvent, Http<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { inject, Injectable, InjectionToken, Injector, Provider } from '@angular/core';
import { catchError } from 'rxjs/operators';
import {Observable, throwError, timeout} from "rxjs";
import { Router } from '@angular/router';
import { Message, MessageService } from 'primeng/api';
import { TranslateService } from '@ngx-translate/core';
import { AuthService } from '../../../auth/public-api';
import { ApiResponse, AppConfigService } from '../../public.api';
import { BASE_PATH } from '../../../services';

@Injectable({ providedIn: 'root' })

export class AppHttpInterceptorService implements HttpInterceptor {

  private readonly _errorTranslationPath = "error.";
  private readonly _defaultTimeout = 200000;

  protected _AutService!: AuthService;
  private readonly _router = inject(Router);
  private readonly _BASE_API: string = "";//= "";// inject(BASE_PATH);
  private readonly _messageService = inject(MessageService);
  private readonly _translate = inject(TranslateService);

  constructor() {
  }



  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {

    let request: HttpRequest<any>;

    // Skip handling the config file
    if (req.url.endsWith("config.json")) {
      return this.handle(req, next);
    }

    // check if the request url is for the backend API
    const isApiCall = req.url.startsWith(this._BASE_API);

    // accessToken should be set after login, check AuthChecker component.
    const token = localStorage.getItem("accessToken");
    const userProfile = localStorage.getItem("current-profile") ?? '';

    if (token === 'null') {
      this.redirectToLogin();
      return next.handle(req);
    }

    if (token && isApiCall) {
      let headers = req.headers.set("Authorization", `Bearer ${token}`);
      if (userProfile) {
        headers = headers.set("Gap-Userprofile", userProfile);
      }

      request = req.clone({ headers: headers, withCredentials: true });
    } else {
      request = req.clone({ headers: req.headers });
    }

    return this.handle(request, next);
  }

  private handle(request: HttpRequest<any>, next: HttpHandler) {
    return next.handle(request).pipe(
      timeout(this._defaultTimeout),
      catchError(error => {
        if (error instanceof HttpErrorResponse) {

          this._messageService.clear();

          switch (error.status) {
            case 401:
              if (error.error == 'Lifetime') {
                this.redirectToLogin();
                break;
              }
              // localStorage.removeItem('accessToken');
              // localStorage.removeItem('user-profile');
              localStorage.setItem("isAuthenticated", "false");
              this._router.navigate(['/unauthorized']);
              break;
            case 403:
              this.setInActive();
              break;
            case 400:
              if (error.error.error?.code == '404') {
                this._router.navigate(['/notfound']);
                break;
              }
              this._messageService.add(this.map400ServerError(error.error));
              break;
            case 500:
              this._messageService.add(this.map500ServerError(error.error));
              break;
            default:
              this._messageService.add(this.getMessageConfig('حدث خطأ في النظام'));
              break;
          }

        }
        return throwError(() => error);
      })
    );
  }

  private redirectToLogin(): void {
    const auth = inject(AuthService);
    auth.resetSessions();
    this._router.navigate(['/login']);
  }
  private setInActive() {
    var auth = inject(AuthService);
    auth.setActive(false);
    this._router.navigate(['/blocked']);
  }

  private map400ServerError(apiResponse: ApiResponse): Message {
    return this.getMessageConfig(this.get400ErrorMessage(apiResponse.error!.code!, apiResponse.error!.referenceId!, apiResponse.error!.params!))
  }

  private get400ErrorMessage(errorCode: string, ref?: string, params?: { [ke: string]: string }): string {
    if (this.errorCodeMessages[errorCode]) {
      return this.errorCodeMessages[errorCode];
    }

    if (errorCode == 'MalformedInput') {
      return " حدث خطأ، الرقم المرجعي " + ref;
    }
    const error = this._translate.instant(this._errorTranslationPath + errorCode, params);
    if (error.startsWith(this._errorTranslationPath)) {
      return "حدث خطأ في النظام";
    }
    return error;
  }

  private errorCodeMessages: { [code: string]: string } = {
    '8001': 'تم تجاوز عدد الأحرف المسموح بها في الملف',
    '8002': 'نوع الملف غير صالح',
    '8003': 'تم تجاوز الحجم المسموح به للملف'
  };

  private getErrorMessage(errorCode: string): string {
    const error = this._translate.instant(this._errorTranslationPath + errorCode);
    if (error.startsWith(this._errorTranslationPath)) {
      return "حدث خطأ في النظام";
    }

    return error;
  }

  private map500ServerError(apiResponse: ApiResponse): Message {
    var msg = 'حدث خطأ';
    if (apiResponse?.error?.code)
      msg += '، الرقم المرجعي ' + apiResponse.error.referenceId;
    else
      msg += ' في النظام '

    return this.getMessageConfig(msg)
  }

  private getMessageConfig(details: string): Message {
    return {
      severity: 'error',
      summary: 'خطأ',
      detail: details,
      life: 5000
    }
  }
}
