import { Component } from '@angular/core';
import {Router} from "@angular/router";
import {FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators} from "@angular/forms";
import {
  FormControlValidationsComponent
} from "../../shared/forms/form-control-validations/form-control-validations.component";
import {CustomFormControl} from "../../shared/forms/custom-form-control.model";
import {BaseForm} from "../../shared/forms/base-form.model";
import {EMAIL} from "../../shared/regex.constants";
import {NavigationHeaderComponent} from "../../layout/app-layout/navigation-header/navigation-header.component";
import {SideNavbarComponent} from "../../layout/side-navbar/side-navbar.component";
import {SvgIconComponent} from "angular-svg-icon";
import {HttpClient} from "@angular/common/http";
import {IAppConfig} from "../../shared/models/app-config.interface";
import {AppConfigService} from "../../shared/app-config.service";
import {UserProfile} from "../../auth/models/user-profile.interface";

@Component({
  selector: 'app-bypass-login',
  standalone: true,
  imports: [
    FormsModule,
    FormControlValidationsComponent,
    ReactiveFormsModule,
    NavigationHeaderComponent,
    SideNavbarComponent,
    SvgIconComponent
  ],
  templateUrl: './bypass-login.component.html',
  styleUrl: './bypass-login.component.scss'
})
export class BypassLoginComponent {
  email = '';
  protected controls = new Control();

  constructor(private router: Router, private http: HttpClient, private appConfigService: AppConfigService) { }

  onLogin(): void {
    this.controls.email.setValue(this.controls.email?.value?.trim());

    if (this.controls.isInvalid) {
      return;
    }

    const email = this.controls.email.value;
    const apiBaseUrl = this.appConfigService.apiBaseUrl;
    this.http.get(`${apiBaseUrl}/api/mock/generate-token?email=${email}`).subscribe({
      next: (response: any) => {
        console.log(response);
        const accessToken = response.data.token;
        console.log('Access Token:', accessToken);
        localStorage.setItem('udata', JSON.stringify(response.data.userData));
        localStorage.setItem('accessToken', accessToken);
        localStorage.setItem("user-profile", JSON.stringify(response.data));
        localStorage.setItem("isActive", response.data.isActive.toString());

        if (response.data?.currentUserProfile) {
          const profileRef = response.data.currentUserProfile.reference ?? '';
          localStorage.setItem("current-profile", profileRef);
        }

        localStorage.setItem("isAuthenticated", "true");
        localStorage.setItem('isLogging', "true");

        this.router.navigate(['/my-settings']);
      },
      error: (error) => {
        console.error('Error generating token:', error);
      },
    });
  }

  backToHomePage() {
    this.router.navigate(['/']);
  }
}

class Control extends BaseForm {
  public readonly email = new CustomFormControl('email', [Validators.required, Validators.pattern(EMAIL)]).setForm(this.form);
}
