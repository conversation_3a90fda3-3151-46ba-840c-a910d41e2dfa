/**
 * MOSAED.Internal.Api
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { Role } from './role';
import { User } from './user';
import { InternalUserProfile } from './internal-user-profile';
import { ContactInformation } from './contact-information';


export interface UserProfile { 
    id: number;
    createdAt: string;
    createdById: number;
    createdByUser: UserProfile;
    updatedAt?: string | null;
    updatedBy?: number | null;
    updatedByUser: UserProfile;
    userId: number;
    user: User;
    isActive: boolean;
    deactivationReason?: string | null;
    roleCode: number;
    role: Role;
    contactInformationId?: number | null;
    contactInformation: ContactInformation;
    internalProfile: InternalUserProfile;
    profileReference: string;
}

