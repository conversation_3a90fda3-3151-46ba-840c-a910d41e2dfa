<nav
  class="navbar navbar-expand-md navbar-light animate__animated animate__fadeInDown"
  id="navbar-example2"
>
  <div class="container-fluid p-4">
    <div class="d-flex justify-content-between align-items-center">
      <a class="navbar-brand ms-0" [routerLink]="['/']"
        ><img
          src="../../../../assets/images/musaid-logo.svg"
          class="logo"
          alt="logo"
      /></a>
    </div>

    <!-- Mobile button -->
    <div class="d-flex justify-content-end align-items-center">
      <button
        class="navbar-toggler border-0 d-lg-none px-0"
        type="button"
        data-bs-toggle="collapse"
        data-bs-target="#collapsibleNavId"
        aria-controls="collapsibleNavId"
        aria-expanded="false"
        aria-label="Toggle navigation"
      >
        <i class="fa fa-sm fa-search text-white"></i>
      </button>
    </div>
    <div class="collapse navbar-collapse" id="collapsibleNavId">
      <div class="ms-auto custom-input-group d-none">
        <div class="input-group mt-3 mt-md-0">
          <button
            class="input-group-text bg-white dropdown-toggle"
            type="button"
            data-bs-toggle="dropdown"
            aria-expanded="false"
          >
            بحث في قضايا ديوان المظالم
          </button>
          <ul class="dropdown-menu">
            <li>
              <a class="dropdown-item" href="#">بحث في قضايا ديوان المظالم</a>
            </li>
            <li>
              <a class="dropdown-item" href="#">بحث في قضايا وزارة العدل</a>
            </li>
          </ul>
          <input
            type="text"
            class="form-control bg-white border-end-0"
            placeholder="البحث .."
          />
          <button class="input-group-text bg-white addon-button pt-0">
            <svg-icon src="../../assets/svg/search-dark.svg"></svg-icon>
          </button>
        </div>
      </div>
      @if(userInformation){
      <div class="mt-2 mt-lg-0 ms-auto">
        <button
          class="btn text-white d-flex align-items-center"
          (click)="op.toggle($event)"
          role="button"
        >
          <svg-icon
            [svgStyle]="{ height: '2.5rem' }"
            src="../../../../assets/images/user-icon.svg"
          ></svg-icon>
          <div
            class="text-start user-name mx-2"
            style="font-size: 12px; font-weight: 400; color: #365a4e"
          >
            <p
              style="
                font-size: 12px;
                font-weight: 400;
                color: #365a4e;
                margin-bottom: 0px;
              "
            >
              {{ userInformation.currentUserProfile?.role?.nameAr }}
            </p>
            <p
              style="
                font-size: 12px;
                font-weight: 400;
                color: #365a4e;
                margin-bottom: 0px;
              "
            >
              {{ userInformation.fullName }}
            </p>
          </div>
          <img
            src="../../../../assets/images/dropdown-icon.svg"
            alt="arrow-down"
          />
        </button>
      </div>
      <p-overlayPanel #op>
        <ul class="list-group p-0 list-group-flush">
          <li
            class="list-group-item border-0"
            role="button"
            [routerLink]="['/my-settings']"
          >
            <i class="pi pi-cog pe-1"></i>
            الإعدادات
          </li>

          <li class="list-group-item border-0" role="button" (click)="logout()">
            <i class="pi pi-sign-out pe-1"></i>
            {{ "auth.logout" | translate }}
          </li>
        </ul>
      </p-overlayPanel>

      }
      <!-- <ul class="navbar-nav mt-2 mt-lg-0 ms-auto" *ngIf="userInformation">
        <li class="nav-item d-none d-md-block">
          <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown"
            aria-expanded="false">
            <svg-icon src="../../assets/svg/user-circle-white.svg"></svg-icon>
            {{userInformation.fullName}}
          </a>
          <ul class="dropdown-menu dropdown-menu-end">
            <li>
              <button class="dropdown-item" (click)="logout()">
                {{ 'auth.logout'|translate}}
              </button>
            </li>
          </ul>
        </li>
      </ul> -->
    </div>
  </div>
</nav>
