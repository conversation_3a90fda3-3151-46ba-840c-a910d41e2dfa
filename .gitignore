### Angular ###
## Angular ##
# compiled output
dist/
tmp/
app/**/*.js
app/**/*.js.map

# dependencies
node_modules/
bower_components/

# IDEs and editors
.idea/

# misc
.sass-cache/
connect.lock/
coverage/
libpeerconnection.log/
npm-debug.log
testem.log
typings/
.angular/

# e2e
e2e/*.js
e2e/*.map

# System Files
.DS_Store/

# Exclude specific dotfiles that we want to track.
!deps/**/.*
!.devcontainer/
!.devcontainer/.devcontainer.json
!test/fixtures/**/.*
!.clang-format
!.cpplint
!.editorconfig
!.eslintignore
!.eslintrc.js
!.eslintrc.yaml
!.gitattributes
!.github
!.gitignore
!.gitkeep
!.gitpod.yml
!.mailmap
!.nycrc
!.yamllint.yaml

# === Rules for root dir ===
/core
/vgcore.*
/v8*.log
/perf.data
/perf.data.old
/tags
/doc/api.xml
/node
/node_g
/gon-config.json
/*.exe
/*.swp
/out
/*.msi
/*.wixpdb
/*.qlog
# For GitHub Codespaces
/pythonenv*

# === Rules for artifacts of `./configure` ===
/icu_config.gypi
/config.gypi
/config.status
/config_fips.gypi

# === Rules for MSVS artifacts ===
Debug/
Release/
*.sln
*.suo
*.vcxproj*
UpgradeLog*.XML
_UpgradeReport_Files/
*.sdf
*.opensdf
*.VC.*
*.wixobj
/tools/msvs/genfiles/
/npm.wxs
/corepack.wxs
/tools/msvs/msi/**/Release/
/tools/msvs/msi/**/obj/
/tools/msvs/msi/**/x64/
# Exclude MSVS files used for .msi file generation
!tools/msvs/msi/**/custom_actions.vcxproj
!tools/msvs/msi/**/nodemsi.sln

# === Rules for GYP artifacts ===
*-nodegyp*
/gyp-*-tool
/test/addons/??_*/
/*.mk

# === Rules for other build artifacts ===
/email.md
/deps/v8-*
/deps/icu
/deps/icu*.zip
/deps/icu*.tgz
/deps/icu-tmp
./node_modules
/android-toolchain/
# generated by gyp on Windows
/deps/openssl/openssl.props
/deps/openssl/openssl.targets
/deps/openssl/openssl.xml
/deps/openssl/openssl-fipsmodule.props
/deps/openssl/openssl-fipsmodule.targets
/deps/openssl/openssl-fipsmodule.xml
# generated by gyp on android
/*.target.mk
/*.host.mk
/deps/openssl/openssl.target.mk
/deps/zlib/zlib.target.mk
# generated by MSVC with /P enabled
tools/*/*.i
tools/*/*.i.tmp

# === Rules for release artifacts ===
/*.tar.*
/*.pkg
/SHASUMS*.txt*

# === Rules for `node_modules` ===
!**/node_modules/**
/node_modules
/tools/doc/node_modules
/tools/clang-format/node_modules

# === Rules for test artifacts ===
/*.tap
/*.xml
/v8*-tap.json
/node_trace.*.log
# coverage related
/gcovr
/build
/coverage

# === Rules for Xcode artifacts ===
*.xcodeproj
*.xcworkspace
*.pbxproj

# === Rules for files in `/deps` ===
# Exclude all files in the vendored `npm`.
!/deps/npm/node_modules
# These are not needed and causes issues for distro packagers.
/deps/npm/node_modules/.bin/
# Respect V8's .gitignore
!deps/v8/**
# Ignore the libuv book
/deps/uv/docs/code/
/deps/uv/docs/src/guide/
# Ignore .github directories
/deps/**/.github/
# Ignore dependencies fetched by tools/v8/fetch_deps.py
/deps/.cipd

# === Rules for Windows vcbuild.bat ===
/temp-vcbuild

# === Rules for CMake ===
cmake-build-debug/
CMakeCache.txt
CMakeFiles
CTestTestfile.cmake
cmake_install.cmake
install_manifest.txt
*.cbp

# === Global Rules ===
# Keep last to avoid being excluded
*.pyc
__pycache__
.DS_Store
*~

# === Rules for C++ development ===
compile_commands.json

### Intellij ###
# Covers JetBrains IDEs: IntelliJ, RubyMine, PhpStorm, AppCode, PyCharm, CLion, Android Studio, WebStorm and Rider
# Reference: https://intellij-support.jetbrains.com/hc/en-us/articles/206544839

# User-specific stuff
.idea/**/workspace.xml
.idea/**/tasks.xml
.idea/**/usage.statistics.xml
.idea/**/dictionaries
.idea/**/shelf

# AWS User-specific
.idea/**/aws.xml

# Generated files
.idea/**/contentModel.xml

# Sensitive or high-churn files
.idea/**/dataSources/
.idea/**/dataSources.ids
.idea/**/dataSources.local.xml
.idea/**/sqlDataSources.xml
.idea/**/dynamic.xml
.idea/**/uiDesigner.xml
.idea/**/dbnavigator.xml

# Gradle
.idea/**/gradle.xml
.idea/**/libraries

# Gradle and Maven with auto-import
# When using Gradle or Maven with auto-import, you should exclude module files,
# since they will be recreated, and may cause churn.  Uncomment if using
# auto-import.
# .idea/artifacts
# .idea/compiler.xml
# .idea/jarRepositories.xml
# .idea/modules.xml
# .idea/*.iml
# .idea/modules
# *.iml
# *.ipr

# CMake
cmake-build-*/

# Mongo Explorer plugin
.idea/**/mongoSettings.xml

# File-based project format
*.iws

# IntelliJ
out/

# mpeltonen/sbt-idea plugin
.idea_modules/

# JIRA plugin
atlassian-ide-plugin.xml

# Cursive Clojure plugin
.idea/replstate.xml

# SonarLint plugin
.idea/sonarlint/

# Crashlytics plugin (for Android Studio and IntelliJ)
com_crashlytics_export_strings.xml
crashlytics.properties
crashlytics-build.properties
fabric.properties

# Editor-based Rest Client
.idea/httpRequests

# Android studio 3.1+ serialized cache file
.idea/caches/build_file_checksums.ser

### Intellij Patch ###
# Comment Reason: https://github.com/joeblau/gitignore.io/issues/186#issuecomment-215987721

# *.iml
# modules.xml
# .idea/misc.xml
# *.ipr

# Sonarlint plugin
# https://plugins.jetbrains.com/plugin/7973-sonarlint
.idea/**/sonarlint/

# SonarQube Plugin
# https://plugins.jetbrains.com/plugin/7238-sonarqube-community-plugin
.idea/**/sonarIssues.xml

# Markdown Navigator plugin
# https://plugins.jetbrains.com/plugin/7896-markdown-navigator-enhanced
.idea/**/markdown-navigator.xml
.idea/**/markdown-navigator-enh.xml
.idea/**/markdown-navigator/

# Cache file creation bug
# See https://youtrack.jetbrains.com/issue/JBR-2257
.idea/$CACHE_FILE$

# CodeStream plugin
# https://plugins.jetbrains.com/plugin/12206-codestream
.idea/codestream.xml

# Azure Toolkit for IntelliJ plugin
# https://plugins.jetbrains.com/plugin/8053-azure-toolkit-for-intellij
.idea/**/azureSettings.xml

### Intellij+all ###
# Covers JetBrains IDEs: IntelliJ, RubyMine, PhpStorm, AppCode, PyCharm, CLion, Android Studio, WebStorm and Rider
# Reference: https://intellij-support.jetbrains.com/hc/en-us/articles/206544839

# User-specific stuff
.idea/**/workspace.xml
.idea/**/tasks.xml
.idea/**/usage.statistics.xml
.idea/**/dictionaries
.idea/**/shelf

# AWS User-specific
.idea/**/aws.xml

# Generated files
.idea/**/contentModel.xml

# Sensitive or high-churn files
.idea/**/dataSources/
.idea/**/dataSources.ids
.idea/**/dataSources.local.xml
.idea/**/sqlDataSources.xml
.idea/**/dynamic.xml
.idea/**/uiDesigner.xml
.idea/**/dbnavigator.xml

# Gradle
.idea/**/gradle.xml
.idea/**/libraries

# Gradle and Maven with auto-import
# When using Gradle or Maven with auto-import, you should exclude module files,
# since they will be recreated, and may cause churn.  Uncomment if using
# auto-import.
# .idea/artifacts
# .idea/compiler.xml
# .idea/jarRepositories.xml
# .idea/modules.xml
# .idea/*.iml
# .idea/modules
# *.iml
# *.ipr

# CMake
cmake-build-*/

# Mongo Explorer plugin
.idea/**/mongoSettings.xml

# File-based project format
*.iws

# IntelliJ
out/

# mpeltonen/sbt-idea plugin
.idea_modules/

# JIRA plugin
atlassian-ide-plugin.xml

# Cursive Clojure plugin
.idea/replstate.xml

# SonarLint plugin
.idea/sonarlint/

# Crashlytics plugin (for Android Studio and IntelliJ)
com_crashlytics_export_strings.xml
crashlytics.properties
crashlytics-build.properties
fabric.properties

# Editor-based Rest Client
.idea/httpRequests

# Android studio 3.1+ serialized cache file
.idea/caches/build_file_checksums.ser

### Intellij+all Patch ###
# Ignore everything but code style settings and run configurations
# that are supposed to be shared within teams.

.idea/*

!.idea/codeStyles
!.idea/runConfigurations

### Intellij+iml ###
# Covers JetBrains IDEs: IntelliJ, RubyMine, PhpStorm, AppCode, PyCharm, CLion, Android Studio, WebStorm and Rider
# Reference: https://intellij-support.jetbrains.com/hc/en-us/articles/206544839

# User-specific stuff
.idea/**/workspace.xml
.idea/**/tasks.xml
.idea/**/usage.statistics.xml
.idea/**/dictionaries
.idea/**/shelf

# AWS User-specific
.idea/**/aws.xml

# Generated files
.idea/**/contentModel.xml

# Sensitive or high-churn files
.idea/**/dataSources/
.idea/**/dataSources.ids
.idea/**/dataSources.local.xml
.idea/**/sqlDataSources.xml
.idea/**/dynamic.xml
.idea/**/uiDesigner.xml
.idea/**/dbnavigator.xml

# Gradle
.idea/**/gradle.xml
.idea/**/libraries

# Gradle and Maven with auto-import
# When using Gradle or Maven with auto-import, you should exclude module files,
# since they will be recreated, and may cause churn.  Uncomment if using
# auto-import.
# .idea/artifacts
# .idea/compiler.xml
# .idea/jarRepositories.xml
# .idea/modules.xml
# .idea/*.iml
# .idea/modules
# *.iml
# *.ipr

# CMake
cmake-build-*/

# Mongo Explorer plugin
.idea/**/mongoSettings.xml

# File-based project format
*.iws

# IntelliJ
out/

# mpeltonen/sbt-idea plugin
.idea_modules/

# JIRA plugin
atlassian-ide-plugin.xml

# Cursive Clojure plugin
.idea/replstate.xml

# SonarLint plugin
.idea/sonarlint/

# Crashlytics plugin (for Android Studio and IntelliJ)
com_crashlytics_export_strings.xml
crashlytics.properties
crashlytics-build.properties
fabric.properties

# Editor-based Rest Client
.idea/httpRequests

# Android studio 3.1+ serialized cache file
.idea/caches/build_file_checksums.ser

### Intellij+iml Patch ###
# Reason: https://github.com/joeblau/gitignore.io/issues/186#issuecomment-249601023

*.iml
modules.xml
.idea/misc.xml
*.ipr

### macOS ###
# General
.DS_Store
.AppleDouble
.LSOverride

# Icon must end with two \r
Icon



# Thumbnails
._*

# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

### macOS Patch ###
# iCloud generated files
*.icloud

### Windows ###
# Windows thumbnail cache files
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db

# Dump file
*.stackdump

# Folder config file
[Dd]esktop.ini

# Recycle Bin used on file shares
$RECYCLE.BIN/

# Windows Installer files
*.cab
*.msi
*.msix
*.msm
*.msp

# Windows shortcuts
*.lnk

### Compressed ###
*.7z
*.deb
*.gz
*.pkg
*.rar
*.rpm
*.sit
*.sitx
*.tar
*.zip
*.zipx
*.tgz

### Data ###
*.csv
*.dat
*.efx
*.gbr
*.key
*.pps
*.ppt
*.pptx
*.sdf
*.tax2010
*.vcf
*.xml

### Database ###
*.accdb
*.db
*.dbf
*.mdb
*.pdb
*.sqlite3
*.db-shm
*.db-wal
*.sql

### DiskImage ###
*.dmg
*.iso
*.toast
*.vcd

### Images ###
# JPEG
# *.jpg
# *.jpeg
# *.jpe
# *.jif
# *.jfif
# *.jfi

# JPEG 2000
*.jp2
*.j2k
*.jpf
*.jpx
*.jpm
*.mj2

# JPEG XR
*.jxr
*.hdp
*.wdp

# Graphics Interchange Format
*.gif

# RAW
*.raw

# Web P
*.webp

# Portable Network Graphics
# *.png

# Animated Portable Network Graphics
*.apng

# Multiple-image Network Graphics
*.mng

# Tagged Image File Format
*.tiff
*.tif

# Scalable Vector Graphics
# *.svg
# *.svgz

# Portable Document Format
*.pdf

# X BitMap
*.xbm

# BMP
*.bmp
*.dib

# ICO
*.ico

# 3D Images
*.3dm
*.max

### VirtualEnv ###
# Virtualenv
# http://iamzed.com/2009/05/07/a-primer-on-virtualenv/
.Python
[Bb]in
[Ii]nclude
[Ll]ib
[Ll]ib64
[Ll]ocal
[Ss]cripts
pyvenv.cfg
.venv
pip-selfcheck.json

### Video ###
*.3g2
*.3gp
*.asf
*.asx
*.avi
*.flv
*.mkv
*.mov
*.mp4
*.mpg
*.ogv
*.rm
*.swf
*.vob
*.wmv
*.webm

### MicrosoftOffice ###
*.tmp

# Word temporary
~$*.doc*
# *.doc*

# Word Auto Backup File
Backup of *.doc*

# Excel temporary
~$*.xls*
*.xls*

# Excel Backup File
*.xlk

# PowerPoint temporary
~$*.ppt*
*.ppt*

# Visio autosave temporary files
*.~vsd*
