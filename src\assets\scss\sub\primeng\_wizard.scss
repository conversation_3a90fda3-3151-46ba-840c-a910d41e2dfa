// ---------- wizard

.custom-wizard {
  .p-steps ul {
    background-color: #fafafa;
    padding: 1rem 9px;
    @extend .mb-4;
    @extend .rounded-1;
    @extend .border;
    .p-steps-item {
      flex: initial;
      .p-menuitem-link {
        background: #fff;
        padding: 25px 50px 13px 30px;
        position: relative;
        border: 1px solid #d8d8d8;
        margin: 0 -10px 0 20px;
        overflow: visible;
        transition: all 0.3s ease;
        [dir="rtl"] & {
          border-left: 0;
        }
        [dir="ltr"] & {
          padding: 25px 30px 13px 50px;
          margin: 0 20px 0 -10px;
          border-right: 0;
        }
        .p-steps-number {
          width: 40px;
          height: 40px;
          position: absolute;
          top: -13px;
          right: 50px;
          border: 0;
          [dir="ltr"] & {
            right: auto;
            left: 50px;
          }
        }
        .p-steps-title {
          color: $secondary;
          font-size: 1.2rem;
        }
        &:before {
          content: " ";
          display: block;
          width: 55px;
          height: 54px;
          background: #fafafa;
          position: absolute;
          top: 10px;
          left: -40px;
          z-index: -1;
          transform: rotate(45deg);
          border-radius: 4px;
          [dir="rtl"] & {
            border-left: 1px solid #ddd;
            border-bottom: 1px solid #ddd;
          }
          [dir="ltr"] & {
            left: auto;
            right: -40px;
            border-right: 1px solid #ddd;
            border-top: 1px solid #ddd;
          }
        }
        &:after {
          content: " ";
          display: block;
          width: 55px;
          height: 54px;
          background: #fff;
          position: absolute;
          top: 10px;
          left: -26px;
          z-index: -1;
          transform: rotate(45deg);
          border-radius: 4px;
          [dir="rtl"] & {
            border-left: 1px solid #ddd;
            border-bottom: 1px solid #ddd;
          }
          [dir="ltr"] & {
            left: auto;
            right: -25px;
            border-right: 1px solid #ddd;
            border-top: 1px solid #ddd;
          }
        }
      }
      &:before {
        display: none;
      }
      &:first-child {
        .p-menuitem-link {
          padding: 25px 20px 13px 20px;
          .p-steps-number {
            right: 20px;
            [dir="ltr"] & {
              right: auto;
              left: 20px;
            }
          }
        }
      }
      &:last-child {
        .p-menuitem-link:before {
          display: none;
        }
      }
      .p-steps-number {
        overflow: hidden;
        position: relative;
        &:before {
          content: "before step number";
        }
        &:after {
          content: "";
          background-color: $primary;
          background-image: url("data:image/svg+xml;base64,PHN2ZyBhcmlhLWhpZGRlbj0idHJ1ZSIgZm9jdXNhYmxlPSJmYWxzZSIgZGF0YS1wcmVmaXg9ImZhcyIgZGF0YS1pY29uPSJjaGVjayIgY2xhc3M9InN2Zy1pbmxpbmUtLWZhIGZhLWNoZWNrIGZhLXctMTYiIHJvbGU9ImltZyIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2aWV3Qm94PSIwIDAgNTEyIDUxMiI+PHBhdGggZmlsbD0iI2ZmZmZmZiIgZD0iTTE3My44OTggNDM5LjQwNGwtMTY2LjQtMTY2LjRjLTkuOTk3LTkuOTk3LTkuOTk3LTI2LjIwNiAwLTM2LjIwNGwzNi4yMDMtMzYuMjA0YzkuOTk3LTkuOTk4IDI2LjIwNy05Ljk5OCAzNi4yMDQgMEwxOTIgMzEyLjY5IDQzMi4wOTUgNzIuNTk2YzkuOTk3LTkuOTk3IDI2LjIwNy05Ljk5NyAzNi4yMDQgMGwzNi4yMDMgMzYuMjA0YzkuOTk3IDkuOTk3IDkuOTk3IDI2LjIwNiAwIDM2LjIwNGwtMjk0LjQgMjk0LjQwMWMtOS45OTggOS45OTctMjYuMjA3IDkuOTk3LTM2LjIwNC0uMDAxeiI+PC9wYXRoPjwvc3ZnPg==");
          background-repeat: no-repeat;
          background-size: 25px auto;
          background-position: center 8px;
          width: 100%;
          height: 100%;
          display: block;
          position: absolute;
          top: 0;
          left: 0;
          z-index: 2;
        }
      }
      &.p-highlight {
        .p-menuitem-link {
          background: #3bad8e;
          .p-steps-title {
            color: #fff;
            font-weight: 400;
          }
          &:after {
            background: #3bad8e;
          }
        }
        .p-steps-number {
          background: #0064b0;
          color: #fff;
        }
      }
      &.step-1 {
        z-index: 5;
      }
      &.step-2 {
        z-index: 4;
      }
      &.step-3 {
        z-index: 3;
      }
      &.step-4 {
        z-index: 2;
      }
      &.step-5 {
        z-index: 1;
      }
    }
  }
  .p-disabled,
  .p-component:disabled {
    opacity: 1;
  }
}
