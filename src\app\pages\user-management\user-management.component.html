<p-confirmDialog></p-confirmDialog>
<main class="pt-1">
  <div class="container-xxl">
    <div class="d-flex justify-content-between align-items-end mb-3">
      <div>
        <h3 class="text-primary">إدارة المستخدمين</h3>
        <nav aria-label="breadcrumb m-0">
          <ol class="breadcrumb pt-1">
            <li class="breadcrumb-item"><a [routerLink]="['/']">الرئيسية</a></li>
            <li class="breadcrumb-item active" aria-current="page">
              <b>
                إدارة المستخدمين
              </b>
            </li>
          </ol>
        </nav>
      </div>


      <div>
        <button class="btn btn-outline-primary me-2" *ngIf="userRoleId == bogSuperAdmin" routerLink="/user-managements/create-with-user-profile">إنشاء مستخدم دعم Elm</button>
        <button class="btn btn-primary" (click)="createUser.show()">إنشاء</button>
      </div>
    </div>

    <div class="card">

      <div class="card-body">



        <p-table #userTable [value]="usersResponse?.records!" [responsiveLayout]="'scroll'"
          [totalRecords]="usersResponse?.totalRecords!" [rows]="usersResponse?.pageSize!" [paginator]="true"
          (onLazyLoad)="loadData($event)" [lazy]="true" class="p-datatable-sm">
          <ng-template pTemplate="header">
            <tr>
              <th>الاسم</th>
              <th>البريد الإلكتروني</th>
              <th>الحالة</th>
              <th></th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-user>
            <tr>
              <td>{{ user.name }}</td>
              <td>{{ user.email }}</td>
              <td>
                <i class="pi"
                  [ngClass]="{'pi-check-circle text-success': user.isActive, 'pi-times-circle text-danger': !user.isActive}"
                  style="font-size: 1.2em;">
                </i>
              </td>
              <td>

                <p-menu #menu [model]="menuItems" [popup]="true" appendTo="body"></p-menu>
                <button pButton type="button" icon="pi pi-ellipsis-v" class="p-button-text"
                  (click)="menu.toggle($event); initializeMenu(user)">
                </button>
              </td>
            </tr>
          </ng-template>
        </p-table>
      </div>
    </div>


  </div>
</main>

<app-view-user-details #userDetail />
<app-update-user #updateUser (onSave)="reloadList()" />
<app-create-user #createUser (onSave)="reloadList()" /> 