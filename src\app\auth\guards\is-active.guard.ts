import {Injectable} from '@angular/core';
import {ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot, UrlTree} from '@angular/router';
import {AuthService} from "../auth.service";

@Injectable({
  providedIn: 'root',
})
export class IsActiveGuard implements CanActivate {
  constructor(private authService: AuthService, private router: Router) {
  }

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): boolean | UrlTree {
    return this.checkIsActive();
  }

  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): boolean | UrlTree {
    return this.checkIsActive();
  }

  private checkIsActive(): boolean | UrlTree {
    const isActive = localStorage.getItem('isActive');
    if (isActive === 'false') {
      return this.router.createUrlTree(['/deactivated-account']);
    }
    return true;
  }
}
