import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { LoginResponse, OidcSecurityService } from 'angular-auth-oidc-client';
 import { AuthService } from '../auth.service';
import {AppConfigService} from "../../shared/app-config.service";
import {take} from "rxjs";

@Component({
  selector: 'auth-checker',
  templateUrl: './auth-checker.component.html'
})
export class AuthCheckerComponent implements OnInit {

  @Input() allowedUserType!: 'internal' | 'external';
  protected router!: Router;

  constructor(
    private oidcSecurityService: OidcSecurityService,
    private appConfigService: AppConfigService,
    protected authService: AuthService) {
    this.authService.allowedUserType = this.allowedUserType;
  }
  protected isLogging!: boolean;
  ngOnInit(): void {
    this.authService.checkIsLogging.subscribe(isLogging => this.isLogging = isLogging);
    this.checkAuth();
  }

  private checkAuth() {
    this.appConfigService.onConfigLoaded().pipe(take(1)).subscribe({
      next: () => {
        this.oidcSecurityService.checkAuth().subscribe((response) => {
          this.authService.isAuthenticated = response.isAuthenticated;
          this.authService.isUserTypeValid = this.isValidUserType(response);

          if (!response.isAuthenticated) {
            this.authService.resetSessions();
            return;
          }

          if (!this.authService.isUserTypeValid) {
            this.authService.logout();
            return;
          }

          localStorage.setItem('udata', JSON.stringify(response.userData));
          localStorage.setItem('accessToken', response.accessToken);
          this.updateSessionStorageToken();
        });
      },
      error: (error) => {
        console.error('Config loading error:', error);
      },
    });
  }

  private isValidUserType(response: LoginResponse): boolean {
    const isExternal = response.userData?.IsExternal === "True";
    const isInternal = response.userData?.IsExternal === "False";
    const isAllowedUserType =
      isExternal && this.allowedUserType === 'external' ||
      isInternal && this.allowedUserType === 'internal';

    return isAllowedUserType;
  }

  updateSessionStorageToken() {
    setTimeout(() => {
      this.oidcSecurityService.getAccessToken().subscribe(res => {
        localStorage.setItem("accessToken", res);
        this.updateSessionStorageToken();
      });
    }, 10000);
  }

}
