// ---------- data Table

body .p-datatable table {
  table-layout: auto;
}
body .p-datatable .p-datatable-thead > tr > th {
  // background: #e4e4e430;
  background: #F8FAFA;
  color: #000000;
}
.p-paginator,
.p-datatable .p-datatable-tbody > tr {
  background: transparent;
  color: #666666;
  border-bottom: 1px solid #F2F2F2;
  .more-button {
    opacity: 0;
    // border-radius: 50% !important;
    // background-color: #F2F2F1;
    // color: #6B6B6B;
    // border-color: transparent;
    // padding: 0.2rem 0 !important;
    &:hover, &:focus {
        // background-color: #F2F0EB !important;
        // box-shadow: none !important;
        // color: #6B6B6B !important;
    }
}
&:hover {
    // background-color: #FAFAFA;
    .more-button {
        opacity: 1;
    }
}
@media screen and (min-width:0) and (max-width:992px) {
    .more-button {
        opacity: 1;
    }
}
}
body .p-datatable .p-datatable-thead > tr > th,
body .p-datatable .p-datatable-tbody > tr > td {
  text-align: right;
  [dir="ltr"] & {
    text-align: left;
  }
}
.table-actions {
  @extend .text-end;
  // padding: .2rem .5rem .5rem;
  // & + p-menu{
  //     display: none;
  //     position: absolute;
  //     top: 30px;
  //     left: 0;
  //     background: #fff;
  //     z-index: 2;
  //     .p-menu{
  //         width: 120px;
  //         padding: 0;
  //     }
  //     [dir="ltr"] & {
  //         left: auto;
  //         right: 0;
  //     }
  // }
  // // &:hover,
  // &:focus{
  //     background-color:#f3f3f3 ;
  //     & + p-menu{
  //         display: block;
  //     }
  // }
}
body .p-datatable .p-sortable-column.p-highlight:hover,
body .p-datatable .p-sortable-column.p-highlight,
body .p-datatable .p-sortable-column:not(.p-highlight):hover {
  background: darken($primary, 10%);
  color: #f3f3f3;
  outline: none;
  box-shadow: 0 0 0;
}
body .p-datatable .p-sortable-column.p-highlight:hover .p-sortable-column-icon,
body
  .p-datatable
  .p-sortable-column:not(.p-highlight):hover
  .p-sortable-column-icon,
body .p-datatable .p-sortable-column .p-sortable-column-icon,
body .p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon {
  color: #fff;
}
[dir="rtl"] {
  .p-datatable .p-sortable-column .p-sortable-column-icon {
    margin-right: 0.5rem;
    margin-left: 0;
  }
}
// .p-paginator .more-button,
// .p-datatable .p-datatable-tbody > tr .more-button {
//   color: #fff;
//   width: 36px;
//   height: 36px;
//   border-radius: 50% !important;
//   padding: 9px;
//   background-color: white;
//   border-color: transparent;
//   .pi {
//     color: #629b78;
//   }
//   &:focus,
//   &:hover {
//     background-color: #b6d1c1;
//     border-color: transparent;
//     .pi {
//       color: #fff;
//     }
//   }
// }

// @media screen and (min-width: 0) and (max-width: 992px) {
//   .p-datatable .p-datatable-tbody > tr > td > .p-column-title {
//     font-weight: 700 !important;
//     padding-left: 1rem;
//     [dir="ltr"] & {
//       padding-left: 0;
//       padding-right: 1rem;
//     }
//   }
//   body table td.table-actions {
//     padding-bottom: 20px !important;
//   }
//   body .p-datatable .p-datatable-tbody > tr > td {
//     justify-content: flex-start !important;
//     padding: 0.7rem 0;
//   }
// }

@media screen and (min-width:0) and (max-width:992px) {  
  .p-datatable .p-datatable-tbody>tr>td>.p-column-title{ 
      font-weight: 700 !important;
      padding-left: 1rem; 
      display: flex;
      [dir="ltr"] & {
          padding-left: 0; 
          padding-right: 1rem; 
      }
  } 
  body table td.table-actions {
      padding-bottom: 20px!important;
  }
  body .p-datatable .p-datatable-tbody > tr > td{ 
      justify-content: flex-start !important;
      padding: .7rem 0; 
  } 
}
@media screen and (max-width: 960px) {
  .p-datatable .p-datatable-thead > tr > th, 
  .p-datatable .p-datatable-tfoot > tr > td {
      display: none !important;
  }
.p-datatable .p-datatable-tbody > tr > td:not(:last-child) {
  border: 0 none;
  display: flex !important;
  width: 100% !important;
  align-items: center !important;
  justify-content: space-between !important;
}
.p-datatable .p-datatable-tbody > tr > td:first-child {
display: flex !important;
}
.p-datatable-tbody > tr > td {
  display: flex !important;
  width: 100% !important;
  align-items: center !important;
  justify-content: space-between !important;
}
.p-datatable .p-datatable-tbody>tr {
  display: block !important;
  background: transparent; 
  color: #666666;
  border: 2px solid #ddd;
  margin: 0 0 1rem;
  padding: 1rem;
  border-radius: 1rem;
}
}
