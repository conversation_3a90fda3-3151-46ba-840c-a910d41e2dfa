import { Injectable } from '@angular/core';
import { Subject } from "rxjs";
import { share } from 'rxjs/operators';

export interface KeyValuePair {
  key: string,
  value: any
}

@Injectable({
  providedIn: 'root'
})
export class StorageService {

  private onSubject = new Subject<KeyValuePair>();
  public changes = this.onSubject.asObservable().pipe(share());

  constructor() {
    this.start();
  }

  ngOnDestroy() {
    this.stop();
  }

  public getStorage(): Array<KeyValuePair> {
    let s: Array<KeyValuePair> = [];
    for (let i = 0; i < localStorage.length; i++) {
      const keyValuePair: KeyValuePair = {
        key: localStorage.key(i) ?? '',
        value: JSON.parse(localStorage.getItem(localStorage.key(i) ?? '') as string)
      }
      s.push(keyValuePair);
    }
    return s;
  }

  public getObj<IType = string>(key: string): IType | null {
    const value = localStorage.getItem(key);
    if (value) {
      try {
        const parsedValue = JSON.parse(value) as IType;
        return parsedValue;
      } catch {
        return null;
      }
    }
    return null;
  }

  public getItem(key: string): any {
    const value = localStorage.getItem(key);
    return value;
  }
  public setItem(item: KeyValuePair): void {
    localStorage.setItem(item.key, item.value);
  }

  public store(item: KeyValuePair): void {
    localStorage.setItem(item.key, JSON.stringify(item.value));
    // the local application doesn't seem to catch changes to localStorage...
    this.onSubject.next(item)
  }

  public clear(key: string): void {
    localStorage.removeItem(key);
    // the local application doesn't seem to catch changes to localStorage...
    this.onSubject.next({ key: key, value: null });
  }


  private start(): void {
    window.addEventListener("storage", this.storageEventListener.bind(this));
  }

  private storageEventListener(event: StorageEvent): void {
    if (event.storageArea == localStorage) {
      let v;
      try {
        v = JSON.parse(event.newValue ?? '');
      } catch (e) {
        v = event.newValue;
      }
      this.onSubject.next({ key: event.key ?? '', value: v });
    }
  }

  private stop(): void {
    window.removeEventListener("storage", this.storageEventListener.bind(this));
    this.onSubject.complete();
  }
}
