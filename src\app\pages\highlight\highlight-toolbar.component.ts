import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { HighlightService } from '../../services';

@Component({
  standalone: true,
  selector: 'app-highlight-toolbar',
  template: `
    <div class="toolbar">
    	<button (click)="highlightSelection()">Highlight</button>
    	<button (click)="clear()">Clear Highlights</button>
    </div>`,
  styles: [`
    .toolbar {
        margin-bottom: 1rem;
        display: flex;
        gap: 1rem;
    }
    button {
        cursor: pointer;
    }`],
    imports: [CommonModule, FormsModule],  // import what this component uses
})
export class HighlightToolbarComponent {
  @Input() section!: 'Actions' | 'Reasons' | 'Verdict';

  constructor(private highlightService: HighlightService) { }

  // highlightSelection() {
  //   const selection = window.getSelection();
  //   if (!selection || selection.rangeCount === 0) return;

  //   const range = selection.getRangeAt(0);
  //   const text = selection.toString();
  //   const startOffset = range.startOffset;
  //   const endOffset = range.endOffset;

  //   if (text.trim()) {
  //     this.highlightService.addHighlight(this.section, text, startOffset, endOffset);
  //   }

  //   selection.removeAllRanges();
  // }

  // clear() {
  //   this.highlightService.clearHighlights(this.section);
  // }
}
