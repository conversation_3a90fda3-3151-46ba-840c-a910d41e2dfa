import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { UsersListQueryResult } from '../../../services';
import { BaseForm, CustomFormControl, EMAIL } from '../../../shared/public.api';
import { Validators } from '@angular/forms';
import { UserManagementBase } from '../user-management-base.component';

@Component({
  selector: 'app-update-user',
  templateUrl: './update-user.component.html',
  styleUrls: ['./update-user.component.css']
})
export class UpdateUserComponent extends UserManagementBase implements OnInit {

  constructor() {
    super('updateUser');
  }

  protected controls = new Control();
  protected showEditDialog: boolean = false;
  protected editedUser!: UsersListQueryResult;

  @Output('onSave') onSave = new EventEmitter<boolean>(false);

  ngOnInit() {
  }

  open(editedUser: UsersListQueryResult) {
    this.showEditDialog = true;
    this.editedUser = editedUser;
    this.controls.form.patchValue({
      ...editedUser,
      email: editedUser.email?.trim(),
      name: editedUser.name?.trim()
    });
  }

  save() {

    if (this.controls.isInvalid) {
      return;
    }

    const isActive = this.controls.isActive.value;

    this._userManagementService.update({
      id: this.editedUser.id,
      email: this.controls.email.value,
      name: this.controls.name.value,
      isActive: isActive == null ? false : isActive
    }).subscribe({
      next: (response) => {
        if (response.isSuccess) {
          this.showEditDialog = false;
          this._messageService.add({
            severity: 'success',
            summary: 'نجحت العملية',
            detail: 'تم تحديث بيانات المستخدم بنجاح'
          });

          this.onSave.emit(true);
        }
      }
    })
  }

}

class Control extends BaseForm {
  public readonly name = new CustomFormControl('name', [Validators.required, Validators.minLength(2), Validators.maxLength(50), Validators.pattern(/^[a-zA-Z\u0621-\u064A\s]*$/)]).setForm(this.form);
  public readonly email = new CustomFormControl('email', [Validators.required, Validators.pattern(EMAIL)]).setForm(this.form);
  public readonly isActive = new CustomFormControl('isActive').setForm(this.form);
}
