$form-control-height: $control-height;
.p-component,
.p-inputtext {
    font-family: $family;
}
 
// ---------- validation
 
.ng-invalid.ng-dirty{  
    @extend .is-invalid; 
}

// .ng-valid{  
//     @extend .is-valid; 
// }

p-dropdown,
p-multiselect{
    &.is-invalid, 
    &.ng-invalid.ng-dirty{
        padding: 0!important;
        background: none!important;
        border: 0!important;
        & > .p-component{
            border: 1px solid #ff3b00!important;  
        }
    }
}
form{
    &.is-invalid, 
    &.ng-invalid.ng-dirty{
        padding: 0!important;
        background: none!important;
        border: 0!important;
    }
}
.p-inputtext{
    @extend .form-control;
}