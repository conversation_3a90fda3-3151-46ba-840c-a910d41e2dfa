# This file is used to take project/repository specific metadata e.g. Nexus IQ, ACS Scanning
name: pipeline-ci
version: 1.0.0
scanners:
  nexusiq:
    enabled: true
    dockerimage: node:20.9.0-buster
    buildcommand: npm install --production --cache /cache/npm-cache --force --verbose --no-audit 
    scantarget: package-lock.json
    targetdir: .
  acs:
    enabled: true
stages:
  skipmaster: No
  skipci: No
