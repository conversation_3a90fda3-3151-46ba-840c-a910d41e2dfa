// ---------- Radiobutton &  Checkbox
.p-field-checkbox,
.p-field-radiobutton {
  @extend .pe-5;
  @extend .py-1;
  // line-height: 1;
  label {
    @extend .ps-2;
  }
}
// .p-checkbox .p-checkbox-box,
// .p-radiobutton .p-radiobutton-box {
//   background: transparent;
// }
.p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box.p-focus,
.p-radiobutton:not(.p-radiobutton-disabled) .p-radiobutton-box.p-focus {
  border-color: $primary;
  box-shadow: none;
}
.p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box.p-highlight:hover,
.p-checkbox .p-checkbox-box.p-highlight,
.p-radiobutton .p-radiobutton-box.p-highlight {
  border-color: $primary;
  background: $primary;
}
.p-radiobutton:not(.p-radiobutton-disabled)
  .p-radiobutton-box.p-highlight:hover,
.p-radiobutton .p-radiobutton-box:not(.p-disabled):not(.p-highlight):hover,
.p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box:hover {
  border-color: $primary;
}
.p-radiobutton .p-radiobutton-box .p-radiobutton-icon {
  background-color: $white;
  color: $primary;
}
@media screen and (min-width: 0) and (max-width: 767px) {
  fieldset.d-flex {
    flex-wrap: wrap !important;
  }
}
