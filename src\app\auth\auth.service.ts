import { HttpClient } from '@angular/common/http';
import { EventEmitter, Injectable, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { LoginResponse, OidcSecurityService } from 'angular-auth-oidc-client';
import {BehaviorSubject, Observable, Subject, Subscription, interval, of, map} from 'rxjs';
import { UserProfile } from './models/user-profile.interface';
import { Location } from '@angular/common';
import { ApiResponse, AppConfigService, ILookupDetailsResult } from '../shared/public.api';
import {Role} from "../shared/enums/role.enum";
import {catchError} from "rxjs/operators";
import {Router} from "@angular/router";

@Injectable({
  providedIn: 'root'
})
export class AuthService implements OnInit, OnDestroy {
  private checkActivation$: Subject<LoginResponse> = new Subject<LoginResponse>();
  public checkActivationListner = this.checkActivation$.asObservable();

  private accountActive: Subject<boolean> = new Subject<boolean>();
  public accountActiveListner = this.accountActive.asObservable();

  public allowedUserType!: 'internal' | 'external';
  private userProfileSubscription!: Subscription;
  private _userProfile!: UserProfile
  private _userProfileSubject = new BehaviorSubject<UserProfile | null>(null);
  public get userProfileObservable() { return this._userProfileSubject.asObservable() }
  public get UserProfile(): UserProfile {
    const userProfile = localStorage.getItem('user-profile');
    if (userProfile) {
      this._userProfile = JSON.parse(userProfile);
    }
    return this._userProfile;
  }

  public get currentNetworkUser$(): Observable<string | null> {
    return this.oidcSecurityService.userData$.pipe(
      map((userData) => userData?.userData?.networkUser ?? null)
    );
  }

  get accessToken() {
    return 'Bearer ' + localStorage.getItem("accessToken");
  }

  public get isLogging(): boolean {

    var isLogging = localStorage.getItem('isLogging');
    if (isLogging === undefined || isLogging === null)
      return false;
    else if (isLogging == 'true')
      return true;
    else
      return false;
  }

  //#region ▬ isAuthenticated ▬
  private _isAuthenticated: boolean = false;
  public get isAuthenticated(): boolean {
    return this._isAuthenticated;
  }
  public set isAuthenticated(v: boolean) {
    this._isAuthenticated = v;
    this.isAuthenticatedSubject.next(v);

  }
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);
  public get checkIsAuthenticated() { return this.isAuthenticatedSubject.asObservable() }
  //#endregion

  //#region ▬ isUserTypeValid ▬

  private _isUserTypeValid: boolean = false;
  public get isUserTypeValid(): boolean {
    return this._isUserTypeValid;
  }
  public set isUserTypeValid(v: boolean) {
    this._isUserTypeValid = v;
    this.isUserTypeValidSubject.next(v);
  }

  private isUserTypeValidSubject = new BehaviorSubject<boolean>(false);
  public get checkIsUserTypeValid() { return this.isUserTypeValidSubject.asObservable() }
  public setUserTypeValidation(isUserTypeValid: boolean) {
    this.isUserTypeValidSubject.next(isUserTypeValid);
  }

  //#endregion

  //#region ▬ isLogging ▬
  private isLoggingSubject = new BehaviorSubject<boolean>(localStorage.getItem('isLogging') as boolean | null ?? false);
  public get checkIsLogging() { return this.isLoggingSubject.asObservable() }
  public setUserLogging(isLogging: boolean) {
    localStorage.setItem('isLogging', JSON.stringify(isLogging))
    this.isLoggingSubject.next(isLogging);
  }
  //#endregion

  public initializeAuthState() {
    const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true';
    this.isAuthenticatedSubject.next(isAuthenticated);
  }

  constructor(protected httpClient: HttpClient, private oidcSecurityService: OidcSecurityService, private appConfig: AppConfigService, private location: Location) {
    const checkConfiguration = {
      count: 0,
      maxCount: 30
    }
    this.userProfileSubscription = interval(800).subscribe(() => {

      const userProfile = localStorage.getItem('user-profile');
      if (userProfile) {
        const parsedUserProfile = JSON.parse(userProfile);
        if (parsedUserProfile !== this._userProfile) {
          this._userProfile = JSON.parse(userProfile);
          this._userProfileSubject.next(this._userProfile);
        }
        checkConfiguration.count++;

        if (checkConfiguration.count == checkConfiguration.maxCount) {
          this.userProfileSubscription.unsubscribe();
        }
      }
    });

  }
  ngOnDestroy(): void {
    this.userProfileSubscription.unsubscribe();
  }
  ngOnInit(): void {  }

  public login() {
    try {
      if (!this.oidcSecurityService) {
        throw new Error('OIDC Security Service is not initialized');
      }

      this.setUserLogging(true);
      this.oidcSecurityService.authorize();
    } catch (error) {
      console.error('Login error:', error);
    }
  }
  private _loginCallback() {
    return this.httpClient.get<ApiResponse<UserProfile>>(`${this.appConfig.apiBaseUrl}/api/auth/login-callback`);
  }

  public loginCallback(): Observable<UserProfile | null> {
    return this._loginCallback().pipe(
      map((response: ApiResponse<UserProfile>) => {
        if (!response || !response.data) {
          return null;
        }

        this.setUserLogging(false);
        localStorage.setItem("user-profile", JSON.stringify(response.data));

        localStorage.setItem("isAuthenticated", true.toString());
        localStorage.setItem("isActive", response.data.isActive.toString());

        if (response.data?.currentUserProfile) {
          const profileRef = response.data.currentUserProfile.reference ?? '';
          localStorage.setItem("current-profile", profileRef);
        }

        this._userProfile = response.data;
        this._userProfileSubject.next(response.data);
        this.setAccountActivation(response.data.isActive);

        return response.data;
      }),
      catchError((error) => {
        this.logoutLocal();
        return of(null);
      })
    );
  }


  // public switchProfile(profileId: string) {
  //   localStorage.setItem("current-profile", profileId);
  //   this.loginCallback(() => {
  //     this.location.replaceState('/');
  //     location.reload()
  //   }
  //   );
  // }

  public setActive(active: boolean) {
    var userProfileSession = localStorage.getItem("user-profile");
    if (!userProfileSession) return;

    var userProfile = JSON.parse(userProfileSession) as UserProfile;
    userProfile.isActive = active;
    localStorage.setItem("user-profile", JSON.stringify(userProfile));
    localStorage.setItem("isActive", active.toString());
    this.setAccountActivation(active);
  }


  public logout() {
    this.resetSessions();
    this.oidcSecurityService.logoff().subscribe(response => { });
  }

  public logoutLocal() {
    this.oidcSecurityService.logoffLocal();
    this.isAuthenticated = false;
    this.resetSessions();
  }

  public resetSessions(): void {
    localStorage.removeItem("udata");
    localStorage.removeItem("accessToken");
    localStorage.removeItem("user-profile");
    localStorage.removeItem("current-profile");
    localStorage.removeItem("isLogging");
    localStorage.removeItem("isActive");
    localStorage.removeItem("isAuthenticated");
  }

  /**
   * Checks if the current user has specific permissions.
   * @param permission The permissions to check.
   * @returns True if the user has at least one of the specified permissions, false otherwise.
   */
  public hasPermission(...permission: number[]): boolean {
    if (!this.UserProfile || permission?.length == 0)
      return false;

    return this.UserProfile.permissions.some(per => permission.includes(per));
  }

  /**
   * Checks if the current user has specific roles.
   * @param role The roles to check.
   * @returns True if the user has at least one of the specified roles, false otherwise.
   */
  public hasRole(...roles: ILookupDetailsResult[]): boolean {
    if (!this.UserProfile || roles?.length == 0)
      return false;

    return roles.findIndex(x => x.id == this.UserProfile.currentUserProfile?.role.id) > -1;
  }



  public setAccountActivation(active: boolean): void {
    this.accountActive.next(active);
  }

  public checkAccountActivation(value: LoginResponse): void {
    this.checkActivation$.next(value);
  }

  public isAdmin(): boolean {
    const userProfile = this.UserProfile;
    if (userProfile && (userProfile.currentUserProfile?.role?.id === Role.ADMIN || userProfile.currentUserProfile?.role?.id === Role.SuperAdmin || userProfile.currentUserProfile?.role?.id === Role.ElmSupportAdmin)) {
      return true;
    }
    return false;
  }
}

