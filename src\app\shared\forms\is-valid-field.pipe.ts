import { Pipe } from "@angular/core";
import { FormGroup, FormGroupDirective } from "@angular/forms";

@Pipe({
    name: "isValidField",
    standalone: true,
    pure: false
})
export class IsValidFieldPipe {
    constructor(private _formGroupDirective: FormGroupDirective) { }
    transform(formGroup: FormGroup, formControlName: string, errorsIn: string[] = [], errorsOut: string[] = []): boolean {
        if (formGroup && formControlName && errorsIn && errorsIn.length > 0 && errorsOut) {
            let hasErrorIn = false;
            let hasErrorOut = false;

            for (let error of errorsIn) {
                if (formGroup?.get(formControlName)?.hasError(error) && formGroup?.get(formControlName)?.touched) {
                    hasErrorIn = true;
                    break;
                }
            }

            for (let error of errorsOut) {
                if (formGroup?.get(formControlName)?.hasError(error) && formGroup?.get(formControlName)?.touched) {
                    hasErrorOut = true;
                    break;
                }
            }

            if (errorsOut.length > 0 && errorsIn.length > 0 && this._formGroupDirective.submitted) {
                formGroup.markAllAsTouched();
            }

            return hasErrorIn && !hasErrorOut;
        } else {
            return false;
        }
    }
}