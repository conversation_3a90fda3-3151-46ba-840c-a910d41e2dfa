//export const MOBILE_NUMBER_REGEX = '^05[0-9]{1}-[0-9]{7}$';
export const MOBILE_NUMBER_REGEX = '^05[0-9]{8}';
export const MOBILE_NUMBER_WITHOUT_ZERO_REGEX = '^5[0-9]{8}';
export const PHONE_NUMBER_REGEX = '^01[0-9]{1}\-[0-9]{3}\-[0-9]{4}$';
export const FAX_NUMBER_REGEX = '^01[0-9]{1}\-[0-9]{3}\-[0-9]{4}$';

export const ARABIC_INPUT_REGEX = '^[\u0621-\u064A\u0660-\u0669 ]*$';
export const ARABIC_LETTERS_REGEX = '^[\u0621-\u064A ]*$';
export const ENGLISH_INPUT_REGEX = '^[a-zA-Z0-9 ]*$';
export const LETTERS_ONLY_REGEX = '^[a-zA-Z\u0621-\u064A ]*$'
export const LETTERS_NUMBER_ONLY_REGEX = '^[a-zA-Z\u0621-\u064A0-9 ]*$'
export const English_LETTERS_NUMBER_ONLY_REGEX = '^[a-zA-Z0-9]*$'

export const GENERAL_ID_NUMBER_REGEX = '^[A-Za-z0-9]{1,20}$';
export const RESIDENT_ID_NUMBER_REGEX = '^(2)[0-9]{9}$';
export const SAUDI_ID_NUMBER_REGEX = '^(1)[0-9]{9}$';
export const SAUDI_RESIDENT_ID_NUMBER_REGEX = '^(1|2)[0-9]{9}$';
export const MOI_NUMBER_REGEX = '^(1|2|7)[0-9]{9}$';
export const PASSPORT_NUMBER_REGEX = '^(?=\\D*\\d)[a-zA-Z0-9]{6,15}$';
export const GCC_ID_NUMBER_REGEX = '^(3|4)[0-9]{9}$';
export const BORDER_NUMBER_REGEX = '^(3|4)[0-9]{9}$';
export const IQAMA_NUMBER_REGEX = '^2[0-9]{9}$';


export const ONLY_NUMBERS = '^[0-9]*$';
export const ONLY_NUMBERS_With_Dot = '^[0-9.]*$';
export const OCCUPATION_CODE_REGEX = '^(1|2)[0-9]{5}$';
export const OCCUPATION_CODE_REGEX_UPDATED = '^[0-9]{6}$';
export const DOUBLE_REGEX = '^[0-9]+(\\.[0-9]+)?$';
export const USERNAME_REGEX = '^[_.@A-Za-z0-9-]*';
export const INTERNATIONAL_MOBILE_PHONE_REGEX = '^[0-9]{5,20}$';

export const CURRENCY_REGEX = "^(?=.*[1-9])\\d{1,6}(?:\\.\\d{1,2})?$"//"^[1-9]{1,18}(\\.\\d{0,2})?$";

export const REFERENCE_IQAMA_NUMBER = '^([Rr]{2})?[0-9]{1,10}$';
export const LINK_REGEX = '^(https?:\\/\\/)?(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([^\\s]*)';
//export const EMAIL = '^((?=(?<word>\\w+))\\k<word>([\\.-])?)*\\w+@(\\k<word>([\\.-])?)*\\w+(\\.\\w{2,63})+$';
export const EMAIL= "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*\\.[a-zA-Z]{2,}$";
//export const EMAIL= "^((([a-z]|\\d|[\\*\\-_`{\\|}~]|[a-zA-Z0-9])+(\\.([a-z]|\\d|[\\*\\-_`{\\|}~]|[a-zA-Z0-9])+)*)|((\\x22)((((\\x20|\\x09)*(\\x0d\\x0a))?(\\x20|\\x09)+)?(([\\x01-\\x08\\x0b\\x0c\\x0e-\\x1f\\x7f]|\\x21|[\\x23-\\x5b]|[\\x5d-\\x7e]|[a-zA-Z0-9])|(\\\\([\\x01-\\x09\\x0b\\x0c\\x0d-\\x7f]|[a-zA-Z0-9]))))*(((\\x20|\\x09)*(\\x0d\\x0a))?(\\x20|\\x09)+)?(\\x22)))@((([a-z]|\\d|[a-zA-Z0-9])|(([a-z]|\\d|[a-zA-Z0-9])([a-z]|\\d|-||_|~|[a-zA-Z0-9])*([a-z]|\\d|[a-zA-Z0-9])))\\.)+(([a-z]|[a-zA-Z0-9])+|(([a-z]|[a-zA-Z0-9])+([a-z]+|-|\\d*|\\.{0,1}|_|~|\\d*|[a-zA-Z0-9])?([a-z]|\\d*|[a-zA-Z0-9])))$";
export const ONLY_LETTER_ENGLISH_ARABIC = '^[a-zA-Z أ-ي]+';
//#region social media

export const FACEBOOK_LINK = '^(https:\/\/(?:(www.))?facebook.com\/(?![a-zA-Z0-9_.]+\/)([a-zA-Z0-9_.]+))+$';
export const TWITTER_LINK = '^(https:\/\/(?:(www.))?twitter.com\/(?![a-zA-Z0-9_]+\/)([a-zA-Z0-9_]+))+$';
export const INSTAGRAM_LINK = '^(https:\/\/(?:(www.))?instagram.com\/(?![a-zA-Z0-9_.]+\/)([a-zA-Z0-9_.]+))+$';
export const LINKEDIN_LINK = '^(https:\/\/(?:(www.))?linkedin.com\/(?![a-zA-Z0-9_.]+\/)([a-zA-Z0-9_.]+))+$';

export const IELTSScore_REGex = "^(?=.*[1-9])\\d{1,2}(?:\\.\\d{1,2})?$"//"^[1-9]{1,18}(\\.\\d{0,2})?$";

//#endregion

//#region CR

export const CR_UnifiedNationalNumber = '^7[0-9]{9}$';
export const UnifiedNationalNumber = '^7[0-9]{9}$';
export const AMOUNT = '^(?!0\d|$)[0-9]{0,10}(?:\.[0-9]{1,2})?$';

export const PASSWORD = '^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[~!@#$%^&*()\\-_=+\\[\\]{}|;:,.<>\\/?])(?!.*(.)\\1\\1)[A-Za-z0-9~!@#$%^&*()\\-_=+\\[\\]{}|;:,.<>\\/?]{10,}$';




