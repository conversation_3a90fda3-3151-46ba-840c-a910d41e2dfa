/**
 * MOSAED.Internal.Api
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { SearchInLawByQuestionResult } from './search-in-law-by-question-result';
import { SearchInCaseByQuestionResult } from './search-in-case-by-question-result';
import { SearchType } from './search-type';


export interface SearchByQuestionCommandResult { 
    caseResult: SearchInCaseByQuestionResult;
    lawResult: SearchInLawByQuestionResult;
    searchType: SearchType;
}
export namespace SearchByQuestionCommandResult {
}


