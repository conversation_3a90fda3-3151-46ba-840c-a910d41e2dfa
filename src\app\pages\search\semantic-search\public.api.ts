import { SearchComponent } from "../search.component";
import { SearchCaseDetailsComponent } from "./search-case-details/search-case-details.component";
import { SearchFullScreenComponent } from "./search-full-screen/search-full-screen.component";
import { SearchResultListComponent } from "./search-result-list/search-result-list.component";
import { SearchSmallScreenComponent } from "./search-small-screen/search-small-screen.component";
import { SemanticSearchContainerComponent } from "./semantic-search-container/semantic-search-container.component";
import { SemanticSearchLandingFileComponent } from "./semantic-search-landing/semantic-search-landing-file/semantic-search-landing-file.component";
import { SemanticSearchLandingTextComponent } from "./semantic-search-landing/semantic-search-landing-text/semantic-search-landing-text.component";
import { SemanticSearchLandingComponent } from "./semantic-search-landing/semantic-search-landing.component";


const landing_components = [
    SemanticSearchLandingComponent,
    SemanticSearchLandingFileComponent,
    SemanticSearchLandingTextComponent,
]
export const semantic_search_components = [
    SearchComponent,
    SemanticSearchContainerComponent,
    SearchFullScreenComponent,
    SearchSmallScreenComponent,
    SearchResultListComponent,
    SearchCaseDetailsComponent,
    ...landing_components

]