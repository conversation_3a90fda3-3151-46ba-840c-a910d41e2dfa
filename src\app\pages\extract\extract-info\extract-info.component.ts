import {
  Component,
  OnInit,
  ViewChild,
  ElementRef,
  AfterViewChecked,
  inject,
} from '@angular/core';
import { <PERSON><PERSON>anitizer, SafeHtml } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { SearchTypeEnum } from '../../../shared/models/search-type.enum';
import { SemanticService } from '../../../services';
import { SearchResultListComponent } from '../../search/semantic-search/search-result-list/search-result-list.component';

@Component({
  selector: 'app-extract-info',
  templateUrl: './extract-info.component.html',
  styleUrls: ['./extract-info.component.scss'],
})
export class ExtractInfoComponent implements OnInit, AfterViewChecked {
  // Add SearchTypeEnum for template access
  protected SearchTypeEnum = SearchTypeEnum;
  // Add searchType property
  protected searchType: SearchTypeEnum = SearchTypeEnum.byLaw;
  messages = [];
  // Inject SemanticService
  protected readonly semanticService = inject(SemanticService);

  value: string | undefined;
  protected caseText!: string;
  protected verdict!: string;
  protected law!: string;
  protected caseDate!: string;
  private readonly router = inject(Router);
  private readonly sanitizer = inject(DomSanitizer);

  protected title: string = 'نص القضية';
  protected content: SafeHtml = '';
  predictKeyWords: string[] = [];
  nerList: string[] = [];
  isSearchResultVisible: boolean = false;

  // Added properties for file upload result UI
  fileName: string = '';
  mainCategory: string = 'عقد';
  subCategory: string = 'عقد إيجار';

  // Add property to track panel state
  showSearchPanel: boolean = false;
  searchResultData: any;

  // Add properties for selected law/case details
  selectedLawDetails: any = null;
  selectedCaseDetails: any = null;
  showDetailsPanel: boolean = false;

  activeTab: string = 'summary'; // Default active tab

  @ViewChild('scrollContainer') private scrollContainer!: ElementRef;
  @ViewChild('searchResultList')
  private searchResultList!: SearchResultListComponent;
  private shouldScrollToBottom = true;

  showSearchResults() {
    this.isSearchResultVisible = true;
    this.showSearchPanel = true; // Show the panel when search results are available
  }

  hideSearchResults() {
    this.isSearchResultVisible = false;
  }

  ngOnInit(): void {
    // Get stored case text
    this.caseText = localStorage.getItem('file_caseText') || '';

    // Get classification data
    this.mainCategory = localStorage.getItem('file_category') || 'عقد';
    this.subCategory = localStorage.getItem('file_subCategory') || 'عقد إيجار';

    // Set content
    this.content = this.sanitizeContent(this.caseText);

    // Get file name if available
    const fileInfo = localStorage.getItem('file');
    if (fileInfo) {
      try {
        const parsedInfo = JSON.parse(fileInfo);
        this.fileName = parsedInfo.fileName || 'document';
      } catch (e) {
        this.fileName = 'document';
      }
    }

    // Load keywords and entities if available
    this.loadKeywordsAndEntities();
  }

  // Method to set search type and perform search
  setSearchType(type: SearchTypeEnum) {
    this.searchType = type;
    this.performSearch();
  }

  // Implement search functionality similar to extract-search-section
  performSearch() {
    if (!this.caseText) {
      return;
    }

    this.semanticService
      .searchByFile({
        text: this.caseText,
        searchSource: this.searchType,
      })
      .subscribe({
        next: (response) => {
          if (response.isSuccess) {
            this.searchResultData = response.data;
            // Store the search response in localStorage
            localStorage.setItem(
              'search-response',
              JSON.stringify(response.data)
            );
            localStorage.setItem('searchType', this.searchType.toString());

            console.log('Search results:', response.data);

            // Show search results
            this.showSearchResults();

            // Refresh the search result list component if it exists
            setTimeout(() => {
              if (this.searchResultList) {
                this.searchResultList.refreshData();
              }
            }, 100);
          } else {
            console.error('Search failed:', response.error);
          }
        },
        error: (error) => {
          console.error('An error occurred:', error);
        },
      });
  }

  sanitizeContent(htmlContent: string): SafeHtml {
    if (!htmlContent) return this.sanitizer.bypassSecurityTrustHtml('');
    return this.sanitizer.bypassSecurityTrustHtml(htmlContent);
  }

  updateContent(title: string, content: string) {
    this.title = title;
    this.content = this.sanitizeContent(content);
    this.shouldScrollToBottom = true;
  }

  reloadFile() {
    localStorage.removeItem('file');
    this.router.navigate(['../']);
  }

  // Toggle panel visibility
  toggleSearchPanel(): void {
    this.showSearchPanel = !this.showSearchPanel;
  }

  // Load keywords and entities from localStorage
  private loadKeywordsAndEntities() {
    const keywordsData = localStorage.getItem('file_keywords');
    if (keywordsData) {
      try {
        const parsed = JSON.parse(keywordsData);
        this.predictKeyWords = parsed.keywords || [];
      } catch (e) {
        this.predictKeyWords = [];
      }
    }

    const entitiesData = localStorage.getItem('file_entities');
    if (entitiesData) {
      try {
        const parsed = JSON.parse(entitiesData);
        this.nerList = parsed.tags || [];
      } catch (e) {
        this.nerList = [];
      }
    }
  }

  // Method to handle selected law
  onLawSelected(lawDetails: any): void {
    this.selectedLawDetails = lawDetails;
    this.selectedCaseDetails = null;
  }

  // Method to handle selected case
  onCaseSelected(caseDetails: any): void {
    this.selectedCaseDetails = caseDetails;
    this.selectedLawDetails = null;

    // When a case is selected, ensure the chat is visible
    setTimeout(() => {
      // The chat-pot component will automatically show when selectedCase changes
    }, 100);
  }

  // Method to close details panel
  closeDetailsPanel() {
    this.showDetailsPanel = false;
    this.selectedLawDetails = null;
    this.selectedCaseDetails = null;
  }

  ngAfterViewChecked() {
    if (this.shouldScrollToBottom) {
      this.scrollToBottom();
    }
  }

  // Method to scroll to the bottom of the container
  scrollToBottom(): void {
    try {
      if (this.scrollContainer && this.scrollContainer.nativeElement) {
        this.scrollContainer.nativeElement.scrollTop =
          this.scrollContainer.nativeElement.scrollHeight;
      }
    } catch (err) {
      console.error('Error scrolling to bottom:', err);
    }
  }
}
