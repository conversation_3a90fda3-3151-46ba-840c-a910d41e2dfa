/**
 * MOSAED.Internal.Api
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { UserProfile } from './user-profile';
import { Permission } from './permission';


export interface Role { 
    code: number;
    name?: string | null;
    nameAr?: string | null;
    createdAt: string;
    updatedBy?: number | null;
    updatedByUser: UserProfile;
    updatedAt?: string | null;
    isExternal: boolean;
    isActive: boolean;
    permissions?: Array<Permission> | null;
    userProfiles?: Array<UserProfile> | null;
}

