import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-lang-switch',
  templateUrl: './lang-switch.component.html',
  styleUrls: ['./lang-switch.component.scss'],
})
export class LangSwitchComponent implements OnInit {
  public get lang() {
    return getCurrentLang();
  }
  public dir = 'ltr';

  // End Lang direction switch
  constructor() { }

  ngOnInit(): void { }

  protected setLanguage(value: string) {
    localStorage.setItem('current-lang', value);
    setHtmlLang();
    setHtmlDir();
  }
}

function getCurrentLang() {
  const localStorageLang = localStorage.getItem('current-lang');
  if (localStorageLang) {
    return localStorageLang;
  }

  const htmlLang = document.getElementsByTagName('html')[0].getAttribute('lang');
  if (htmlLang) {
    return htmlLang;
  }

  return 'ar';
}

export function setHtmlLang() {
  const html = document.getElementsByTagName('html')[0];
  html.setAttribute('lang', getCurrentLang());
}

export function setHtmlDir() {
  const html = document.getElementsByTagName('html')[0];
  if (getCurrentLang() == 'ar')
    html.setAttribute('dir', 'rtl');
  else
    html.setAttribute('dir', 'ltr');
}
