import { inject, Injectable, Injector } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, CanActivateChild, CanActivateFn, RouterStateSnapshot, UrlTree } from '@angular/router';
import { Observable } from 'rxjs';
import { BaseAuthGuard } from './base-auth.guard';

@Injectable({
  providedIn: 'root'
})
export class AuthenticatedGuard extends BaseAuthGuard implements CanActivate, CanActivateChild {

  constructor(injector: Injector) {
    super(injector);
  }

  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot): boolean | UrlTree | Observable<boolean | UrlTree> | Promise<boolean | UrlTree> {
    return this.validate();
  }

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    return this.validate()
  }

  protected validate(): boolean | UrlTree {
    if (this.isAuthenticated)
      return true;

    const isActive = localStorage.getItem('isActive');
    if (isActive === 'false') {
      return this.router.createUrlTree(['/deactivated-account']);
    }

    return this.router.createUrlTree(['/']);
    // return this.router.createUrlTree(['/unauthorized']);
  }
}

export const canActivateAuthGuard: CanActivateFn = (route, state) => {
  return inject(AuthenticatedGuard).canActivate(route, state);
};
