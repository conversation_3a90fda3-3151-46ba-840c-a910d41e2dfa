// Reset
@font-face {
  font-family: "DIN Next LT Arabic Light";
  src: url("../../fonts/DIN_Next_LT_Arabic_Light.ttf");
}
@font-face {
  font-family: "DIN Next LT Arabic Medium";
  src: url("../../fonts/DIN_Next_LT_Arabic_Regular.ttf");
}
@font-face {
  font-family: "DIN Next LT Arabic Regular";
  src: url("../../fonts/DIN_Next_LT_Arabic_Regular.ttf");
}
html {
  position: relative;
  min-height: 100%;
  font-size: 16px;
  scroll-behavior: smooth;
}

// @import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@400;700&display=swap');
//@import url('https://fonts.googleapis.com/css2?family=Baloo+Bhaijaan+2:wght@400;600&display=swap');
$family: "DIN Next LT Arabic Regular", sans-serif;
$familyBold: "DIN Next LT Arabic Medium", sans-serif;
$familyLight: "DIN Next LT Arabic Light", sans-serif;

:root {
  // --bs-font-sans-serif: 'Ba<PERSON>o Bhaijaan 2', system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-family: $family;
}
body {
  font-family: $family;
  line-height: 1.7 !important;
  background-color: #f5f6fa;
}
h6,
.h6,
h5,
.h5,
h4,
.h4,
h3,
.h3,
h2,
.h2,
h1,
.h1 {
  line-height: 1.6 !important;
}
.btn {
  min-width: 80px;
  // border-radius: $border-radius;
  border-radius: 5px;
}
.btn-primary {
  --bs-btn-color: #ffffff;
  --bs-btn-hover-color: #eee;
}
// a {
//     &:hover {
//         color: $primary;
//     }
// }
a,
input,
button,
.btn {
  -webkit-transition: all 0.3s ease;
  -webkit-transition: all 0.5s ease;
  transition: all 0.5s ease;
}

*::-moz-selection {
  background: #ccc;
  color: #333;
}

*::selection {
  background: #ccc;
  color: #333;
}

*::-moz-selection {
  background: #ccc;
  color: #333;
}

*::-webkit-selection {
  background: #ccc;
  color: #333;
}

a,
a:hover,
a:focus {
  text-decoration: none;
  color: inherit;
}

.clearfix {
  clear: both;
}
p {
  line-height: 1.7;
  font-size: 1.03rem;
}

.bold {
  font-family: $familyBold;
}
.flight {
  font-family: $familyLight;
}
img {
  max-width: 100%;
  border: 0;
}

.required:after {
  content: "*";
  color: red;
  font-size: 1.2rem;
  display: inline;
  line-height: 1;
}

// .btn:not(.dropdown-toggle) {
//     &.btn-primary{
//         color: #fff;
//         &:focus,
//         &:hover{
//             background-color: darken($primary, 10%);
//         }
//     }
//     &.btn-secondary{
//         color: #fff;
//         &:focus,
//         &:hover{
//             background-color: darken($secondary, 10%);
//         }
//     }
//     &.btn-outline-primary{
//         &:focus,
//         &:hover{
//             color: #fff;
//             background-color: $primary;
//         }
//     }
//     &.btn-outline-secondary{
//         &:focus,
//         &:hover{
//             color: #fff;
//             background-color: $secondary;
//         }
//     }
// }
// background blur effect
.blurred {
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
}
.transition {
  transition: all 0.5s ease;
}
.d-flex-between {
  @extend .d-flex;
  @extend .justify-content-between;
  @extend .align-items-center;
}
.d-flex-center {
  @extend .d-flex;
  @extend .justify-content-center;
  @extend .align-items-center;
}
.ltr {
  direction: ltr;
}
.rtl {
  direction: rtl;
}
.flip-ltr {
  transform: scaleX(-1);
}
.wrapper {
  //display: grid;
  //grid-template-rows: auto 1fr auto;
  // min-height: calc( 100vh - var(--headerHeight) );
  //grid-template-columns: minmax(0,1fr) minmax(0,2.5fr);
  //grid-template-columns: 1fr 50pxs;

  min-height: 100vh;
  overflow: hidden;
  // padding-top: var(--headerHeight);
}

.loading-div {
  z-index: 5000;
  opacity: 0.98;
  @extend .align-items-center;
  // @extend .bg-white ;
  @extend .d-flex;
  @extend .end-0;
  @extend .justify-content-center;
  @extend .min-vh-100;
  @extend .min-vw-100;
  @extend .position-fixed;
  @extend .top-0;
  background-color: rgba(#fff, 0.9);
  &:after {
    content: "";
    //background: url("~assets/images/logo.svg") no-repeat center;
    background-size: 100% auto;
    width: 250px;
    height: 126px;
    max-width: 60vw;
    transition: all 0.5s ease-in-out;
    animation-name: bounce-3;
    animation-timing-function: ease;
    animation-duration: 3s;
    animation-iteration-count: infinite;
    // filter: brightness(10);
  }
}
.loading-btn {
  &:after {
    content: "";
    @extend .spinner-border;
    @extend .spinner-border-sm;
    @extend .d-inline-block;
    @extend .ms-2;
  }
}

// .flip-ico{
//     [dir="rtl"] & {
//         @extend .flip-ltr;
//     }
// }

.form-select,
.form-control {
  min-height: $control-height;
  border-radius: $border-radius;
}
.form-select {
  padding: 0.75rem 2.25rem 0.75rem 0.75rem !important;
  padding-inline-end: 2.25rem;
}

.form-control {
  //   border: 1px solid #e4dfd6;
  // padding: 0.375rem 0.75rem 0.575rem 0.75rem;
  &.form-date {
    background: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0NS41IiBoZWlnaHQ9IjUyIj48cGF0aCBkPSJNNDAuNjI1IDYuNUgzNS43NVYxLjIxOUExLjIxOSAxLjIxOSAwIDAwMzQuNTMxIDBoLS44MTJBMS4yMTkgMS4yMTkgMCAwMDMyLjUgMS4yMTlWNi41SDEzVjEuMjE5QTEuMjE5IDEuMjE5IDAgMDAxMS43ODEgMGgtLjgxMkExLjIxOSAxLjIxOSAwIDAwOS43NSAxLjIxOVY2LjVINC44NzVBNC44NzUgNC44NzUgMCAwMDAgMTEuMzc1djM1Ljc1QTQuODc1IDQuODc1IDAgMDA0Ljg3NSA1MmgzNS43NWE0Ljg3NSA0Ljg3NSAwIDAwNC44NzUtNC44NzV2LTM1Ljc1QTQuODc1IDQuODc1IDAgMDA0MC42MjUgNi41ek00Ljg3NSA5Ljc1aDM1Ljc1YTEuNjI3IDEuNjI3IDAgMDExLjYyNSAxLjYyNXY0Ljg3NWgtMzl2LTQuODc1QTEuNjI3IDEuNjI3IDAgMDE0Ljg3NSA5Ljc1em0zNS43NSAzOUg0Ljg3NWExLjYyNyAxLjYyNyAwIDAxLTEuNjI1LTEuNjI1VjE5LjVoMzl2MjcuNjI1YTEuNjI3IDEuNjI3IDAgMDEtMS42MjUgMS42MjV6IiBmaWxsPSIjNzc3Ii8+PC9zdmc+")
      97% center no-repeat #fff;
    background-size: 18px;
    padding-right: 2.5rem;
    [dir="rtl"] & {
      background-position: 10px center;
      padding-right: 0.5rem;
      padding-left: 2.5rem;
    }
  }
  &.form-upload {
    text-indent: -6.6em;
    overflow: hidden;
    background: url("data:image/svg+xml;base64,PHN2ZyBhcmlhLWhpZGRlbj0idHJ1ZSIgZGF0YS1wcmVmaXg9ImZhbCIgZGF0YS1pY29uPSJ1cGxvYWQiIGNsYXNzPSJzdmctaW5saW5lLS1mYSBmYS11cGxvYWQgZmEtdy0xNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2aWV3Qm94PSIwIDAgNTEyIDUxMiI+PHBhdGggZmlsbD0iI0NFRDREQSIgZD0iTTQ1MiA0MzJjMCAxMS05IDIwLTIwIDIwcy0yMC05LTIwLTIwIDktMjAgMjAtMjAgMjAgOSAyMCAyMHptLTg0LTIwYy0xMSAwLTIwIDktMjAgMjBzOSAyMCAyMCAyMCAyMC05IDIwLTIwLTktMjAtMjAtMjB6bTE0NC00OHYxMDRjMCAyNC4zLTE5LjcgNDQtNDQgNDRINDRjLTI0LjMgMC00NC0xOS43LTQ0LTQ0VjM2NGMwLTI0LjMgMTkuNy00NCA0NC00NGgxMjR2LTk5LjNoLTUyLjdjLTM1LjYgMC01My40LTQzLjEtMjguMy02OC4zTDIyNy43IDExLjdjMTUuNi0xNS42IDQwLjktMTUuNiA1Ni42IDBMNDI1IDE1Mi40YzI1LjIgMjUuMiA3LjMgNjguMy0yOC4zIDY4LjNIMzQ0VjMyMGgxMjRjMjQuMyAwIDQ0IDE5LjcgNDQgNDR6TTIwMCAxODguN1YzNzZjMCA0LjQgMy42IDggOCA4aDk2YzQuNCAwIDgtMy42IDgtOFYxODguN2g4NC43YzcuMSAwIDEwLjctOC42IDUuNy0xMy43TDI2MS43IDM0LjNjLTMuMS0zLjEtOC4yLTMuMS0xMS4zIDBMMTA5LjcgMTc1Yy01IDUtMS41IDEzLjcgNS43IDEzLjdIMjAwek00ODAgMzY0YzAtNi42LTUuNC0xMi0xMi0xMkgzNDR2MjRjMCAyMi4xLTE3LjkgNDAtNDAgNDBoLTk2Yy0yMi4xIDAtNDAtMTcuOS00MC00MHYtMjRINDRjLTYuNiAwLTEyIDUuNC0xMiAxMnYxMDRjMCA2LjYgNS40IDEyIDEyIDEyaDQyNGM2LjYgMCAxMi01LjQgMTItMTJWMzY0eiIvPjwvc3ZnPg==")
      97% center no-repeat #fff;
    background-size: 18px;
    padding-right: 2.5rem;
    [dir="rtl"] & {
      background-position: 10px center;
      padding-right: 0.5rem;
      padding-left: 2.5rem;
    }
  }
  &::-moz-placeholder {
    color: #ccc;
  }
  &::-webkit-placeholder {
    color: #ccc;
  }
  &::placeholder {
    color: #ccc;
  }
}

.hover-shadow {
  transition: all 0.5s ease-in-out;
  &:hover,
  &:focus {
    @extend .shadow;
  }
}
.hover-x-10 {
  transition: all 0.5s ease-in-out;
  &:hover,
  &:focus {
    transform: translateX(-10px);
  }
  [dir="ltr"] & {
    &:hover,
    &:focus {
      transform: translateX(10px);
    }
  }
}
.hover-y-10 {
  transition: all 0.5s ease-in-out;
  &:hover,
  &:focus {
    transform: translateY(-10px);
  }
}

// .form-number{
//     appearance: none;
//     -webkit-appearance: none;
// }
// .form-check-input[type=radio] {
//     margin-top: 7px;
// }
// .alert{
//     padding: .7rem 1rem .8rem;
//     border: 0;
// }
.revert {
  transform: none;
  [dir="ltr"] & {
    transform: rotate(180deg);
  }
}

// .btn-arrow{
//     &:after {
//         width: 1rem;
//         height: 1rem;
//         content: "";
//         background-image: url(data:image/svg+xml;base64,PHN2ZyBhcmlhLWhpZGRlbj0idHJ1ZSIgZGF0YS1wcmVmaXg9ImZhbCIgZGF0YS1pY29uPSJhbmdsZS1kb3duIiBjbGFzcz0ic3ZnLWlubGluZS0tZmEgZmEtYW5nbGUtZG93biBmYS13LTgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgdmlld0JveD0iMCAwIDI1NiA1MTIiPjxwYXRoIGZpbGw9IiM3NzciIGQ9Ik0xMTkuNSAzMjYuOUwzLjUgMjA5LjFjLTQuNy00LjctNC43LTEyLjMgMC0xN2w3LjEtNy4xYzQuNy00LjcgMTIuMy00LjcgMTcgMEwxMjggMjg3LjNsMTAwLjQtMTAyLjJjNC43LTQuNyAxMi4zLTQuNyAxNyAwbDcuMSA3LjFjNC43IDQuNyA0LjcgMTIuMyAwIDE3TDEzNi41IDMyN2MtNC43IDQuNi0xMi4zIDQuNi0xNy0uMXoiLz48L3N2Zz4=);
//         background-repeat: no-repeat;
//         background-size: cover;
//         background-position: center;
//         transition: transform 0.2s ease-in-out;
//         display: inline-block;
//         // margin: 0 2px -7px 0;
//         border: 0;
//         filter: brightness(250%);
//         margin: 0px 10px 0;
//         transform:rotate(90deg);
//         [dir="ltr"] & {
//             transform:rotate(-90deg);
//         }
//     }
//     &.text-secondary{
//         &:after {
//             filter: none;
//             width: .7rem;
//             height: .7rem;
//             // transform: scale(0.7) rotate(90deg);
//             margin: 0 10px;
//             // [dir="ltr"] & {
//             //     transform:  scale(0.7) rotate(-90deg);
//             // }
//         }
//     }
//     &.btn-outline-light{
//         &:after {
//             // filter: none;
//             // transform: scale(0.7) rotate(90deg);
//             width: .7rem;
//             height: .7rem;
//             margin: 0px 11px -1px 14px;
//             // [dir="ltr"] & {
//             //     transform:  scale(0.7) rotate(-90deg);
//             // }
//         }
//         &:hover,
//         &:focus{
//             background: none;
//         }
//     }
//     &:hover,
//     &:focus{
//         &:after{
//             transform: rotate(90deg) translateY(5px);
//             [dir="ltr"] & {
//                 transform:rotate(-90deg) translateY(5px);
//             }
//         }
//     }
// }

.scrollbar-style {
  &::-webkit-scrollbar {
    width: 5px;
  }

  /* Track */
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: $primary;
  }

  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
}
