@if (data) {

<p class="text-primary mb-4 fs-4 fw-bold">
  المصادر
</p>
<div class="form-check my-3">
  <input class="form-check-input" type="radio" name="exampleRadios" id="exampleRadios2" [(ngModel)]="searchType"
    [value]="SearchTypeEnum.byLaw">
  <label class="form-check-label" for="exampleRadios2">
    بحث ف الأنظمة
  </label>
</div>
<div class="form-check my-3">
  <input class="form-check-input" type="radio" name="exampleRadios" id="exampleRadios3" [(ngModel)]="searchType"
    [value]="SearchTypeEnum.byIssue">
  <label class="form-check-label" for="exampleRadios3">
    بحث ف القضايا
  </label>
</div>


<div class="row row-cols-md-12">
  <div class="col py-4">
    <p class="text-primary mb-4 fs-4 fw-bold">
      التصنيفات
    </p>
  </div>
  <div class="col py-4">
    <button (click)="openClassificationFeedbackModel()" type="button"
      class="btn btn-outline-primary fw-bold fs-6 d-flex align-items-center px-4 pb-2">
      <span class="ps-1">إبداء الرأي</span>
    </button>
  </div>
</div>


<div class="row row-cols-md-4">
  <div class="col">
    <p class="fw-bold">
      التصنيف الرئيسي:
    </p>
    {{ data.classificationMainCategory }}
  </div>

  <div class="col">
    <p class="fw-bold">
      التصنيف الفرعي:
    </p>
    {{ data.classificationSubCategory }}
  </div>
</div>
<!-- <div class="row row-cols-md-12">
  <div class="col py-4">
    <button (click)="openClassificationFeedbackModel()" type="button"
      class="btn btn-outline-primary fw-bold fs-6 d-flex align-items-center px-4 pb-2">
      <span class="ps-1">إبداء الرأي</span>
    </button>
  </div>

</div> -->


<div class="d-flex justify-content-end align-items-center">

  <div>
    <button (click)="search()" type="button" class="btn btn-primary fw-bold fs-6 d-flex align-items-center px-4 pb-2">
      <svg-icon src="assets/svg/search-light.svg"></svg-icon>
      <span class="ps-1"> بحث </span>
    </button>
  </div>


</div>


}


<p-dialog [(visible)]="showClassificationFeedbackDialog" [modal]="true" [header]="'إبداء الرأي'"
  [style]="{ width: '500px' }">

  @if (!modifyClassification) {

  <p class="mb-4 ">
    هل التصنيف صحيح؟
  </p>

  <div class="row row-cols-md-12">
    <div class="col d-flex justify-content-center">
      <button (click)="onSubmitClassificationFeedback(null)" class="btn btn-primary mx-2 me-3">
        تأكيد التصنيف
      </button>

      <button (click)="modifyClassification=true" class="btn btn-outline-primary mx-2">
        تعديل التصنيف
      </button>
    </div>
  </div>

  } @else {
  <p class="mb-4 ">
    اختار التصنيف الذي تراه مناسبًا
  </p>

  <form class="row needs-validation" novalidate autocomplete="off" #form="ngForm"
    (ngSubmit)="onSubmitClassificationFeedback(form)" [formGroup]="formGroup">

    <div class="col-12 mb-3">
      <label for="mainClassification" class="form-label">التصنيف الرئيسي</label>
      <div class="cutom-primeng">
        <p-dropdown emptyMessage="لم يتم العثور على العنصر" name="mainClassificationList" formControlName="mainClassification" optionValue="id"
          [options]="mainClassificationList" (onChange)="filterSubClassification()" optionLabel="nameAr"
          placeholder="التصنيف الرئيسي" />
      </div>
      <div class="text-danger" *ngIf="formGroup.get('mainClassification')?.hasError('required')  && form.submitted">
        التصنيف الرئيسي مطلوب
      </div>
    </div>

    <div class="col-12 mb-4">
      <label for="subClassification" class="form-label">التصنيف الفرعي</label>
      <div class="cutom-primeng">
        <p-dropdown emptyMessage="لم يتم العثور على العنصر" name="subClassificationList" formControlName="subClassification" [options]="subClassificationList" optionValue="id"
          optionLabel="nameAr" placeholder="التصنيف الفرعي" />
      </div>
      <div class="text-danger" *ngIf="formGroup.get('mainClassification')?.hasError('required')  && form.submitted">
        التصنيف الفرعي مطلوب
      </div>
    </div>

    <div class="col-12 mb-2 mt-3">
      <textarea maxlength="500" (keydown.enter)="onSubmitClassificationFeedback(form)" formControlName="feedbackNotes" placeholder="السبب" cols="4" rows="4" id="note" name="note"
        class="form-control" type="email"></textarea>
        <div class="text-danger" *ngIf="formGroup.get('feedbackNotes')?.hasError('required')  && form.submitted">
          سبب التصنيف مطلوب
        </div>
    </div>


    <div class="row row-cols-md-12 mt-4">
      <div class="col d-flex justify-content-center">
        <button [loading]="createClassificationFeedbackInProgress" pButton type="submit"
          class="btn btn-primary mx-2 me-3">
          ارسال
        </button>

        <button (click)="showClassificationFeedbackDialog=false" class="btn btn-outline-primary mx-2">
          الغاء
        </button>
      </div>
    </div>
  </form>
  }





</p-dialog>
