import { Component, ElementRef, Input, OnInit, ViewChild } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { FavoriteTypeEnum, HighlightInput, HighlightItem, HighlightSectionEnum, HighlightService } from "../../services";


@Component({
  standalone: true,
  selector: 'app-highlight-section',
  templateUrl: './highlight-section.component.html',
  styleUrls: ['./highlight-section.component.css'],
  imports: [CommonModule, FormsModule],  // import what this component uses
})
export class HighlightSectionComponent implements OnInit {
  @Input() section!: HighlightSectionEnum;//'Actions' | 'Reasons' | 'Verdict' | 'Regulation';
  // @Input() content!: string;
  @Input() selectedText: string | null = null;
  @Input() highlightHistory: HighlightItem[] = [];
  @Input() refId?: string;
  @Input() caseId?: number;
  @Input() header: string | null = null;
  @Input() rawText: string | null = null;
  @Input() contentStyle: { [key: string]: any } = {};
  @Input() favoriteType: FavoriteTypeEnum | undefined;

  @ViewChild('highlightContainer', { static: false }) containerRef!: ElementRef;
  // highlights: HighlightView[] = [];
  showColorPicker = false;
  colorPickerPosition = { x: 0, y: 0 };
  constructor(private highlightService: HighlightService) { }
  private currentRange: Range | null = null;
  highlightColors = ['#fffa65', '#a3f7bf', '#ffb3ba', '#bae1ff'];

  public setContent(newText: string) {
    this.selectedText = newText;
    // this.applyServerHighlights(); // reapply highlights on new content
  }
  ngOnInit() {
    // if (this.highlightHistory) {
    //   this.applyServerHighlights();
    // }
  }


  ngAfterViewInit() {
    if (this.highlightHistory) {
      this.applyServerHighlights();
    }
  }

  public applyServerHighlights() {

    // const sectionMap: Record<HighlightSectionEnum, string> = {
    //   Actions: 'actions-text',
    //   Reasons: 'reasons-text',
    //   Verdict: 'verdict-text'
    // };

    // 1) Group your HighlightItems by SectionId
    const bySection = this.highlightHistory.reduce(
      (acc: Record<HighlightSectionEnum, HighlightItem[]>, hi: HighlightItem) => {
        (acc[hi.SectionId] ||= []).push(hi);
        return acc;
      },
      {} as Record<HighlightSectionEnum, HighlightItem[]>
    );

    // 2) For each section, reset its text-holder and re-apply all highlights
    const sectionKey = this.section as HighlightSectionEnum;
    const items = this.highlightHistory.filter(h => h.SectionId === sectionKey);
    if (!items.length) {
      this.setContent(this.rawText ?? '');
      return;
    }

    const container = this.containerRef.nativeElement.querySelector(
      `[data-section="${sectionKey}"]`
    ) as HTMLElement | null;
    if (!container) return;

    // find only the text-holder div we wrapped in the template
    // const textHolder = container.querySelector(
    //   `.${sectionMap[sectionKey]}`
    // ) as HTMLElement | null;
    const textHolder = container.querySelector(
      '.actions-text'
    ) as HTMLElement | null;
    if (!textHolder) return;

    // reset it back to raw text
    textHolder.innerHTML = this.rawText ?? '';//this.rawSectionTextMap[sectionKey] || '';

    // 3) apply in reverse order so offsets don’t shift
    items
      .sort((a, b) => b.StartOffset - a.StartOffset)
      .forEach((hi: HighlightItem) => {
        const raw = this.rawText ?? '';//this.rawSectionTextMap[sectionKey] || '';
        const snippet = raw.slice(hi.StartOffset, hi.EndOffset);

        // walk only textHolder’s Text nodes
        const walker = document.createTreeWalker(textHolder, NodeFilter.SHOW_TEXT);
        let charCount = 0;
        let startNode: Text | null = null, endNode: Text | null = null;
        let startInNode = 0, endInNode = 0, node: Text | null;

        while ((node = walker.nextNode() as Text | null)) {
          const len = node.textContent?.length ?? 0;

          if (!startNode && hi.StartOffset >= charCount && hi.StartOffset < charCount + len) {
            startNode = node;
            startInNode = hi.StartOffset - charCount;
          }
          if (!endNode && hi.EndOffset > charCount && hi.EndOffset <= charCount + len) {
            endNode = node;
            endInNode = hi.EndOffset - charCount;
          }
          charCount += len;
          if (startNode && endNode) break;
        }

        if (startNode && endNode) {
          const range = document.createRange();
          range.setStart(startNode, startInNode);
          range.setEnd(endNode, endInNode);

          const span = document.createElement('span');
          span.style.backgroundColor = hi.Color ?? '';
          span.textContent = snippet;
          span.style.borderRadius = '3px';
          span.style.padding = '0 2px';

          range.deleteContents();
          range.insertNode(span);
          range.detach?.();
        } else {
          console.warn('Could not map highlight back into DOM nodes', hi);
        }
      });
  }

  handleTextSelection(event: Event) {
    const mouseEvent = event as MouseEvent;
    const selection = window.getSelection();
    if (!selection || selection.isCollapsed) {
      this.showColorPicker = false;
      return;
    }


    // this.currentSection = this.section;            // remember which section
    this.currentRange = selection.getRangeAt(0).cloneRange();

    const rect = this.currentRange.getBoundingClientRect();
    this.colorPickerPosition = {
      x: rect.left + window.scrollX,
      y: rect.top + window.scrollY - 40
    };
    this.showColorPicker = true;
  }

  applyHighlight(color: string) {
    this.insertHighlight(color, true); // saves
  }

  // applyHighlighsOnCase(selectedText: string) {
  //   let actionsText = this.selectedCaseActions ?? "";
  //   // let resonsText = this.selectedCase?.reasons ?? "";
  //   // let verdictsText = this.selectedCase?.verdict ?? "";

  //   this.selectedCaseActions = actionsText.replace(new RegExp(this.selectedCaseTextToBeHighlight, 'g'), "<mark>$&</mark>");
  //   // this.selectedCaseReasons = resonsText.replace(new RegExp(this.selectedCaseTextToBeHighlight, 'g'), "<mark>$&</mark>");
  //   // this.selectedCaseVerdicts = verdictsText.replace(new RegExp(this.selectedCaseTextToBeHighlight, 'g'), "<mark>$&</mark>");
  // }

  private insertHighlight(color: string, save: boolean = true, source: 'user' | 'dropdown' = 'user') {
    if (!this.currentRange || !this.refId) return;

    const range = this.currentRange;
    const text = range.toString();
    if (!text.trim()) return;

    // Determine which section the text belongs to
    let foundSection: HighlightSectionEnum | null = null;
    let startOffset = -1;
    let endOffset = -1;

    const sections: HighlightSectionEnum[] = ['Actions', 'Reasons', 'Verdict'];
    const rawText = this.rawText ?? '';
    if (!rawText) return;

    const offset = rawText.indexOf(text);
    if (offset !== -1) {
      foundSection = this.section;
      startOffset = offset;
      endOffset = offset + text.length;
    }

    if (!foundSection || startOffset === -1) {
      console.warn('Selected text not found in any section:', { text });
      return;
    }

    // 2) Insert highlight span into DOM
    const span = document.createElement('span');
    span.style.backgroundColor = color;
    span.dataset['source'] = source;
    span.textContent = text;
    span.style.borderRadius = '3px';
    span.style.padding = '0 2px';

    range.deleteContents();
    range.insertNode(span);
    window.getSelection()?.removeAllRanges();
    this.showColorPicker = false;

    // 3) Save if needed
    if (save) {
      const input: HighlightInput = {
        refId: this.refId,
        caseId: this.caseId?.toString(),
        selectedText: text,
        sectionId: foundSection,
        startOffset,
        endOffset,
        color,
        typeId: this.favoriteType ?? FavoriteTypeEnum.Case//FavoriteTypeEnum.Case
      };
      this.saveHighlight(input);
    }
  }

  saveHighlight(input: HighlightInput) {
    this.highlightService.saveHighlights(input).subscribe({
      next: (response) => {
        console.error('Added');
      },
      error: (err) => {
        console.error('Error querying chat:', err);
      }
    });
  }
}
