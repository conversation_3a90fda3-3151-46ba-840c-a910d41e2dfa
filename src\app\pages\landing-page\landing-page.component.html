<nav
  class="custom_navbar navbar fixed-top navbar-expand-md navbar-light rounded rounded-0 wow animate__animated animate__fadeInDown"
  data-bs-theme="dark" id="navbar-example2">
  <div class="container-xxl py-1 py-sm-2">
    <a class="navbar-brand ms-0" href="#"><img src="assets/images/logo.svg" class="logo" alt="logo" /></a>
    <!-- Mobile button -->
    <div class="d-flex justify-content-end align-items-center">
      <button class="navbar-toggler border-0 d-lg-none px-0" type="button" data-bs-toggle="collapse"
        data-bs-target="#collapsibleNavId" aria-controls="collapsibleNavId" aria-expanded="false"
        aria-label="Toggle navigation">
        <!-- <span class="navbar-toggler-icon"></span> -->
        <svg class="hb" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10" stroke="#eee" stroke-width=".6"
          fill="rgba(0,0,0,0)" stroke-linecap="round" style="cursor: pointer">
          <path d="M2,3L5,3L8,3M2,5L8,5M2,7L5,7L8,7">
            <animate dur="0.2s" attributeName="d"
              values="M2,3L5,3L8,3M2,5L8,5M2,7L5,7L8,7;M3,3L5,5L7,3M5,5L5,5M3,7L5,5L7,7" fill="freeze"
              begin="start.begin" />
            <animate dur="0.2s" attributeName="d"
              values="M3,3L5,5L7,3M5,5L5,5M3,7L5,5L7,7;M2,3L5,3L8,3M2,5L8,5M2,7L5,7L8,7" fill="freeze"
              begin="reverse.begin" />
          </path>
          <rect width="10" height="10" stroke="none">
            <animate dur="2s" id="reverse" attributeName="width" begin="click" />
          </rect>
          <rect width="10" height="10" stroke="none">
            <animate dur="0.001s" id="start" attributeName="width" values="10;0" fill="freeze" begin="click" />
            <animate dur="0.001s" attributeName="width" values="0;10" fill="freeze" begin="reverse.begin" />
          </rect>
        </svg>
      </button>
    </div>
    <div class="collapse navbar-collapse mk-landing-nav" id="collapsibleNavId">
      <ul class="navbar-nav ms-auto text-white">
        <li class="nav-item">
          <a class="nav-link active" aria-current="page" href="#scrollspyHeading1">لماذا المساند</a>
        </li>
<!--        <li class="nav-item">-->
<!--          <a class="nav-link" aria-current="page" href="#">الأبحاث في علم</a>-->
<!--        </li>-->
        <li class="nav-item">
          <a class="nav-link" aria-current="page" href="#">تواصل معنا</a>
        </li>
<!--        <li class="nav-item">-->
<!--          <a class="nav-link" aria-current="page" *ngIf="isAuthenticated == true" [routerLink]="['/search/regular']"-->
<!--            routerLinkActive="active">-->
<!--            البحث العادي-->
<!--          </a>-->
<!--        </li>-->
        <li class="nav-item">
          <a class="nav-link" aria-current="page" *ngIf="isAuthenticated == true" [routerLink]="['/extract/info-empty']"
            routerLinkActive="active">
            البحث الدلالي
          </a>
        </li>
      </ul>

      @if(userInformation != null && isAuthenticated){
        <div class="mt-2 mt-lg-0 ms-auto">

          <button class="btn text-white d-flex align-items-center" (click)="op.toggle($event)" role="button">
            <svg-icon [svgStyle]="{height:'2.5rem'}" src="../../assets/svg/user-circle-white.svg"></svg-icon>
            <p class="text-start my-0 mx-2">
              {{userInformation.fullName}} <br />
              <sup>
                {{userInformation.currentUserProfile?.role?.nameAr}}
              </sup>
            </p>
            <i class="pi pi-sort-down"></i>
          </button>
        </div>
        <p-overlayPanel #op>
          <ul class="list-group p-0 list-group-flush">
            <li class="list-group-item border-0" role="button" [routerLink]="['/my-settings']">
              <i class="pi pi-cog pe-1"></i>
              الإعدادات
            </li>

            <li class="list-group-item border-0" role="button" (click)="logout()">
              <i class="pi pi-sign-out pe-1"></i>
              {{ 'auth.logout'|translate}}
            </li>
          </ul>
        </p-overlayPanel>

      }
      @else if(userInformation == null && isAuthenticated) {
        <ul class="navbar-nav mt-2 mt-lg-0 ms-auto">
          <li class="nav-item">
            <a class="nav-link active" aria-current="page" href="javascript:void(0)" (click)="logout()">
              {{ 'auth.logout' | translate }}

              <i class="pi pi-sign-out pe-1"></i></a>
          </li>
        </ul>
      }
      @else {
        <ul class="navbar-nav mt-2 mt-lg-0 ms-auto">
          <li class="nav-item" *ngIf="!isAuthenticated">
            <a class="nav-link active" aria-current="page" href="javascript:void(0)" (click)="login()">
              {{ 'auth.login' | translate }}

              <i class="fa fa-flip-horizontal fa-sign-in pe-1"></i></a>
          </li>
        </ul>
      }

    </div>
  </div>
</nav>
  <!-- place navbar here -->
<header class="position-relative custom_header z-3">
  <div style="height: 140px"></div>
  <div class="hero-section container-xxl z-1 text-center text-white wow animate__animated animate__fadeIn">
    <div class="row">
      <div class="col-12">
        <div id="carouselExampleAutoplaying" class="carousel slide" data-bs-ride="carousel">
          <div class="carousel-indicators">
            <button type="button" data-bs-target="#carouselExampleAutoplaying" data-bs-slide-to="0" class="active"
                    aria-current="true" aria-label="Slide 1"></button>
<!--            <button type="button" data-bs-target="#carouselExampleAutoplaying" data-bs-slide-to="1"-->
<!--                    aria-label="Slide 2"></button>-->
<!--            <button type="button" data-bs-target="#carouselExampleAutoplaying" data-bs-slide-to="2"-->
<!--                    aria-label="Slide 3"></button>-->
          </div>
          <div class="carousel-inner">
            <div class="carousel-item active mb-5">
              <div class="carousel-caption row justify-content-center">
                <h4 class="col-lg-6 lh-3">
                  مرحبا بك في نظام المساعد الذي يعتمد على تقنيات الذكاء الاصطناعي لتسهيل عمليات البحث في السوابق القضائية والأنظمة والتشريعات الحديثة. لتمكين القضاة والباحثين من الحصول على نتائج بحث مصنفة وذلك من خلال تحليل بيانات القضايا والأحكام واستنتاج العلاقات والتشابه فيما بينها.
                </h4>
              </div>
            </div>
<!--            <div class="carousel-item mb-5">-->
<!--              <div class="carousel-caption row justify-content-center">-->
<!--                <h4 class="col-lg-6 lh-3">-->
<!--                  هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة، لقد تم-->
<!--                  توليد هذا النص من مولد النص العربى الثاني-->
<!--                </h4>-->
<!--              </div>-->
<!--            </div>-->
<!--            <div class="carousel-item mb-5">-->
<!--              <div class="carousel-caption row justify-content-center">-->
<!--                <h4 class="col-lg-6 lh-3">-->
<!--                  هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة، لقد تم-->
<!--                  توليد هذا النص من مولد النص العربى الثالث-->
<!--                </h4>-->
<!--              </div>-->
<!--            </div>-->
          </div>

<!--          <button class="carousel-control-prev" type="button" data-bs-target="#carouselExampleAutoplaying" data-bs-slide="prev">-->
<!--            <span class="carousel-control-prev-icon" aria-hidden="true"></span>-->
<!--            <span class="visually-hidden">السابق</span>-->
<!--          </button>-->
<!--          <button class="carousel-control-next" type="button" data-bs-target="#carouselExampleAutoplaying" data-bs-slide="next">-->
<!--            <span class="carousel-control-next-icon" aria-hidden="true"></span>-->
<!--            <span class="visually-hidden">التالي</span>-->
<!--          </button>-->
        </div>

        <!--        <a *ngIf="isAuthenticated == false" href="javascript:void(0)" (click)="login()"-->
<!--          class="btn btn-light rounded-pill px-5 btn-lg mt-4 fw-bold">-->
<!--          {{'auth.login' | translate}}-->
<!--        </a>-->
<!--        <a class="btn btn-link rounded-pill px-5 btn-lg pt-5 text-white d-block go-to-bottom"-->
<!--          href="#scrollspyHeading1"><i class="fa fa-chevron-down"></i></a>-->
      </div>
    </div>
  </div>
</header>
<main class="mt-5 pt-1">
  <div class="container-xxl mt-3 overflow-hidden">
    <div class="row gx-5 align-items-center text-center text-lg-start">
      <div class="col-lg-6 position-relative z-1 mb-3 mb-lg-0">
        <h3 class="mt-3 text-primary fw-bold mb-3 order-last order-lg-first wow animate__animated animate__fadeInLeft">
          تكامل المصادر وتحليل البيانات
        </h3>
        <p class="lead wow animate__animated animate__fadeInLeft">
          تكامل المصادر وتحليل البيانات من خلال تبسيط استرجاع وتحليل النصوص بشكل أوسع واستخدامها في عدة مهام مثل البحث الدلالي في القضايا والسوابق والمبادئ القضائية وكذلك في الأنظمة واللوائح والتعاميم والتشريعات الحديثة.
        </p>
      </div>
      <div class="col-lg-6 order-first order-lg-last text-md-end position-relative z-2" id="scrollspyHeading1">
        <img src="assets/images/Left-bg.svg" class="img-fluid wow animate__animated animate__fadeInLeft" />
      </div>
    </div>
    <div class="row gx-5 align-items-center text-center text-lg-start">
      <div class="col-lg-6 text-md-start position-relative z-2">
        <img src="assets/images/Right-bg.svg" class="img-fluid wow animate__animated animate__fadeInRight z-2" />
      </div>
      <div class="col-lg-6 position-relative z-1 mb-3 mb-lg-0">
        <h3 class="mt-3 text-primary fw-bold mb-3 wow animate__animated animate__fadeInRight">
          الشفافية والوصول إلى المعلومات
        </h3>
        <p class="lead wow animate__animated animate__fadeInRight">
          الشفافية والوصول إلى المعلومات من خلال تسهيل عملية الوصول إلى المعلومات القانونية والإدارية، مما يدعم محور الشفافية ويزيد من فهم المستخدمين للإجراءات والأنظمة.
        </p>
      </div>
    </div>
    <div class="row gx-5 align-items-center text-center text-lg-start">
      <div class="col-lg-6 position-relative z-1 mb-3 mb-lg-0">
        <h3 class="mt-3 text-primary mb-3 order-last fw-bold order-lg-first wow animate__animated animate__fadeInLeft">
          تقديم الدعم المعلوماتي والمساعدة
        </h3>
        <p class="lead wow animate__animated animate__fadeInLeft">
          تقديم الدعم المعلوماتي والمساعدة في اتخاذ القرار من خلال عرض السوابق القضائية والأنظمة والتشريعات والمساهمة في التنبؤ بالأحكام القضائية.
        </p>
      </div>
      <div class="col-lg-6 order-first order-lg-last text-md-end position-relative z-2">
        <img src="assets/images/Left2-bg.svg" class="img-fluid wow animate__animated animate__fadeInLeft z-2" />
      </div>
    </div>
    <div class="row gx-5 align-items-center text-center text-lg-start">
      <div class="col-lg-6 text-md-start position-relative z-2">
        <img src="assets/images/Right2-bg.svg" class="img-fluid wow animate__animated animate__fadeInRight z-2" />
      </div>
      <div class="col-lg-6 position-relative z-1">
        <h3 class="mt-3 text-primary fw-bold mb-3 wow animate__animated animate__fadeInRight">
          زيادة الكفاءة والإنتاجية
        </h3>
        <p class="lead wow animate__animated animate__fadeInRight">
          زيادة الكفاءة والإنتاجية: تقليل الوقت المستغرق في معالجة ملفات الدعوى والطلبات، وخفض نسبة الأحكام المنقوضة مما يعزز من كفاءة العمل ويساهم في زيادة الإنتاجية.
        </p>
      </div>
    </div>
  </div>
  <div class="container-xxl mt-5 mb-0 mb-lg-3 d-none">
    <div class="row pt-3 mb-4 mb-lg-5 pb-5 align-items-center text-center text-lg-start">
      <div class="col-12 wow animate__animated animate__fadeInUp">
        <div class="d-block px-4 freeـtrial">
          <div class="row align-items-center py-4">
            <div class="col-12 col-lg-7">
              <h5 class="m-0 mb-4 mb-lg-0 lead text-primary fw-bold">
                هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة
              </h5>
            </div>
            <div class="col-12 col-lg-5 d-lg-flex flex-lg-row-reverse">
              <a href="#" class="btn btn-primary rounded-pill px-4 btn-lg fw-bold">اشترك الان في التجربة المجانية<i
                  class="fa fa-arrow-left ps-2"></i></a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</main>

<footer>
  <div class="container">
  <div class="d-flex justify-content-between align-items-center mt-4 border-top py-4 flex-column flex-md-row">
    <div>جميع الحقوق محفوظه لصالح ديوان المظالم السعودية</div>
    <div class="d-flex justify-content-between align-items-center">
      <a
        class="mx-2 fw-bold text-muted"
        [routerLink]="['/policies']"
        aria-label="السياسات"
      >السياسات</a
      >
      <div class="mx-2 fw-bold text-muted">الدعم</div>
    </div>
  </div>
</div>
</footer>
<!-- Back to Top Button -->
<div class="back-to-top d-none z-3">
  <a href="#top" class="btn btn-dark">
    <i class="fa fa-arrow-up"></i>
  </a>
</div>
