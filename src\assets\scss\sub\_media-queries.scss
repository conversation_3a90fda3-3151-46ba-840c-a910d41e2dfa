/* Media Queries */

@media (max-width:1700px){ 
 
}
@media (max-width:1430px){  
    // .main-nav .nav-link {
    //     font-size: 1.1rem;
    //     padding: 0.7rem 0.5rem 1rem;
    // }
}
@media (max-width:1330px){  
    .container{
        //max-width: 100%;
    }  
}
@media (max-width:1200px){ 
  
}
@media screen and (min-width:0) and (max-width:992px) { 
    html,body{
        font-size: 14px;
    }  
    .container{
        max-width: 100%;
    } 
    .scroll-down{
        padding: 100px 0 0;
    }    
    .header-left{
        position: absolute;
        left: 1rem;
        right: auto;
        top: 2.2rem;
        top: calc(50% - 15px);
        [dir="ltr"] & {
            left: auto;
            right: 1rem;
        }
        & > a ,
        & > .dropdown > a {
            margin: 0 .7rem;
            // &:last-child{
            //     [dir="rtl"] & {
            //         margin-left: 0;
            //     }
            //     [dir="ltr"] & {
            //         margin-right: 0;
            //     }
            // }
            // svg{
            //     width: 20px;
            //     height: auto;
            //     color: $primary;
            // }
        }
    } 
   
    .scroll-down{
        display: block;
    } 
}
@media screen and (min-width:0) and (max-width:767px) {    
   
    .tabview-faq .p-tabview-nav {
        display: grid;
        row-gap: 10px;
        column-gap: 10px;
        grid-template-columns: 1fr 1fr;
        li .p-tabview-nav-link{
            font-size: 1rem;
            padding: 1rem;
            // margin: 10px 0 !important;
            // margin-left: 0 !important;
            // margin-right: 0 !important;
        }
    }
    [dir=rtl] .tabview-faq .p-tabview-nav li .p-tabview-nav-link,
    [dir=ltr] .tabview-faq .p-tabview-nav li .p-tabview-nav-link{
        margin:  0 !important;
    }
}
@media screen and (min-width:0) and (max-width:400px) {
   
}


@media print{
    header,footer{
        display: none;
    }
}

// @media (prefers-color-scheme: dark){
//     @import 'dark';
// }    