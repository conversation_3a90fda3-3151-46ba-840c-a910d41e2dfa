import {
  Component,
  EventEmitter,
  HostListener,
  inject,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { TableLazyLoadEvent } from 'primeng/table';
import { SearchTypeEnum } from '../../../../shared/models/search-type.enum';
import { SearchEvents } from '../../../../shared/events/search.events';
import {
  CaseDocumentCommandResultPortalResponse,
  CreateCaseFeedbackInput,
  FavoriteService,
  FavoriteTypeEnum,
  FeedbackService,
  LawDocumentCommandResultPortalResponse,
  SearchByQuestionCommandResult,
  SemanticService,
} from '../../../../services';
import { Router } from '@angular/router';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { DecimalPipe } from '@angular/common';
import { ESearchingType } from '../../../../services/enums/searching-type.enum';
import { finalize } from 'rxjs';
import { BaseComponent } from '../../../../shared/base-component';
import { BaseTranslationKeys } from '../../../../shared/models/base-translation-keys.model';

@Component({
  selector: 'app-search-question-result-list',
  templateUrl: './search-question-result-list.component.html',
  styleUrl: './search-question-result-list.component.scss',
})
export class SearchQuestionResultListComponent
  extends BaseComponent
  implements OnInit
{
  //private readonly SearchService = inject(SearchService);
  rowsPerPage = 3;
  currentPage: number = 0;

  @Output() goBackToSearch = new EventEmitter<void>();
  @Output() caseSelected = new EventEmitter<any>();
  @Output() lawSelected = new EventEmitter<any>();
  @Input() searchResultData: any;

  isCaseDetailsVisible: boolean = false;
  isLawDetailsVisible: boolean = false;
  selectedResult: any = null;

  protected title: string = 'نص القضية';
  protected content: string = '';
  protected isSearchTypeByLaw!: boolean;

  totalRecords!: number;
  loading: boolean = false;

  private text!: string;
  private mainCategory!: string;
  private subCategory!: string;
  private searchType!: SearchTypeEnum;

  protected searchQuestion!: string;
  answer!: string;
  selectedCaseId: number | null = null;
  selectedLawId: number | null = null;

  protected readonly _feedbackService = inject(FeedbackService);
  showCaseFeedbackDialog = false;
  modifyCaseFeedback = false;
  submitCaseFeedbackInProgress = false;
  formGroup!: FormGroup;
  selectedCasePercentage!: number | any;
  _decimalPipeService = inject(DecimalPipe);
  selectedCaseForFeedback: any;
  createCaseFeedbackInput!: CreateCaseFeedbackInput;

  constructor(
    private semantic: SemanticService,
    private favorite: FavoriteService,
    private router: Router
  ) {
    super(
      new BaseTranslationKeys('semantic', 'SearchQuestionResultListComponent')
    );
  }

  ngOnInit() {
    this.initCaseFeedbackForm();
    // if (!history.state) {
    //   history.back();
    // }

    //
    //this.searchQuestion = history.state.searchQuestion;
    this.fetchData();
  }

  fetchData() {
    const response = JSON.parse(
      localStorage.getItem('search-response') ?? '""'
    ) as SearchByQuestionCommandResult;

    this.isSearchTypeByLaw = response?.lawResult != null;
    if (this.isSearchTypeByLaw) {
      const result = response?.lawResult?.data ?? [];
      this.searchResult.data = result as any;
      this.searchResult.total = response.lawResult.totalRecords;
      this.answer = response.lawResult.answer!;
    } else {
      const result = response?.caseResult?.caseData ?? [];
      this.searchResult.data = result as any;
      this.searchResult.total = response.caseResult.totalRecords;
      this.answer = response.caseResult.answer!;
    }
    this.toggleOrientation();
  }

  viewCaseDetails(id: number) {
    const index = this.searchResult.data.findIndex(
      (item: any) => item.id === id
    );

    if (index !== -1) {
      this.currentPage = Math.floor(index / this.rowsPerPage);
    }

    this.selectedCaseId = id;
    this.selectedLawId = null;

    this.loading = true;
    this.isCaseDetailsVisible = false;
    this.isLawDetailsVisible = false;
    this.selectedResult = null;

    this.semantic.getCaseDocument(id).subscribe({
      next: (response: CaseDocumentCommandResultPortalResponse) => {
        this.loading = false;
        if (!response) return;

        this.selectedResult = response.data || null;
        this.caseSelected.emit(this.selectedResult);
        this.isCaseDetailsVisible = true;
      },
      error: (err) => {
        this.loading = false;
        console.error('Error retrieving case details:', err);
      },
    });
  }

  addToFavorite(selectedResult: any) {
    this.favorite
      .storeFavorite({
        refId: this.isSearchTypeByLaw ? selectedResult.id : selectedResult.id,
        typeId: this.isSearchTypeByLaw
          ? FavoriteTypeEnum.Regulation
          : FavoriteTypeEnum.Case,
        summary: this.isSearchTypeByLaw
          ? selectedResult.shortSystemBrief
          : selectedResult.shortCleanText,
      })
      .subscribe({
        next: () => {
          this.loading = false;
          const matchedItem = this.searchResult.data.find(
            (item: any) => item.id === selectedResult.id
          ) as any;
          if (matchedItem) {
            matchedItem.isFavorite = true;
          }
          // this.selectedResult = response.data || null;
        },
        error: (err) => {
          this.loading = false;
          console.error('Error retrieving case details:', err);
        },
      });
  }

  removeFavorite(selectedResult: any) {
    this.favorite
      .removeFavorites({
        refId: selectedResult.id,
        typeId: this.isSearchTypeByLaw
          ? FavoriteTypeEnum.Regulation
          : FavoriteTypeEnum.Case,
      })
      .subscribe({
        next: () => {
          this.loading = false;
          const matchedItem = this.searchResult.data.find(
            (item: any) => item.id === selectedResult.id
          ) as any;
          if (matchedItem) {
            matchedItem.isFavorite = false;
          }
        },
        error: (err) => {
          this.loading = false;
          console.error('Error retrieving case details:', err);
        },
      });
  }

  viewLawDetails(lawId: number) {
    const index = this.searchResult.data.findIndex(
      (item: any) => item.id === lawId
    );

    if (index !== -1) {
      this.currentPage = Math.floor(index / this.rowsPerPage);
    }
    this.selectedLawId = lawId;
    this.selectedCaseId = null;

    this.loading = true;
    this.isCaseDetailsVisible = false;
    this.isLawDetailsVisible = false;
    this.selectedResult = null;

    this.semantic.getLawDocument(lawId).subscribe({
      next: (response: LawDocumentCommandResultPortalResponse) => {
        this.loading = false;
        if (!response) return;

        this.selectedResult = response.data || null;
        this.lawSelected.emit(this.selectedResult);
        this.isLawDetailsVisible = true;
      },
      error: (err) => {
        this.loading = false;
        console.error('Error retrieving law details:', err);
      },
    });
  }

  goBackToTextSearch() {
    localStorage.setItem('isReturning', 'true');
    this.router.navigate(['/search/semantic/text']);
  }

  goBack() {
    this.isCaseDetailsVisible = false;
    this.isLawDetailsVisible = false;
  }

  onPageChange(event: TableLazyLoadEvent) {
    this.currentPage = Math.floor(event.first! / this.rowsPerPage);
  }

  updateContent(title: string, content: string) {
    this.title = title;
    this.content = content;
  }
  protected searchResult = {
    data: [],
    limit: 10,
    offset: 0,
    total: 0,
  };

  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.toggleOrientation();
  }

  orientation: 'horizontal' | 'vertical' = 'horizontal';
  toggleOrientation() {
    if (window.innerWidth < 992) {
      this.orientation = 'vertical';
    } else {
      this.orientation = 'horizontal';
    }
  }

  initCaseFeedbackForm() {
    this.formGroup = new FormGroup({
      feedbackPercentage: new FormControl(
        1,
        Validators.compose([Validators.required])
      ),
      feedbackNotes: new FormControl(
        '',
        Validators.compose([Validators.required])
      ),
    });
  }

  openCaseFeedbackDialog(_case: any) {
    this.showCaseFeedbackDialog = true;
    this.modifyCaseFeedback = false;
    this.selectedCaseForFeedback = _case;
    this.selectedCasePercentage = this._decimalPipeService.transform(
      _case.embeddingScore * 100,
      '1.2-2'
    );
    this.initCaseFeedbackForm();
  }

  onSubmitCaseFeedback(form: any) {
    let searchingType = ESearchingType.ByQueryTextInCase;
    let searchedQuery = JSON.parse(
      localStorage.getItem('file_caseText') || '""'
    );
    this.createCaseFeedbackInput = {
      searchedQuery: searchedQuery,
      searchingType: searchingType,
      caseId: this.selectedCaseForFeedback.caseId,
      auditVerdictNumber: this.selectedCaseForFeedback.auditVerdictNumber,
      previousScore: this.selectedCasePercentage,
      feedbackScore: this.selectedCasePercentage,
      feedbackNote: null,
      caseData: JSON.stringify(this.selectedCaseForFeedback),
    };

    if (form) {
      if (form.valid) {
        this.createCaseFeedbackInput.feedbackScore =
          this.formGroup.get('feedbackPercentage')?.value + '';
        this.createCaseFeedbackInput.feedbackNote =
          this.formGroup.get('feedbackNotes')?.value;
      } else {
        return;
      }
    }

    this.submitCaseFeedbackInProgress = true;
    this._feedbackService
      .submiCaseFeedback(this.createCaseFeedbackInput)
      .pipe(
        finalize(() => {
          this.submitCaseFeedbackInProgress = false;
        })
      )
      .subscribe({
        next: (res) => {
          this.showCaseFeedbackDialog = false;
          this._messageService.add({
            severity: 'success',
            summary: 'نجاح',
            detail: 'تم تأكيد نسبة التشابه بنجاح',
          });
        },
        error: (err) => {},
      });
  }
}
