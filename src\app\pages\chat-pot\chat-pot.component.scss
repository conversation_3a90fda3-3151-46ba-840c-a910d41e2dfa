/* Add styles for embedded mode */
.embedded-chat-container {
  height: calc(100% - 60px) !important;
  overflow-y: auto;
}

.chat-input-container {
  background-color: white;
  border-top: 1px solid #eee;
}

/* Make sure the overlay panel is properly sized when embedded */
:host-context(.full-width-chat) {
  .p-overlaypanel {
    width: 100% !important;
    max-width: 100% !important;
    height: 100% !important;
  }
}

/* Styles for the trigger input container */
.chat-trigger-container {
  margin-bottom: 1rem;

  .form-control {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 14px;

    &:focus {
      border-color: #488371;
      box-shadow: 0 0 0 0.2rem rgba(72, 131, 113, 0.25);
    }
  }

  .send-button {
    background-color: #488371;
    border-color: #488371;
    border-radius: 8px;
    padding: 12px 20px;
    font-weight: 500;

    &:hover {
      background-color: #3a6b5a;
      border-color: #3a6b5a;
    }
  }
}
