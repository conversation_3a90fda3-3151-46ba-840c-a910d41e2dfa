/* Add styles for embedded mode */
.embedded-chat-container {
  height: calc(100% - 60px) !important;
  overflow-y: auto;
}

.chat-input-container {
  background-color: white;
  border-top: 1px solid #eee;
}

/* Make sure the overlay panel is properly sized when embedded */
:host-context(.full-width-chat) {
  .p-overlaypanel {
    width: 100% !important;
    max-width: 100% !important;
    height: 100% !important;
  }
}

/* Add a method to handle input focus */
