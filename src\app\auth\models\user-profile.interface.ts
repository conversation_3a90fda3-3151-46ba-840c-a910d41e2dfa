import { ILookupDetailsResult } from "../../shared/public.api";


export interface UserProfile {
    fullName: string;
    userCode: string;
    permissions: Array<number>;
    currentUserProfile?: UserProfileDetails;
    profiles: Array<UserProfileDetails>;
    isActive: boolean;
}

export interface UserProfileDetails {
    reference: string;
    role: ILookupDetailsResult;
    agency?: ILookupDetailsResult;
}