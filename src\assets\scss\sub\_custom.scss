.text-primary {
  color: $primary !important;
}
.text-secondary {
  color: $secondary !important;
}
.text-dark {
  color: $dark !important;
}
.text-white {
  color: #fff;
}
.text-primary-light {
  color: #629b78 !important;
}
.text-error {
  color: #d9534f !important;
}
.fs-small {
  font-size: 14px;
}
.fs-16 {
  font-size: 16px;
}
.fs-large {
  font-size: 36px;
}
.cursor-pointer {
  cursor: pointer;
}
main {
  z-index: 1;
  position: relative;
}
a {
  color: $primary-light;
}
// .underline {
//   &::after {
//     content: "...";
//     color: #b6d1c1;
//     text-align: center;
//     letter-spacing: 5px;
//     width: 8px;
//     height: 8px;
//     border-radius: 50%;
//     // background: #b6d1c1;
//     display: inline-block;
//     bottom: 22px;
//     position: relative;
//     right: 322px;
//     font-size: 30px;
//     // margin: 0 10px;
//   }
// }
.line-container {
  width: 130%;
  position: relative;
  bottom: 18px;
}
.underline {
  height: 5px;
  width: 100%;
  border-radius: 4px;
  background-color: #b6d1c1;
  opacity: 0.8;
  // position: relative;
  bottom: 17px;
  z-index: -1;
}
.dots {
  bottom: 22px;
  // position: relative;
  right: 122px;
}
.dot1 {
  background-color: #b6d1c1;
  letter-spacing: 5px;
  border-radius: 50%;
  width: 5px;
  height: 5px;
  opacity: 0.7;
}
.dot2 {
  background-color: #b6d1c1;
  letter-spacing: 5px;
  border-radius: 50%;
  width: 5px;
  height: 5px;
  opacity: 0.5;
}
.dot3 {
  background-color: #b6d1c1;
  letter-spacing: 5px;
  border-radius: 50%;
  width: 5px;
  height: 5px;
  opacity: 0.3;
}
.wrapper {
  background-color: #fff;
  // &:before {
  //   content: "";
  //   background-image: url("../../images/svg/square_pattern.svg");
  //   opacity: 0.1;
  //   width: 100%;
  //   height: 100%;
  //   position: absolute;
  // }
  // &:after {
  //   content: "";
  //   background-image: url("../../images/svg/square_pattern.svg");
  //   background-repeat: repeat-y;
  //   top: 0;
  //   left: 2rem;
  //   position: absolute;
  //   width: 6%;
  //   background-size: contain;
  //   height: 100%;
  //   opacity: 0.2;
  // }
}
.menu {
  @media (min-width: 968px) {
    max-width: 21rem;
  }
  box-shadow: -4px 0px 36px 0px rgba(0, 0, 0, 0.04);
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  height: 100%;

  .nav {
    .accordion-item {
      border: none;
    }
    .nav-item,
    .accordion-item {
      &.active {
        .nav-link {
          background-color: $primary-light;
        }
        .nav-link:hover {
          transform: none;
        }
      }
      .nav-link:hover {
        transform: translateX(-10px);
        transition: all 0.3s ease;
        background-color: #f3f7f6;
      }
      .nav-link {
        color: $dark;
        padding-top: 1rem;
        padding-bottom: 1rem;
        border-radius: $border-radius;
      }
    }
  }
}
.footer {
  color: #c2c2c2;
  z-index: 0;
  a {
    &:hover {
      color: #ffff !important;
      text-decoration: underline;
    }
  }
}
.page-layout {
  background-color: #ffffff;
  .page-title {
    color: #333333;
  }
  .breadcrumb-item {
    a {
      color: #797979;
    }
    &.active {
      color: $primary;
    }
  }
}
.user-name {
  color: #252525;
  width: 170px;
}
.user-card {
  background-color: #ffffff;
  box-shadow: 0px 0px 32px #6b6b6b14;
  border-radius: 8px;
  max-width: 19rem;
  .notification {
    align-items: center;
    display: flex;
    position: relative;

    .unread-mark {
      background-color: #d9534f;
      border-radius: 4px;
      position: absolute;
      height: 20px;
      top: 16%;
      color: #fff;
      font-size: 12px;
      align-items: center;
      display: flex;
      justify-content: center;
      right: 1%;
    }
  }

  .user-name {
    color: #252525;
    width: 200px;
  }
  .user-role {
    color: #8e8e8e;
  }
}
.secondAdmin-title {
  border: 1px solid #d0cdc9;
  border-radius: 8px;
}
.verifyIdentityName {
  position: relative;
  .fa-circle-check {
    position: absolute;
    bottom: 12px;
    left: 26px;
    color: #5cb85c;
  }
}
.custom-file-upload {
  display: flex;
  // border: 1px solid #e4dfd6;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  color: $primary;
  font-size: 16px;
  justify-content: end;
  &:hover {
    .files-icon {
      display: block !important;
    }
  }
  &.view {
    justify-content: space-between;
    .files-icon {
      display: none;
      .fa-eye {
        color: #337ab7;
      }
      .fa-trash {
        color: #d43f3a;
      }
    }
  }
}

.menu-custom {
  .p-menu-list {
    border-radius: 8px;
    box-shadow: 0px 0px 32px #6b6b6b14;
    margin-bottom: 0rem;

    .p-menuitem-content {
      background: white !important;
    }
    .p-menuitem {
      .p-menuitem-link:hover {
        background: #e9f7ef;
      }
      .p-menuitem-link {
        border-radius: 8px;
        margin: 6px;
        .p-menuitem-text {
          margin: 0 5px;
          color: #146936;
          font-weight: 700;
          font-size: 14px;
        }
        .p-menuitem-icon {
          margin-right: 0;
          color: #146936;
          font-weight: 700;
          font-size: 14px;
        }
        .p-menu-item-content {
          margin-bottom: 5px;
          border-radius: 8px;
        }
      }
      // &:nth-child(1) {
      //   span {
      //     color: #262321 !important;
      //   }
      // }
      // &:nth-child(2) {
      //   .fa-toggle-large-on {
      //     color: #5cb85c !important;
      //   }
      // }
    }
  }
}
.p-multiselect {
  width: 100%;
}
.p-accordion {
  margin-bottom: 0.75rem;
}
.p-inputtext,
.p-input-icon-left {
  width: 100%;
}
.p-accordion-header-link {
  background-color: $white;
}
.accordion-button {
  background-color: white;
}
.custom-badge span {
  font-size: 10px;
  font-weight: 600;
  min-width: 15px;
  height: 15px;
  line-height: 13px;
  position: relative;
  bottom: 0.6rem;
  left: 1.2rem;
}
.inti-phone {
  position: relative;
  height: 36px;
  p-inputmask {
    position: absolute;
    width: 100%;
    .p-inputmask {
      width: 100%;
      direction: ltr;
      padding-left: 140px;
    }
  }
  p-dropdown {
    position: absolute;
    width: 94px;
    left: 0;
    // .p-dropdown {
    //     border-right: none;
    // }
    .p-overlay {
      width: 296px;
    }
    .p-dropdown-clear-icon {
      display: none;
    }
    .country-item {
      .mx-2 {
        direction: ltr;
        float: right;
      }
    }
  }
  .codeNoVal {
    position: absolute;
    left: 100px;
    top: 10px;
    text-align: left;
    direction: ltr;
  }
}
.lottie-animation {
  width: 13rem;
  height: 13rem;
  margin: 0 auto;
}
.section-title-view {
  position: relative;
  &:after {
    content: "";
    position: absolute;
    border-bottom: 1px solid #f2f0ea;
    width: 100%;
    top: 55%;
    margin-right: 20px;
    margin-left: 20px;
  }
}
[dir="rtl"] {
  .calender-input {
    p-dropdown .p-dropdown {
      border-bottom-left-radius: 0;
      border-top-left-radius: 0;
    }
  }
}
.calender-input {
  position: relative;
  p-dropdown {
    width: 38%;
    .p-dropdown {
      //background-color: #f8f7f6;
    }
    .p-dropdown:not(.p-disabled).p-focus {
      box-shadow: none;
    }
  }
  .ngbDatepicker-group .dropdown-menu {
    width: 148%;
  }
}

.alert-primary {
  background-color: #e9fcf1;
  color: #169f52;
}
.alert-secondary {
  background-color: #e6f5ff;
  color: #0170b5;
}
.alert-dark {
  background-color: #f3f3f2;
  color: #5f5c56;
}
.alert-warning {
  background-color: #fcf4e9;
  color: #e58f1f;
}
.alert-danger {
  background-color: #fceded;
  color: #d04241;
}
.p-tag {
  background: #e8f0eb;
  color: #082c17;
  font-size: 0.75rem;
  font-weight: normal;
  padding: 0.25rem 0.4rem;
  border-radius: 20px;
}
@keyframes fadeInOut {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.angle-left {
  svg {
    [dir="ltr"] & {
      transform: rotate(180deg);
    }
  }
}
.angle-right {
  svg {
    [dir="rtl"] & {
      transform: rotate(180deg);
    }
  }
}
.status-1 {
  background-color: #f8fafa;
  border-radius: 5px;
  color: #666666;
}

.status-2 {
  background-color: #e7fcf2;
  border-radius: 5px;
  color: #357173;
}

body {
  font-family: "DIN Next LT Arabic Light", sans-serif;
}
.main_height {
  height: auto;
  min-height: calc(100vh - 305px);
}
.g-bg {
  background: linear-gradient(-91deg, $primary 50.94%, #64c4ed 106.46%);
}
.go-to-bottom {
  margin-top: 8rem !important;
}
.mobile-toggle {
  right: 2.3rem;
}
.lh-3 {
  line-height: 3rem;
}
// .btn,
// .h1,
// .h2,
// .h3,
// .h4,
// .h5,
// .h6,
// h1,
// h2,
// h3,
// h4,
// h5,
// h6 {
//   font-family: "DIN Next LT Arabic Medium";
// }
.shadow-sm {
  box-shadow: 6px 0 10px 0 rgba(0, 0, 0, 0.02) !important;
}
.custom_navbar {
  //background-color: rgba(61, 114, 180, 0.5);
  //transition: background-color 0.3s;
}
a {
  text-decoration: none;
}
.menu-open {
  width: 16rem !important;
}
.overlay-open {
  opacity: 1 !important;
  display: block !important;
}
.logo {
  width: 120px;
}
@media (max-width: 991.98px) {
  .logo {
    width: 110px;
  }
}
.hb {
  width: 35px;
  margin: 0 auto;
  display: block;
}
.carousel-indicators [data-bs-target] {
  width: 5px;
  height: 5px;
  border-radius: 50%;
}
.freeـtrial {
  border-radius: 20px;
  background: #f4fbff;
  box-shadow: 10px 20px 35px 0 rgba(0, 0, 0, 0.06);
}
header .carousel-caption {
  position: static;
}
.custom_header {
  height: 683px;
}
.custom_header::before {
  content: "";
  position: absolute;
  background: url(../../../assets/images/header-bg.svg) top center no-repeat;
  background-size: 100% 100%;
  width: 100%;
  min-height: 683px;
  position: absolute;
  top: 0;
  right: 0;
  z-index: -1;
}
.custom_header::after {
  content: "";
  position: absolute;
  background: url(../../../assets/images/Polygon-bg.svg) top center no-repeat;
  background-size: 100% 100%;
  width: 100%;
  min-height: 683px;
  position: absolute;
  top: 0;
  right: 0;
  z-index: -1;
}
@media (max-width: 1568px) {
  .custom_header::before {
    background-size: cover;
  }
  .custom_header::after {
    background-size: cover;
  }
}
.Left_bg {
  position: relative;
  height: 440px;
}
.Left_bg::before {
  content: "";
  position: absolute;
  background: url(../../../assets/images/Left-bg.svg) top left no-repeat;
  background-size: 100% 100%;
  width: 100%;
  top: -140px;
  right: 0;
  height: 740px;
  background-size: auto;
}
.Right_bg {
  position: relative;
  height: 440px;
}
.Right_bg::before {
  content: "";
  position: absolute;
  background: url("../../../assets/images/Right-bg.svg") top left no-repeat;
  background-size: 100% 100%;
  width: 100%;
  top: 0;
  left: 0;
  height: 740px;
  background-size: auto;
}
@media (max-width: 991.98px) {
  .Left_bg::before {
    content: "";
    position: absolute;
    background: url(../../../assets/images/Left-bg.svg) top ybv7hy76V SrtDFsxz
      no-repeat;
    background-size: 100% 100%;
    width: 100%;
    top: -140px;
    right: 0;
    height: 740px;
    background-size: auto;
  }
  .Right_bg::before {
    content: "";
    position: absolute;
    background: url("../../../assets/images/Right-bg.svg") top right no-repeat;
    background-size: 100% 100%;
    width: 100%;
    top: 0;
    left: 0;
    height: 740px;
    background-size: auto;
  }
}
.modal {
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}
.modal-content {
  border: none;
}
.footer {
  position: relative;
  height: auto;
}
.footer::before {
  content: "";
  position: absolute;
  background: url("../../../assets/images/Footer-bg.svg") top center no-repeat;
  width: 100%;
  top: 0;
  left: 0;
  height: 180px;
  background-size: cover;
}
.footer .container-xxl {
  top: 70px;
}
@media (max-width: 991.98px) {
  .footer::before {
    height: 60px;
    background-size: 150%;
  }
  .footer .container-xxl {
    top: 0;
  }
}
.btn-light {
  color: $primary;
}
.btn-light:hover {
  color: $primary;
}
.back-to-top {
  position: fixed;
  bottom: 15px;
  right: 15px;
  transition: opacity 0.3s ease-in-out;
}
.back-to-top a {
  padding: 10px 16px !important;
  border-radius: 50%;
  opacity: 0.7;
}
.back-to-top a:hover {
  opacity: 1;
}
[data-bs-theme="dark"] .back-to-top a {
  background: #3d699f;
}
[data-bs-theme="dark"] .freeـtrial {
  background: #212529;
  border: 1px solid #3d699f;
  box-shadow: -10px 20px 35px 0 #1a1d21;
}
.nav-link {
  color: var(--bs-list-group-action-color);
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: $primary;
  font-family: "DIN Next LT Arabic Light", sans-serif;
  background-color: var(--green-100);
}
.closeButton {
  // right: 0;
  top: 10px;
}

.side-nav-transition {
  transition: width 0.3s ease, opacity 0.3s ease;
}
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1021;
  display: none;
  transition: opacity 0.3s ease;
}
.bd-placeholder-img {
  font-size: 1.125rem;
  text-anchor: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
@media (min-width: 768px) {
  .bd-placeholder-img-lg {
    font-size: 3.5rem;
  }
}
.b-example-divider {
  width: 100%;
  height: 3rem;
  background-color: rgba(0, 0, 0, 0.1);
  border: solid rgba(0, 0, 0, 0.15);
  border-width: 1px 0;
  box-shadow: inset 0 0.5em 1.5em rgba(0, 0, 0, 0.1),
    inset 0 0.125em 0.5em rgba(0, 0, 0, 0.15);
}
.b-example-vr {
  flex-shrink: 0;
  width: 1.5rem;
  height: 100vh;
}
.bi {
  vertical-align: -0.125em;
  fill: currentColor;
}
.nav-scroller {
  position: relative;
  z-index: 2;
  height: 2.75rem;
  overflow-y: hidden;
}
.nav-scroller .nav {
  display: flex;
  flex-wrap: nowrap;
  padding-bottom: 1rem;
  margin-top: -1px;
  overflow-x: auto;
  text-align: center;
  white-space: nowrap;
  -webkit-overflow-scrolling: touch;
}
.bd-mode-toggle {
  z-index: 1500;
}
.bd-mode-toggle .dropdown-menu .active .bi {
  display: block !important;
}
.btn-label-primary {
  color: $primary;
  border-color: transparent;
  background: #e7f4fb;
}
.btn-label-primary.waves-effect .waves-ripple {
  background: radial-gradient(
    rgba(115, 103, 240, 0.2) 0,
    rgba(115, 103, 240, 0.3) 40%,
    rgba(115, 103, 240, 0.4) 50%,
    rgba(115, 103, 240, 0.5) 60%,
    rgba(255, 255, 255, 0) 70%
  );
}
.btn-label-primary:hover {
  border-color: transparent !important;
  background: #dbf0fb !important;
  color: $primary !important;
}
.btn-label-primary.focus,
.btn-label-primary:focus {
  color: $primary;
  background: #dbf0fb;
  box-shadow: none !important;
}
.btn-label-primary.active,
.btn-label-primary.show.dropdown-toggle,
.btn-label-primary:active,
.show > .btn-label-primary.dropdown-toggle {
  color: $primary !important;
  background-color: #dbf0fb !important;
  border-color: transparent !important;
}
.btn-label-primary.active:focus,
.btn-label-primary.show.dropdown-toggle:focus,
.btn-label-primary:active:focus,
.show > .btn-label-primary.dropdown-toggle:focus {
  box-shadow: none;
}
.btn-label-primary.disabled,
.btn-label-primary:disabled {
  color: $primary !important;
  border-color: transparent !important;
  background: #e7f4fb !important;
  box-shadow: none;
}
.bg-label-primary {
  background-color: #e7f4fb !important;
  color: $primary !important;
}
.bg-label-hover-primary {
  background-color: #e7f4fb !important;
  color: $primary !important;
}
.bg-label-hover-primary:hover {
  background-color: $primary !important;
  color: #fff !important;
}
.btn-label-secondary {
  color: #a8aaae;
  border-color: transparent;
  background: #f1f1f2;
}
.btn-label-secondary.waves-effect .waves-ripple {
  background: radial-gradient(
    rgba(168, 170, 174, 0.2) 0,
    rgba(168, 170, 174, 0.3) 40%,
    rgba(168, 170, 174, 0.4) 50%,
    rgba(168, 170, 174, 0.5) 60%,
    rgba(255, 255, 255, 0) 70%
  );
}
.btn-label-secondary:hover {
  border-color: transparent !important;
  background: #eaebec !important;
  color: #a8aaae !important;
}
.btn-label-secondary.focus,
.btn-label-secondary:focus {
  color: #a8aaae;
  background: #eaebec;
  box-shadow: none !important;
}
.btn-label-secondary.active,
.btn-label-secondary.show.dropdown-toggle,
.btn-label-secondary:active,
.show > .btn-label-secondary.dropdown-toggle {
  color: #a8aaae !important;
  background-color: #eaebec !important;
  border-color: transparent !important;
}
.btn-label-secondary.active:focus,
.btn-label-secondary.show.dropdown-toggle:focus,
.btn-label-secondary:active:focus,
.show > .btn-label-secondary.dropdown-toggle:focus {
  box-shadow: none;
}
.btn-label-secondary.disabled,
.btn-label-secondary:disabled {
  color: #a8aaae !important;
  border-color: transparent !important;
  background: #f2f2f3 !important;
  box-shadow: none;
}
.btn-label-success {
  color: #28c76f;
  border-color: transparent;
  background: #ddf6e8;
}
.btn-label-success.waves-effect .waves-ripple {
  background: radial-gradient(
    rgba(40, 199, 111, 0.2) 0,
    rgba(40, 199, 111, 0.3) 40%,
    rgba(40, 199, 111, 0.4) 50%,
    rgba(40, 199, 111, 0.5) 60%,
    rgba(255, 255, 255, 0) 70%
  );
}
.btn-label-success:hover {
  border-color: transparent !important;
  background: #cbf2dc !important;
  color: #28c76f !important;
}
.btn-label-success.focus,
.btn-label-success:focus {
  color: #28c76f;
  background: #cbf2dc;
  box-shadow: none !important;
}
.btn-label-success.active,
.btn-label-success.show.dropdown-toggle,
.btn-label-success:active,
.show > .btn-label-success.dropdown-toggle {
  color: #28c76f !important;
  background-color: #cbf2dc !important;
  border-color: transparent !important;
}
.btn-label-success.active:focus,
.btn-label-success.show.dropdown-toggle:focus,
.btn-label-success:active:focus,
.show > .btn-label-success.dropdown-toggle:focus {
  box-shadow: none;
}
.btn-label-success.disabled,
.btn-label-success:disabled {
  color: #28c76f !important;
  border-color: transparent !important;
  background: #dff7e9 !important;
  box-shadow: none;
}
.btn-label-danger {
  color: #ea5455;
  border-color: transparent;
  background: #fce4e4;
}
.btn-label-danger.waves-effect .waves-ripple {
  background: radial-gradient(
    rgba(234, 84, 85, 0.2) 0,
    rgba(234, 84, 85, 0.3) 40%,
    rgba(234, 84, 85, 0.4) 50%,
    rgba(234, 84, 85, 0.5) 60%,
    rgba(255, 255, 255, 0) 70%
  );
}
.btn-label-danger:hover {
  border-color: transparent !important;
  background: #fad6d6 !important;
  color: #ea5455 !important;
}
.btn-label-danger.focus,
.btn-label-danger:focus {
  color: #ea5455;
  background: #fad6d6;
  box-shadow: none !important;
}
.btn-label-danger.active,
.btn-label-danger.show.dropdown-toggle,
.btn-label-danger:active,
.show > .btn-label-danger.dropdown-toggle {
  color: #ea5455 !important;
  background-color: #fad6d6 !important;
  border-color: transparent !important;
}
.btn-label-danger.active:focus,
.btn-label-danger.show.dropdown-toggle:focus,
.btn-label-danger:active:focus,
.show > .btn-label-danger.dropdown-toggle:focus {
  box-shadow: none;
}
.btn-label-danger.disabled,
.btn-label-danger:disabled {
  color: #ea5455 !important;
  border-color: transparent !important;
  background: #fce5e6 !important;
  box-shadow: none;
}
.btn-label-warning {
  color: #ff9f43;
  border-color: transparent;
  background: #fff0e1;
}
.btn-label-warning.waves-effect .waves-ripple {
  background: radial-gradient(
    rgba(255, 159, 67, 0.2) 0,
    rgba(255, 159, 67, 0.3) 40%,
    rgba(255, 159, 67, 0.4) 50%,
    rgba(255, 159, 67, 0.5) 60%,
    rgba(255, 255, 255, 0) 70%
  );
}
.btn-label-warning:hover {
  border-color: transparent !important;
  background: #ffe8d2 !important;
  color: #ff9f43 !important;
}
.btn-label-warning.focus,
.btn-label-warning:focus {
  color: #ff9f43;
  background: #ffe8d2;
  box-shadow: none !important;
}
.btn-label-warning.active,
.btn-label-warning.show.dropdown-toggle,
.btn-label-warning:active,
.show > .btn-label-warning.dropdown-toggle {
  color: #ff9f43 !important;
  background-color: #ffe8d2 !important;
  border-color: transparent !important;
}
.btn-label-warning.active:focus,
.btn-label-warning.show.dropdown-toggle:focus,
.btn-label-warning:active:focus,
.show > .btn-label-warning.dropdown-toggle:focus {
  box-shadow: none;
}
.btn-label-warning.disabled,
.btn-label-warning:disabled {
  color: #ff9f43 !important;
  border-color: transparent !important;
  background: #fff1e3 !important;
  box-shadow: none;
}
.btn-label-info {
  color: #00cfe8;
  border-color: transparent;
  background: #d6f7fb;
}
.btn-label-info.waves-effect .waves-ripple {
  background: radial-gradient(
    rgba(0, 207, 232, 0.2) 0,
    rgba(0, 207, 232, 0.3) 40%,
    rgba(0, 207, 232, 0.4) 50%,
    rgba(0, 207, 232, 0.5) 60%,
    rgba(255, 255, 255, 0) 70%
  );
}
.btn-label-info:hover {
  border-color: transparent !important;
  background: #c2f3f9 !important;
  color: #00cfe8 !important;
}
.btn-label-info.focus,
.btn-label-info:focus {
  color: #00cfe8;
  background: #c2f3f9;
  box-shadow: none !important;
}
.btn-label-info.active,
.btn-label-info.show.dropdown-toggle,
.btn-label-info:active,
.show > .btn-label-info.dropdown-toggle {
  color: #00cfe8 !important;
  background-color: #c2f3f9 !important;
  border-color: transparent !important;
}
.btn-label-info.active:focus,
.btn-label-info.show.dropdown-toggle:focus,
.btn-label-info:active:focus,
.show > .btn-label-info.dropdown-toggle:focus {
  box-shadow: none;
}
.btn-label-info.disabled,
.btn-label-info:disabled {
  color: #00cfe8 !important;
  border-color: transparent !important;
  background: #d9f8fc !important;
  box-shadow: none;
}
.bg-label-secondary {
  background-color: #f2f2f3 !important;
  color: #a8aaae !important;
}
.bg-label-success {
  background-color: #dff7e9 !important;
  color: #28c76f !important;
}
.bg-label-info {
  background-color: #d9f8fc !important;
  color: #00cfe8 !important;
}
.bg-label-warning {
  background-color: #fff1e3 !important;
  color: #ff9f43 !important;
}
.bg-label-danger {
  background-color: #fce5e6 !important;
  color: #ea5455 !important;
}
.bg-label-light {
  background-color: #fafafb !important;
  color: #dfdfe3 !important;
}
.bg-label-dark {
  background-color: #e4e4e4 !important;
  color: #4b4b4b !important;
}
.bg-label-gray {
  background-color: rgba(254, 254, 254, 0.8575) !important;
  color: rgba(75, 70, 92, 0.05) !important;
}

.text-primary {
  color: $primary !important;
}

[dir="rtl"] .g-bg {
  background: linear-gradient(91deg, #365a4e 50.94%, #488371 106.46%);
}

.text-white {
  color: white !important;
}

.pi {
  //margin-top: 9px !important;
  margin-right: 3px;
  margin-left: 3px;
}

.pi.p-button-icon {
  margin-top: 3px !important;
  margin-right: 3px;
  margin-left: 3px;
}

.p-toast .p-toast-message {
  background: whitesmoke !important;
}

@media (min-width: 400px) {
  .d-xxl-flex {
    display: flex !important
  ;
  }
}

// html {
//   zoom: 85%;
// }

// @supports (-moz-appearance: none) {
//   html {
//     zoom: 85%;
//   }
// }

:root {
  --side-width: 3.5rem;
  --side-hover-width: 13rem;
  --wrapper-padding: 0;
}

.mk-side-nav {
  background: #365a4e;
  width: 252px;
}
.mk-wrapper {
  // padding-inline-start: var(--wrapper-padding);
}
.custom-textarea {
  max-width: 100%;
}
@media (min-width: 768px) {
  :root {
    --wrapper-padding: 3.5rem;
  }
}
@media (max-width: 768px) {
  .nav-open {
    .mk-side-nav {
      display: block !important;
      min-width: 100%;
      margin-top: 4rem;
    }
  }
  // fixing landing page menu in mobile
  .mk-landing-nav {
    background-color: rgba(lighten($primary, 10%), 0.7);
    backdrop-filter: blur(5px);
    padding: 1rem;
    border-radius: 5px;
  }
  .custom_navbar {
    background-color: rgba(lighten($primary, 5%), 0.9);
    backdrop-filter: blur(2px);
    padding: 0.5rem;
  }
}

.cutom-primeng .p-dropdown .p-inputtext {
  border-color: transparent;
  box-shadow: none;
}

.cutom-primeng .p-dropdown .p-dropdown-label.p-placeholder {
  opacity: 0.3;
}
