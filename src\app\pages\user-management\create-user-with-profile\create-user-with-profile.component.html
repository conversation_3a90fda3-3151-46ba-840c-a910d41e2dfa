<main class="pt-1">
  <div class="container-xxl">
    <div class="d-flex justify-content-between align-items-end mb-3">
      <div>
        <h3 class="text-primary">إدارة المستخدمين</h3>
        <nav aria-label="breadcrumb m-0">
          <ol class="breadcrumb pt-1">
            <li class="breadcrumb-item"><a [routerLink]="['/']">الرئيسية</a></li>
            <li class="breadcrumb-item"><a [routerLink]="'/user-managements'">إدارة المستخدمين</a></li>
            <li class="breadcrumb-item active" aria-current="page">
              <b>
                إضافة مستخدم جديد
              </b>
            </li>
          </ol>
        </nav>
      </div>


      <div>

        <button routerLink="/user-managements" class="btn btn-outline-danger">
          إلغاء
        </button>

      </div>
    </div>

    <div class="card">

      <div class="card-body">

        <form class="row" #createForm="ngForm" [formGroup]="controls.form" (ngSubmit)="submit()">

          <div class="col-12">
            <label for="name" class="form-label">الاسم</label>
            <input id="name" class="form-control" maxlength="50" minlength="2" type="text" [formControlName]="controls.name.key" />
            <lib-form-control-validations [formCtrl]="controls.name" label="الاسم" />
          </div>

          <div class="col-12">
            <label for="nationalId" class="form-label">الهوية الوطنية</label>
            <input id="nationalId" maxlength="10" minlength="10" class="form-control" type="text" [formControlName]="controls.nationalId.key" />
            <lib-form-control-validations [formCtrl]="controls.nationalId" label="الهوية الوطنية" />
          </div>

          <div class="col-12">
            <label for="email" class="form-label">البريد الإلكتروني</label>
            <input id="email"maxlength="200" placeholder="<EMAIL>" minlength="4" class="form-control" type="email" [formControlName]="controls.email.key" />
            <lib-form-control-validations [formCtrl]="controls.email"
              [label]="getSharedTranslateKey('email') | translate" />
          </div>

          <div class="col-12">
            <label for="phoneNumber" class="form-label">رقم التليفون </label>
            <input id="phoneNumber" maxlength="9" minlength="9" class="form-control" type="text" placeholder="5XXXXXXXX" [formControlName]="controls.phoneNumber.key" />
            <lib-form-control-validations [formCtrl]="controls.phoneNumber" label="رقم التليفون" />
          </div>


          <div class="col-12">
            <label for="gender" class="form-label">جنس</label>
            <div class="cutom-primeng">
              <p-dropdown optionValue="id" emptyMessage="لم يتم العثور على العنصر" name="gender"
                formControlName="controls.gender.key" [options]="genders" />
            </div>
            <lib-form-control-validations [formCtrl]="controls.gender" label="جنس" />
          </div>

          <div class="col-12">
            <label for="roleCode" class="form-label">رمز الدور</label>
            <div class="cutom-primeng">
              <p-dropdown emptyMessage="لم يتم العثور على العنصر" name="roleCode"
                formControlName="controls.roleCode.key" [options]="roles" optionValue="id" />
            </div>
            <lib-form-control-validations [formCtrl]="controls.roleCode" label="رمز الدور" />
          </div>


          <div class="col-12 p-3">
            <p-checkbox id="isActive_CK" [formControlName]="controls.isActive.key" [binary]="true"
              inputId="isActive"></p-checkbox>
            <label style="margin-right: 5px" for="isActive_CK">نشط</label>
          </div>

          <div class="col-12 d-flex justify-content-center  g-2 mt-3">

            <button [disabled]="addUserInProgress" type="submit" class="btn btn-primary mx-2">
              حفظ
            </button>


            <button routerLink="/user-managements" class="btn btn-outline-danger mx-2">
              إلغاء
            </button>


          </div>
        </form>

      </div>
    </div>


  </div>
</main>