import { Component, EventEmitter, inject, OnInit, Output } from '@angular/core';
import { Observable } from "rxjs";

import { HttpClient } from "@angular/common/http";
import { ActivatedRoute, Router } from "@angular/router";
import { BaseComponent } from '../../../shared/base-component';
import { BASE_PATH, CaseDocumentCommandResultPortalResponse, SemanticService } from '../../../services';
import { BaseTranslationKeys } from '../../../shared/public.api';
import { CommonModule } from '@angular/common';
import { SearchModule } from '../../search/search.module';

@Component({
  selector: 'users-case-viewer',
  templateUrl: './users-case-viewer.component.html',
  standalone: true, // this is the key change,
  imports: [CommonModule, SearchModule]  // Include CommonModule here
})
export class UsersCaseViewerComponent extends BaseComponent implements OnInit {
  private readonly baseBath = inject(BASE_PATH);
  private readonly _httpClient = inject(HttpClient);
  protected readonly router = inject(Router);
  protected readonly semantic = inject(SemanticService);
  @Output() onSemanticSearch = new EventEmitter();
  question: string = '';
  searchType!: 'case' | 'law';
  maxKeywords: number = 4500;
  openChat = false;
  constructor(
    private semanticService: SemanticService,
    private route: ActivatedRoute
  ) {
    super(new BaseTranslationKeys('case-viewer', 'user'))
  }


  ngOnInit() {
    const id = this.route.snapshot.paramMap.get('caseId');
    if (id != null) {
      this.viewCaseDetails(id);
    } else {
      console.error('Invalid or missing case ID in URL');
    }

    // read the query-param instead of @Input()
    this.openChat = this.route.snapshot.queryParamMap.get('openChat') === 'true';
  }

  selectedResult: any = null;
  loading = false;
  isCaseDetailsVisible = false;
  viewCaseDetails(id: any) {
    this.loading = true;
    this.isCaseDetailsVisible = false;
    this.selectedResult = null;

    this.semanticService.getCaseDocument(id).subscribe({
      next: (response: CaseDocumentCommandResultPortalResponse) => {
        this.loading = false;
        if (!response) return;

        this.selectedResult = response.data || null;
        this.isCaseDetailsVisible = true;
      },
      error: (err) => {
        this.loading = false;
        console.error('Error retrieving case details:', err);
      },
    });
  }

}
