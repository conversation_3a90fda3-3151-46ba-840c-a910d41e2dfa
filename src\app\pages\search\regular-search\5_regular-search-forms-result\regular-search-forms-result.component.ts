import { Component, OnInit } from '@angular/core';
import { RegularSearchBaseResult } from '../regular-search-base-result';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-regular-search-forms-result',
  templateUrl: './regular-search-forms-result.component.html',
  styleUrls: ['./regular-search-forms-result.component.css']
})
export class RegularSearchFormsResultComponent extends RegularSearchBaseResult<IRegularSearchFormsResult[]> implements OnInit {

  protected override getDataObservable(): Observable<IRegularSearchFormsResult[]> {
    return this._httpClient.get<IRegularSearchFormsResult[]>(this._basePath + "/document/forms/" + this.name)
  }

}

interface IRegularSearchFormsResult {
  name: string;
  description: string;
  url: string;
  type: string;
  source: string;
}
