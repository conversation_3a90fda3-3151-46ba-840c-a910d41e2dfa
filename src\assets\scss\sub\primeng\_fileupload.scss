// ---------- fileupload

.custom-upload {
  position: relative;
  .files-icon {
    opacity: 0;
    position: absolute;
    display: block !important;
    top: 11px;
    left: 9px;
  }

  .p-fileupload {
    position: relative;
    overflow: hidden;
    @extend .form-control;
    // background-color: #fff;
    // min-height: $form-control-height;
    // padding-top: 0;
    padding: 0;
    border: none;
    // border: 1px solid #D8D8D8;
    // border-radius: 1px;
    .p-button {
      // background: none;
      // color: #555;
      // padding: 0.3rem 0;
      // border: 0;
      // margin: 2px 0 0;
      border-radius: $border-radius;
      color: #3a7165;
      background-color: #fff;
      // direction: ltr;
      width: 100%;
      border: 1px solid #ced4da;
      padding: 0.6rem 1rem;
      &:focus {
        box-shadow: none !important;
      }
      .p-button-label {
        text-align: right;
        color: #000000;
        font-weight: 500;
        [dir="ltr"] & {
          text-align: left;
        }
      }
      .p-fileupload-choose {
        width: 100%;
        color: #3a7165;
        background-color: #fff;
        direction: ltr;
        &:not(.p-disabled):active {
          border: none;
          background: transparent;
        }
      }
      .p-button-icon-left {
        margin-left: 0.5rem;
        margin-right: auto;
        [dir="ltr"] & {
          margin-right: 0.5rem;
          margin-left: auto;
        }
      }
    }
    // .p-fileupload-row {
    //     background: #fff;
    //     display: grid;
    //     grid-template-columns: 35px 1fr 70px 30px;
    //     grid-gap: 1rem;
    //     padding: 0;
    //     border: 0;
    //     margin: 0 ;
    //     height: 35px;
    //     width: calc(100% - 25px);
    //     border-bottom: 1px solid #ddd;
    //     &:last-child{
    //         border: 0;
    //     }
    //     & > div{
    //         padding: 0;
    //         flex: auto;
    //         width: auto;
    //         &:last-child {
    //             text-align: left;
    //             [dir="ltr"] & {
    //                 text-align: right;
    //             }
    //         }
    //         .p-button{
    //             background: none;
    //             color: #555;
    //             padding: 0.3rem 0;
    //             border: 0;
    //             margin: 2px 0 0;
    //             .pi {
    //                 font-size: .9rem;
    //                 font-weight: 700;
    //             }
    //         }
    //         &:nth-child(3){
    //             // display: none;
    //             font-size: .8rem;
    //             color: #777;
    //             text-align: left;
    //             [dir="ltr"] & {
    //                 text-align: right;
    //             }
    //         }
    //         &:nth-child(4){
    //             text-align: left;
    //             color: #999;
    //             font-size: .8rem;
    //             @extend .text-truncate;
    //             [dir="ltr"] & {
    //                 text-align: right;
    //             }
    //         }
    //     }
    //     // img{
    //     //     max-height: 30px;
    //     //     width: auto;
    //     //     margin: 3px 0 0;
    //     //     display: block;
    //     // }
    //     // .p-fileupload-filename {
    //     //     @extend .text-truncate;
    //     //     font-size: .9rem;
    //     //     color: inherit;
    //     // }
    // }
    .p-fileupload-buttonbar {
      background: none;
      padding: 0;
      border: 0;
      color: transparent;
      border-bottom: 0 none;
      border-top-right-radius: 0px;
      border-top-left-radius: 0px;
      .p-button {
        margin-right: 0px;
      }
      p-button {
        display: none;
      }
    }
    .p-fileupload-content {
      background: none;
      padding: 0;
      border: 0;
      border-top-right-radius: 0px;
      border-top-left-radius: 0px;
      .p-progressbar {
        display: none;
      }
    }
    .ui-fileupload-row {
      border: 1px solid #dee2e6;
      background-color: #ffffff;
      .files-icons {
        opacity: 1;
        .fa-eye {
          color: #337ab7;
        }
        .p-button {
          border: none;
          background-color: transparent;
          //width: 2.357rem;
          //width: 18%;
          padding: 0;
          color: #d43f3a;
        }
      }
      // .pi-times {
      //     &::before {
      //         content: "";
      //         background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0NDggNTEyIiBmaWxsPSIjRDQzRjNBIj48IS0tISBGb250IEF3ZXNvbWUgUHJvIDYuNC4wIGJ5IEBmb250YXdlc29tZSAtIGh0dHBzOi8vZm9udGF3ZXNvbWUuY29tIExpY2Vuc2UgLSBodHRwczovL2ZvbnRhd2Vzb21lLmNvbS9saWNlbnNlIChDb21tZXJjaWFsIExpY2Vuc2UpIENvcHlyaWdodCAyMDIzIEZvbnRpY29ucywgSW5jLiAtLT48cGF0aCBkPSJNMTc3LjEgNDhoOTMuN2MyLjcgMCA1LjIgMS4zIDYuNyAzLjZsMTkgMjguNGgtMTQ1bDE5LTI4LjRjMS41LTIuMiA0LTMuNiA2LjctMy42ek0zNTQuMiA4MEwzMTcuNSAyNC45QzMwNy4xIDkuNCAyODkuNiAwIDI3MC45IDBIMTc3LjFjLTE4LjcgMC0zNi4yIDkuNC00Ni42IDI0LjlMOTMuOCA4MEg4MC4xIDMyIDI0QzEwLjcgODAgMCA5MC43IDAgMTA0czEwLjcgMjQgMjQgMjRIMzUuNkw1OS42IDQ1Mi43YzIuNSAzMy40IDMwLjMgNTkuMyA2My44IDU5LjNIMzI0LjZjMzMuNSAwIDYxLjMtMjUuOSA2My44LTU5LjNMNDEyLjQgMTI4SDQyNGMxMy4zIDAgMjQtMTAuNyAyNC0yNHMtMTAuNy0yNC0yNC0yNGgtOEgzNjcuOSAzNTQuMnptMTAuMSA0OEwzNDAuNSA0NDkuMmMtLjYgOC40LTcuNiAxNC44LTE2IDE0LjhIMTIzLjRjLTguNCAwLTE1LjMtNi41LTE2LTE0LjhMODMuNyAxMjhIMzY0LjN6Ii8+PC9zdmc+);
      //         position: absolute;
      //         width: 100%;
      //         height: 100%;
      //         background-size: 54%;
      //         background-repeat: no-repeat;
      //         top: 8px;
      //         left: 8px;
      // }
      //     }

      // .p-button {
      //     border: none;
      //     background-color: transparent;
      //     //width: 2.357rem;
      //     width: 18%;
      //     padding: 0;
      // }
      // img
      // div:nth-child(1) {
      //     display: none;
      // }
      // // size
      // div:nth-child(3) {
      //     display: none;
      // }
      // div:last-child {
      //     text-align: left;
      // }
    }
    // .p-fileupload-buttonbar,
    // .p-fileupload-content{
    // border: 0;
    // background: #0000 ;
    //}
    // .p-fileupload-content{
    //     padding: 0 0.75rem;
    //     .p-progressbar {
    //         height: 3px;
    //         background: #0000;
    //         .p-progressbar-value{
    //             background: $primary;
    //         }
    //     }
    // }
    // .p-fileupload-buttonbar{
    //     position: absolute;
    //     z-index: 2;
    //     top: 4px;
    //     left: 0;
    //     padding: 0;
    //     display: flex;
    //     justify-content: center;
    //     align-items: center;
    //     background: #fff;
    //     border-radius: 0;
    //     [dir="ltr"] & {
    //         left: auto;
    //         right: 5px;
    //     }
    //     .p-button{
    //         border-radius: 50%;
    //         width: 20px;
    //         height: 29px;
    //         display: flex;
    //         justify-content: center;
    //         align-items: center;
    //         background: #0000;
    //         // color: $primary;
    //         color: #555;
    //         border: 0;
    //         margin: 0 3px;
    //         padding: 0 15px;
    //         &:focus{
    //             box-shadow: 0 0 0;
    //         }
    //     }
    //     .p-button-icon{
    //         margin: 0;
    //         font-weight: 700;
    //         font-size: .9rem;
    //         &:before{
    //             content: "\e934";
    //         }
    //     }
    //     .p-button-label{
    //         display: none;
    //     }
    // }
  }
  .trash-icon {
    //background: url('../../../images/svg/trash-regular.svg') no-repeat;
    width: 20px;
    height: 17px;
  }
  .file-arrow-up-light {
    //background: url('../../../images/svg/file-arrow-up-light.svg') no-repeat;
    width: 14px;
    height: 16px;
  }
}
