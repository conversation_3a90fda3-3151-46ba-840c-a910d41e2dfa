
export interface ExtractInfoResponse {
  keywordPredict: KeywordPredict;
  ner: NerPredict;
  classification: ClassificationPredict;
}

export interface Keyword {
  value: string;
  position: number;
}

export interface KeywordPredict {
  keywords: Keyword[];
  grouped_keywords: string[];
}

export interface NerTag {
  tag: string;
  word: string;
}

export interface NerPredict {
  tags: NerTag[];
}

export interface ClassificationPredict {
  classificationMainCategory: string;
  classificationSubCategory: string;
  datasetType: string;
  extractedEntities: any[];
  extractedKeywords: any[];
  datasetIndexes: any[];
  datasetSimilarities: any[];
  dataset: string;
  id: string;
}

export interface ITagResult {
  tag: string;
  word: string;
}


export class SemanticExtractSearchResult {
  caseText!: string;
  caseDate!: string;
  verdict!: string;
  law!: string;
  category!: string;
  subCategory!: string;
  keyWords!: string[];
  entities!: string[];
}
