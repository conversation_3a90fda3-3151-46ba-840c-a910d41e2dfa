{"auth": {"login": "تسجيل الدخول", "logout": "تسجيل الخروج", "register": "إنشاء حساب", "username": "اسم المستخدم", "password": "كلمة المرور", "confirmPassword": "تأكيد كلمة المرور"}, "landing": {"extractLegalInfo": "استخراج المعلومات القانونية", "extractLegalInfo_desc": "الهدف يكمن في تبسيط استرجاع وتحليل النصوص بشكل أوسع واستخدامها في عدة مهام مثل البحث الدلالي في القضايا والسوابق القضائية وكذلك في القوانين والتعاميم واللوائح", "extractLegalTerms": "استخراج المصطلحات القانونية", "extractLegalTerms_desc": "الهدف يكمن في تبسيط استرجاع وتحليل النصوص بشكل أوسع واستخدامها في عدة مهام مثل البحث الدلالي في القضايا والسوابق القضائية وكذلك في القوانين والتعاميم واللوائح", "catagoriesDocs": "تصنيف المستندات الطويلة والقصيرة", "catagoriesDocs_desc": "الهدف يكمن في تبسيط استرجاع وتحليل النصوص بشكل أوسع واستخدامها في عدة مهام مثل البحث الدلالي في القضايا والسوابق القضائية وكذلك في القوانين والتعاميم واللوائح", "semanticSearch": "البح<PERSON> الدلالي", "semanticSearch_desc": "الهدف يكمن في تبسيط استرجاع وتحليل النصوص بشكل أوسع واستخدامها في عدة مهام مثل البحث الدلالي في القضايا والسوابق القضائية وكذلك في القوانين والتعاميم واللوائح", "subscribeInFreeTrail": "إشترك الان في التجربة المجانية"}, "shared": {"extract-info": "استخراج المعلومات", "email": "بريد الكتروني", "name": "الإسم", "age": "العمر", "dob": "تاريخ الميلاد", "gender": "الجنس", "job": "المهنة", "male": "ذكر", "female": "انثى", "martial_status": "الحالة الاجتماعية", "lookup": "nameAr", "msg": {"required": "حقل {{$}} مطلوب", "invalidField": "صيغة {{$}} غير صحيحة", "SaudiAndIqamaIdErrorMsg": "رقم الهوية يجب أن لايتجاوز 10 أرقام ويجب أن يبدأ بالرقم 1 او 2", "phoneNumberErrorMsg": "رقم الجوال يجب أن يكون 9 أرقام ويجب أن يبدأ بالرقم 5", "noResult": "لا توجد بيانات", "onlySaudiId": "رقم الهوية يجب أن لايتجاوز 10 أرقام ويجب أن يبدأ بالرقم 1", "onlyIqamaId": "رقم الإقامة يجب أن لايتجاوز 10 أرقام ويجب أن يبدأ بالرقم 2", "maxlength": "{{$}} هو الحد الاقصى", "maxRang": "الح<PERSON> الاعلى {{$}}", "minRang": "الح<PERSON> الادنى {{$}}", "textEditorMaxLength": "{{field}} يجب أن لايتجاوز {{$}} حرف", "textMaxLength": "{{field}} يج<PERSON> يكون علي الاكثر {{$}} حرف", "textMinLength": "{{field}} يج<PERSON> أن يكون {{$}} حرف على الاقل", "passportErrorMsg": "رقم جواز السفر يجب أن يحتوي على حروف و أرقام أو أرقام فقط وأن يكون من 6 إلى 15 خانة", "linkErrorMsg": "الرجاء التأكد من أن صيغة الرابط هي كالآتي: www.example.com", "allowedFiles": " صيغة الملف المسموح به هي PDF ", "exceedFileSize": "الح<PERSON> الأق<PERSON>ى لحجم الملف 2 ميجابايت", "dateMinErrorMsg": "{{field}} يجب أن يكون اكبر من {{$}}", "dateMaxErrorMsg": "{{field}} يجب أن يكون اصغر من {{$}}", "numberLengthErrorMsg": "{{field}} يجب أن يكون {{$}} ارقام", "confirmPasswordNotMatch": "البيانات المدخلة في حقل تأكيد كلمة المرور لا تتطابق مع البيانات المدخلة في حقل كلمة المرور"}}, "error": {"8000": "المستخدم مسجل مسبقًا"}}