import { Injectable, Injector } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, CanActivateChild, RouterStateSnapshot, UrlTree } from '@angular/router';
import { BaseAuthGuard } from './base-auth.guard';

@Injectable({
  providedIn: 'root'
})
export class AuthorizationGuard extends BaseAuthGuard implements CanActivate, CanActivateChild {


  constructor(injector: Injector) {
    super(injector);
  }

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot): boolean | UrlTree {
    return this.validate(route);
  }

  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot): boolean | UrlTree {
    return this.validate(childRoute);
  }

  private validatePermissions(routePermissions: any[]): boolean {
    const routePermissionsSet = new Set(routePermissions);
    for (let i = 0; i < this.authService.UserProfile.permissions.length; i++) {
      if (routePermissionsSet.has(this.authService.UserProfile.permissions[i])) {
        return true;
      }
    }
    return false;
  }

  private validate(route: ActivatedRouteSnapshot) {
    const roles: Array<number> | undefined = route.data["roles"] ?? undefined;
    const inputs = this.getRequiredDate(route);
    let validPermission = true;
    let validRole = true;

    if (inputs.permission)
      validPermission = this.validatePermissions(inputs.permission);


    if (inputs.role)
      validRole = this.authService.UserProfile.currentUserProfile?.role.id == inputs.role;

    
    if (roles)
      validRole = roles.includes(this.authService.UserProfile.currentUserProfile?.role.id??0);

    if (this.isAuthenticated && validPermission && validRole)
      return true;

    // return this.router.createUrlTree(['/']);
    return this.router.createUrlTree(['/unauthorized']);
  }

  private getRequiredDate(route: ActivatedRouteSnapshot): { role?: number, permission?: Array<number> | undefined } {
    const permission: Array<number> | undefined = route.data["permission"] ?? undefined;
   
    const role = route.data["role"] ? +route.data["role"] : undefined;

    return { role, permission };
  }


}
