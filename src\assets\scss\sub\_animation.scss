
/* animation */
$smoth: 50px; // setting the animation smothness
$smother: 20px; // setting the animation for more smothness

// ---------- fadeInLeft
@-webkit-keyframes fadeInLeft { 0% { opacity: 0; -webkit-transform: translateX(-$smother); transform: translateX(-$smother); }  100% { opacity: 1;  -webkit-transform: translateX(0); transform: translateX(0);  }}
@keyframes fadeInLeft { 0% { opacity: 0; -webkit-transform: translateX(-$smother); transform: translateX(-$smother); }  100% { opacity: 1;  -webkit-transform: translateX(0); transform: translateX(0);  }}
.fadeInLeft{
    -webkit-animation-name: fadeInRight;  
    animation-name: fadeInRight;
    [dir="rtl"] &{
       -webkit-animation-name: fadeInLeft; 
       animation-name: fadeInLeft;
   }
}

// ---------- fadeInRight
@-webkit-keyframes fadeInRight { 0% { opacity: 0; -webkit-transform: translateX($smother); -ms-transform: translateX($smother); transform: translateX($smother);  }  100% { opacity: 1; -webkit-transform: translateX(0); -ms-transform: translateX(0); transform: translateX(0);  }}
@keyframes fadeInRight { 0% { opacity: 0; -webkit-transform: translateX($smother); -ms-transform: translateX($smother); transform: translateX($smother);  }  100% { opacity: 1; -webkit-transform: translateX(0); -ms-transform: translateX(0); transform: translateX(0);  }}
.fadeInRight {
    -webkit-animation-name: fadeInLeft; 
    animation-name: fadeInLeft;
    [dir="rtl"] &{
        -webkit-animation-name: fadeInRight; 
        animation-name: fadeInRight; 
    }
}

// ---------- fadeInUp
@-webkit-keyframes fadeInUp { 0% { opacity: 0; -webkit-transform: translateY($smoth); -ms-transform: translateY($smoth); transform: translateY($smoth);  }  100% { opacity: 1; -webkit-transform: translateY(0); -ms-transform: translateY(0); transform: translateY(0);  }}
@keyframes fadeInUp { 0% { opacity: 0; -webkit-transform: translateY($smoth); -ms-transform: translateY($smoth); transform: translateY($smoth);  }  100% { opacity: 1; -webkit-transform: translateY(0); -ms-transform: translateY(0); transform: translateY(0);  }}
.fadeInUp    { -webkit-animation-name: fadeInUp;  animation-name: fadeInUp;} 

// ---------- FadeDown
@-webkit-keyframes fadeInDown { 0% { opacity: 0; -webkit-transform: translateY(-$smother); -ms-transform: translateY(-$smother); transform: translateY(-$smother);  }  100% { opacity: 1; -webkit-transform: translateY(0); -ms-transform: translateY(0); transform: translateY(0);  }}
@keyframes fadeInDown { 0% { opacity: 0; -webkit-transform: translateY(-$smother); -ms-transform: translateY(-$smother); transform: translateY(-$smother);  }  100% { opacity: 1; -webkit-transform: translateY(0); -ms-transform: translateY(0); transform: translateY(0);  }}
.fadeInDown    { -webkit-animation-name: fadeInDown;  animation-name: fadeInDown;} 

// // ---------- Pulse
@-webkit-keyframes pulse { 0% { -webkit-box-shadow: 0 0 0 0 rgba(250,250,250, 0.4);} 70% { -webkit-box-shadow: 0 0 0 15px rgba(250,250,250, 0);} 100% { -webkit-box-shadow: 0 0 0 0 rgba(250,250,250, 0);}}
@keyframes pulse { 0% { -moz-box-shadow: 0 0 0 0 rgba(250,250,250, 0.4); box-shadow: 0 0 0 0 rgba(250,250,250, 0.4); } 70% { -moz-box-shadow: 0 0 0 15px rgba(250,250,250, 0); box-shadow: 0 0 0 10px rgba(250,250,250, 0);} 100% { -moz-box-shadow: 0 0 0 0 rgba(250,250,250, 0); box-shadow: 0 0 0 0 rgba(250,250,250, 0);}}
.pulse { box-shadow: 0 0 0 rgba(250,250,250, 0);  animation: pulse 2s infinite;}
.pulse:hover {  animation: none;}

// // ---------- Waver
// @-webkit-keyframes waver { 0% { background-position-x: left } 50% { background-position-x: right } 100% { background-position-x: left }} 
// @keyframes waver { 0% { background-position-x: left } 50% { background-position-x: right } 100% { background-position-x: left } }
// .waver { -webkit-animation-name: waver;  animation-name: waver;}

// // ---------- ZoomIn
// @-webkit-keyframes zoomIn { 0% { -webkit-transform: scale(1.15); transform: scale(1.15); } 100% { -webkit-transform: scale(1); transform: scale(1); } }
// @keyframes zoomIn { 0% { -webkit-transform: scale(1.15); transform: scale(1.15); } 100% { -webkit-transform: scale(1); transform: scale(1); } }
// .zoomIn { -webkit-animation-name: zoomIn;  animation-name: zoomIn;}

// // ---------- Spin
// @-webkit-keyframes spinMe { to { transform: rotate(2turn) ; } }
// @keyframes spinMe { to { transform: rotate(2turn) ; } }
// .spinMe { -webkit-animation: spinMe 1s forwards; animation: spinMe 1s forwards;}

// // ---------- loading bounce
@-webkit-keyframes  bounce-3 { 0% { transform: translateY(0) } 20% { transform: translateY(-$smother) } 40% { transform: translateY(0) } 100% { transform: translateY(0) } }
@keyframes bounce-3          { 0% { transform: translateY(0) } 50% { transform: translateY(-$smother) } 100% { transform: translateY(0) } }

.animate,
.alert     { -webkit-animation-fill-mode: both; animation-fill-mode: both; -webkit-animation-duration: 1.0s; animation-duration: 1.0s ; }

// // ---------- Manage time delay
.delay{ -webkit-animation-delay: 0.5s; animation-delay: 0.5s}
.delay1{ -webkit-animation-delay: 0.8s; animation-delay: 0.8s}
.delay2{ -webkit-animation-delay: 1.1s; animation-delay: 1.1s}
.easy1{ animation-duration: 1.2s;}
// .delay3{ -webkit-animation-delay:  0.2s; animation-delay: 0.2s}


// // ---------- if you are using AOS pluginm, uncomment the following style

.aos-init{
    opacity: 0; 
    transition-property: opacity,transform;
    transition-timing-function: ease;
    &[data-aos=fade-up] { transform: translate3d(0, 100px, 0); }
    &[data-aos=fade-right] { transform: translate3d(-100px, 0, 0); } 
    &[data-aos=fade-left] { transform: translate3d(100px, 0, 0); }  
    &[data-aos][data-aos-duration="1000"] { transition-duration: 1.0s; } 
    &[data-aos][data-aos-duration="1500"] { transition-duration: 1.5s; }
    &[data-aos][data-aos-duration="2000"] { transition-duration: 2s; } 
}
[data-aos].aos-animate{
    // @extend .animate; 
    opacity: 1;
    transform:translateZ(0); 
}
[data-aos][data-aos-delay="100"] { transition-delay: .1s; }
[data-aos][data-aos-delay="200"] { transition-delay: .2s; }
[data-aos][data-aos-delay="300"] { transition-delay: .3s; }
[data-aos][data-aos-delay="400"] { transition-delay: .4s; }
[data-aos][data-aos-delay="500"] { transition-delay: .5s; } 
[data-aos][data-aos-delay="600"] { transition-delay: .6s; } 
/* End animation */
