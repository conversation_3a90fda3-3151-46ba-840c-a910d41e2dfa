
.nav-btn.active {
  background-color: var(--backgroundColorPrimaryDark);
  color: whitesmoke;
}

.nav-btn {
  color: white;
  line-height: 2;
  border-radius: 5rem;
  min-width: 5rem;
  background-color: rgba(72, 131, 113, 0.5);
}

.btn-xs {
  padding: 2px 6px;
  font-size: 0.75rem;
}

.template-background {
  background-color: #fff;
}

.card-body {
  text-align: right;
}

.selected-item {
  background-color: #f1f5f9 !important;
  border-left: 5px solid #00ae8d !important;
  transition: background-color 0.3s ease-in-out;
}
