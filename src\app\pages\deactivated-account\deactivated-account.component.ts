import {Component, inject} from '@angular/core';
import {Router} from "@angular/router";
import {AuthService} from "../../auth/auth.service";
import {AngularSvgIconModule} from "angular-svg-icon";

@Component({
  selector: 'app-deactivated-account',
  standalone: true,
  imports: [AngularSvgIconModule],
  templateUrl: './deactivated-account.component.html',
  styleUrl: './deactivated-account.component.scss'
})
export class DeactivatedAccountComponent {
  constructor(private router: Router) {}
  private readonly authService = inject(AuthService);

  redirectToHomepage() {
    this.authService.logout();
    this.router.navigate(['/']);
  }
}
