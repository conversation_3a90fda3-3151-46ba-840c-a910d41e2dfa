import { ApiResponse } from "./api-response.interface";

/**
 * ******************* This is new Lookups implementation ************
 */
export interface IStaticLookupResult extends ILookupResult {
    id?: number;
    code?: string;
    name: string,
    nameAr: string;
}

export interface ILookupDetailsResult extends ILookupResult {
    id: number;
    name: string,
    nameAr: string;
}

export interface ILookupResult {
    name: string,
    nameAr: string;
}

export interface LookupObjectResult {
    lookupName: string,
    lookupList: ApiResponse<IStaticLookupResult[]>;
}

export interface Lookup {
    id: number;
    name?: string,
    nameAr?: string; 
}

