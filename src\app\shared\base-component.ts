
import { ChangeDetectorRef, Injector, inject } from "@angular/core";

import { BaseTranslationKeys } from "./models/base-translation-keys.model";
import { TranslateService } from "@ngx-translate/core";
import { Router } from "@angular/router";
import { MessageService } from "primeng/api";


export abstract class BaseComponent {

  protected readonly baseTranslateKeys!: BaseTranslationKeys;
  protected readonly _translateService = inject(TranslateService);
  protected readonly _router = inject(Router);
  protected readonly _cdf = inject(ChangeDetectorRef);
  protected readonly _messageService = inject(MessageService);

  constructor(keyTranslate: BaseTranslationKeys) {
    this.baseTranslateKeys = keyTranslate;
  }

  protected toJson(obj: any) {
    return JSON.stringify(obj)
  }

  protected getTranslateKey(key: any, isShared: boolean = false) {
    if (isShared)
      return key;
    else if (!this.baseTranslateKeys.componentKey)
      return this.baseTranslateKeys?.moduleKey + '.' + key;
    else
      return this.baseTranslateKeys?.moduleKey + '.' + this.baseTranslateKeys?.componentKey + '.' + key;
  }
  protected getModuleTranslateKey(key: any) {
    return `${this.baseTranslateKeys?.moduleKey}.${key}`;
  }

  protected getSharedTranslateKey(key: any) {
    return 'shared.' + key;
  }

  protected getSharedMessageTranslateKey(key: any) {
    return 'shared.msg.' + key;
  }
  protected getSharedSummaryTranslateKey(key: any) {
    return 'shared.summary.' + key;
  }

  protected getNotificationMessageTranslateKey(key: any) {
    return 'notificationMessages.' + key;
  }

  protected getErrorTranslateKey(key: any) {
    return 'error.' + key;
  }

  /**
    * Returns a translation instantly from the internal state of loaded translation.
    * All rules regarding the current language, the preferred language of even fallback languages will be used except any promise handling.
    */
  protected getTranslationValue(key: string) {
    return this._translateService.instant(key);
  }

  protected setPagination<TPage extends {
    pageNumber?: number,
    pageSize?: number
  }>(event: LazyPaginationEvent, input: TPage) {
    const size = event.rows == 0 || !event.rows ? 10 : event.rows;
    const offset = event.first == 0 ? size : event.first + size;
    input.pageNumber = (offset / size);
    input.pageSize = size;
  }

  protected get CurrentLanguage() {
    return this._translateService.currentLang;
  }



}

export interface LazyPaginationEvent {
  first: number;
  rows: number;
  sortField: string;
  sortOrder: number;
}


