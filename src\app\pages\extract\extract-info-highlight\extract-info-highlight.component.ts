import { Component } from '@angular/core';
import { AngularSvgIconModule } from 'angular-svg-icon';
import { SkeletonModule } from 'primeng/skeleton';


@Component({
  selector: 'app-extract-info-highlight',
  templateUrl: './extract-info-highlight.component.html',
  styleUrl: './extract-info-highlight.component.scss',
})
export class ExtractInfoHighlightComponent {
  isCollapsed: boolean = false;
  value: string | undefined;

  toggleCollapse(id: number): void {
    this.isCollapsed = !this.isCollapsed;
  }
}
