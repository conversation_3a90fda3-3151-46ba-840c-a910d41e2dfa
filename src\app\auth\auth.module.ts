import { NgModule } from '@angular/core';
import {AbstractSecurityStorage, AuthModule as SSO_AuthModule, StsConfigLoader} from 'angular-auth-oidc-client';
import { AuthComponent } from './auth.component';
import { Auth<PERSON><PERSON><PERSON>Component } from './auth-checker/auth-checker.component';
import { CommonModule } from '@angular/common';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { HttpBackend } from '@angular/common/http';
import { loadOpenIdConfiguration } from './factories/loadOidcConfiguration';
import { LoginCallbackComponent } from './login-callback/login-callback.component';
import { RouterModule } from '@angular/router';
import { AccountActivationGuard } from './guards/account-activation.guard';
import { AuthenticatedGuard } from './guards/authenticated.guard';
import { AppConfigService, TranslateFactory } from '../shared/public.api';
import {BrowserStorageService} from "../shared/services/storage/browser-storage.service";

@NgModule({
  declarations: [
    AuthComponent,
    AuthCheckerComponent,
  ],
  imports: [
    CommonModule,
    // SSO_AuthModule.forRoot({
    //   loader: {
    //     provide: StsConfigLoader,
    //     useFactory: loadOpenIdConfiguration,
    //     deps: [AppConfigService]
    //   }
    // }),
    // ngx-translate and the loader module
    // TranslateModule.forRoot({
    //   loader: {
    //     provide: TranslateLoader,
    //     useFactory: TranslateFactory,
    //     deps: [HttpBackend]
    //   }
    // }),

  ],
  exports: [
    AuthComponent,
    AuthCheckerComponent
  ],
  providers: [
    AuthenticatedGuard,
    AccountActivationGuard
  ]
})
export class AuthModule {

  constructor() {
  }
}


