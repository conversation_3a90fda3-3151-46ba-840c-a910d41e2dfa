<div class="row">
  <div class="col-6">
    <span class="p-input-icon-left">
      <i class="pi pi-search"></i>
      <input
        type="text"
        class="p-inputtext-sm form-control"
        pInputText
        placeholder="البحث .."
        [(ngModel)]="value"
      />
    </span>
  </div>
  <div class="col-3">
    <div class="cutom-primeng">
      <p-dropdown
        emptyMessage="لم يتم العثور على العنصر"
        name="mainClassificationList"
        [(ngModel)]="selectedCaseTextToBeHighlight"
        [showClear]="true"
        [options]="nerPredictWords"
        (onClear)="resetHighlights()"
        (onChange)="applyHighlighsOnCase($event.value)"
        placeholder="التصنيف الرئيسي"
      />
    </div>
  </div>
  <div class="col-3">
    <div class="dropdown">
      <button
        class="btn btn-outline-primary dropdown-toggle px-5 py-2"
        type="button"
        id="nerDropdown"
        (click)="entitiesOverlay.toggle($event)"
      >
        <span class="px-1"> الأعلام</span>
      </button>
      <p-overlayPanel [scrollTop]="true" #entitiesOverlay>
        <div class="dropdown-overlay-container">
          <div class="row g-2 row-cols-1">
            <div class="col" *ngFor="let item of nerList">
              <span class="text-primary"> - {{ item }} </span>
            </div>
          </div>
        </div>
      </p-overlayPanel>

      <ul class="dropdown-menu" aria-labelledby="nerDropdown">
        <li *ngFor="let ner of nerList">
          <a class="dropdown-item">{{ ner }}</a>
        </li>
      </ul>
    </div>
  </div>
</div>

<div class="card-body">
  <div class="d-inline-flex flex-wrap align-items-center gap-2 my-2">
    <button
      class="tab-button"
      [ngClass]="{ active: currentTab === MainTab.CaseText }"
      (click)="updateTab(MainTab.CaseText)"
    >
      نص القضية
    </button>

    <button
      class="tab-button"
      [ngClass]="{ active: currentTab === MainTab.SessionLaw }"
      (click)="updateTab(MainTab.SessionLaw)"
    >
      قانون الجلسة
    </button>

    <button
      class="tab-button"
      [ngClass]="{ active: currentTab === MainTab.Verdict }"
      (click)="updateTab(MainTab.Verdict)"
    >
      الحكم
    </button>

    <button
      class="tab-button"
      [ngClass]="{ active: currentTab === MainTab.Classification }"
      (click)="updateTab(MainTab.Classification)"
    >
      التصنيفات
    </button>

    <button
      class="tab-button"
      [ngClass]="{ active: currentTab === MainTab.Summary }"
      (click)="updateTab(MainTab.Summary)"
    >
      الملخص
    </button>

    <a
      *ngIf="isFavorite"
      href="javascript:void(0)"
      (click)="removeFavorite()"
      title="إزالة من المفضلة"
    >
      <i class="pi pi-bookmark-fill fs-5 text-primary"></i>
    </a>

    <a
      *ngIf="!isFavorite"
      href="javascript:void(0)"
      (click)="addToFavorite()"
      title="إضافة إلى المفضلة"
    >
      <i class="pi pi-bookmark fs-5 text-secondary"></i>
    </a>
  </div>
</div>

<div class="card-body py-3" *ngIf="currentTab === MainTab.CaseText">
  <div class="d-flex justify-content-between p-1">
    <label class="fs-5 fw-bold">
      {{ title }}
    </label>
  </div>

  <div
    #highlightContainer
    *ngIf="selectedCase"
    class="scroll-lg text-justify pe-1"
    style="white-space: pre-line"
  >
    <!-- ACTIONS -->
    <app-highlight-section
      #highlightAction
      *ngIf="selectedCase && highlightfetched"
      [section]="'Actions'"
      [selectedText]="selectedCase.actions || ''"
      [refId]="selectedCase.refId"
      [caseId]="selectedCase.caseId"
      [header]="'الوقائع'"
      [rawText]="selectedCase.actions || ''"
      [highlightHistory]="highlightHistory"
    ></app-highlight-section>

    <!-- REASONS -->
    <app-highlight-section
      #highlightReason
      *ngIf="selectedCase && highlightfetched"
      [section]="'Reasons'"
      [selectedText]="selectedCase.reasons || ''"
      [refId]="selectedCase.refId"
      [caseId]="selectedCase.caseId"
      [header]="'الأسباب'"
      [rawText]="selectedCase.reasons || ''"
      [highlightHistory]="highlightHistory"
    ></app-highlight-section>

    <!-- VERDICT -->
    <app-highlight-section
      #highlightVerdict
      *ngIf="selectedCase && highlightfetched"
      [section]="'Verdict'"
      [selectedText]="selectedCase.verdict || ''"
      [refId]="selectedCase.refId"
      [caseId]="selectedCase.caseId"
      [header]="'نص الحكم'"
      [rawText]="selectedCase.verdict || ''"
      [highlightHistory]="highlightHistory"
    ></app-highlight-section>

    <div
      *ngIf="
        selectedCase.groupedKeywords && selectedCase.groupedKeywords.length
      "
      style="margin-top: 1rem"
    >
      <div>
        <label class="fw-bold">الكلمات الدالة</label>
      </div>
      <div class="d-flex flex-wrap">
        <span
          *ngFor="let keyword of selectedCase.groupedKeywords"
          class="badge bg-secondary m-1"
        >
          {{ keyword }}
        </span>
      </div>
    </div>

    <div
      *ngIf="
        selectedCase.groupedNamedEnities &&
        selectedCase.groupedNamedEnities.length
      "
      style="margin-top: 1rem"
    >
      <div>
        <label class="fw-bold">الأعلام القانونية</label>
      </div>
      <div class="d-flex flex-wrap">
        <span
          *ngFor="let entity of selectedCase.groupedNamedEnities"
          class="badge bg-secondary m-1"
        >
          {{ entity }}
        </span>
      </div>
    </div>
  </div>

  <!-- Update Here  -->
  <div class="d-flex gap-2 mt-5">
    <comments-shared
      *ngIf="selectedCase"
      [refId]="selectedCase.refId ?? ''"
      [caseId]="selectedCase.caseId ?? ''"
      [text]="selectedCase?.cleanTxt ?? ''"
      [typeId]="favoriteType.Case"
    ></comments-shared>
  </div>

  <app-chat-popup
    #chatOverlay
    *ngIf="selectedCase"
    [selectedCase]="selectedCase"
  >
  </app-chat-popup>
</div>
<div class="card-body py-3" *ngIf="currentTab === MainTab.SessionLaw">
  <div class="d-flex justify-content-between align-items-center p-1">
    <label class="fs-5 fw-bold text-justify">
      {{ title }}
    </label>
  </div>
  <div class="scroll-lg">
    <div class="d-flex justify-content-between align-items-start">
      <ul>
        <li *ngFor="let rule of selectedCase?.rules">{{ rule }}</li>
      </ul>
      <label class="fs-6">
        <span class="fw-bold"> تاريخ الجلسة </span>
        <br />
        {{ selectedCase?.verdictDate }}
      </label>
    </div>
  </div>
</div>

<div class="card-body py-3" *ngIf="currentTab === MainTab.Verdict">
  <div class="d-flex justify-content-between p-1">
    <label class="fs-5 fw-bold">
      {{ title }}
    </label>
  </div>
  <div class="scroll-lg">
    <div class="text-justify">
      {{ selectedCase?.verdict }}
    </div>

    <div class="row mt-3">
      <div class="col" *ngIf="selectedCase?.verdictDate">
        <label class="fw-bold fs-6"> تاريخ الحكم </label>
        <p>
          {{ selectedCase?.verdictDate }}
        </p>
      </div>

      <div class="col" *ngIf="selectedCase?.verdictNumber">
        <label class="fw-bold fs-6"> رقم الحكم </label>
        <p>
          {{ selectedCase?.verdictNumber }}
        </p>
      </div>

      <div class="col" *ngIf="selectedCase?.auditVerdictDate">
        <label class="fw-bold fs-6"> تاريخ الاستئناف </label>
        <p>
          {{ selectedCase?.auditVerdictDate }}
        </p>
      </div>

      <div class="col" *ngIf="selectedCase?.auditVerdictNumber">
        <label class="fw-bold fs-6"> رقم حكم الاستئناف </label>
        <p>
          {{ selectedCase?.auditVerdictNumber }}
        </p>
      </div>
    </div>
  </div>
</div>

<div class="card-body py-3" *ngIf="currentTab === MainTab.Classification">
  <label class="fs-5 fw-bold"> التصنيفات </label>
  <div class="mt-3 scroll-lg">
    <label class="fw-bold fs-6"> التصنيف الرئيسي </label>
    <p>
      {{ selectedCase?.category ?? "-" }}
    </p>

    <label class="fw-bold fs-6"> التصنيف الفرعي </label>
    <p>
      {{ selectedCase?.subCategory ?? "-" }}
    </p>
  </div>
</div>

<!-- Summary Tab -->
<div class="card-body" *ngIf="currentTab === MainTab.Summary">
  <div class="d-inline-flex align-items-center gap-2 my-2">
    <button
      class="btn btn-sm btn-outline-primary rounded-5"
      [ngClass]="{ active: summarySubTab === SummarySubTab.General }"
      (click)="updateSummarySubTab(SummarySubTab.General)"
    >
      ملخص عام
    </button>
    <button
      class="btn btn-sm btn-outline-primary rounded-5"
      [ngClass]="{ active: summarySubTab === SummarySubTab.IRAC }"
      (click)="updateSummarySubTab(SummarySubTab.IRAC)"
    >
      ملخص IRAC
    </button>
  </div>

  <div class="mt-2">
    <button
      class="btn btn-sm btn-primary rounded-5"
      (click)="generateSummary()"
    >
      إضافة ملخص
    </button>
  </div>

  <div *ngIf="summarySubTab === SummarySubTab.General">
    <div class="mt-3 scroll-lg text-justify pe-1">
      <div style="white-space: pre-line">
        {{ generalSummaryText }}
      </div>

      <br />
    </div>
  </div>
  <div *ngIf="summarySubTab === SummarySubTab.IRAC">
    <div class="mt-3 scroll-lg text-justify pe-1">
      <div *ngIf="iracIssues">
        <strong>القضايا:</strong>
        <p>{{ iracIssues }}</p>
      </div>

      <div *ngIf="iracRules">
        <strong>القواعد القانونية:</strong>
        <p>{{ iracRules }}</p>
      </div>

      <div *ngIf="iracAnalysis">
        <strong>التحليل القانوني:</strong>
        <p>{{ iracAnalysis }}</p>
      </div>

      <div *ngIf="iracConclusion?.length">
        <strong>الاستنتاج:</strong>
        <ul>
          <li *ngFor="let conclusion of iracConclusion">{{ conclusion }}</li>
        </ul>
      </div>
    </div>
  </div>
</div>

<p-dialog
  header="إضافة ملاحظة"
  [(visible)]="displayNoteDialog"
  [modal]="true"
  [draggable]="false"
  [resizable]="false"
  [closable]="true"
  [style]="{ width: '600px', height: 'auto' }"
  [contentStyle]="{ 'max-height': '500px', 'overflow-y': 'auto' }"
>
  <div class="p-fluid">
    <div class="field">
      <label for="noteText" class="fw-bold">ملاحظتك</label>
      <textarea
        id="noteText"
        rows="6"
        class="form-control"
        [(ngModel)]="noteText"
      ></textarea>
    </div>
  </div>

  <hr />
  <p-footer>
    <button
      type="button"
      class="btn btn-sm btn-secondary me-2"
      (click)="displayNoteDialog = false"
    >
      إلغاء
    </button>
    <button
      type="button"
      class="btn btn-sm btn-primary"
      (click)="saveComment()"
    >
      {{ isEditingNote ? "تحديث" : "حفظ" }}
    </button>
  </p-footer>
</p-dialog>

<p-dialog
  header="قائمة الملاحظات"
  [(visible)]="displayUserUpdatesDialog"
  [modal]="true"
  [draggable]="false"
  [resizable]="false"
  [closable]="true"
  [style]="{ width: '1000px', height: 'auto' }"
  [contentStyle]="{ 'max-height': '600px', 'overflow-y': 'auto' }"
>
  <!-- Pinned Updates -->
  <div *ngIf="userUpdates.length > 0">
    <h5 class="fw-bold text-primary">ملاحظاتي</h5>
    <div
      class="border rounded p-2 mb-2 pin-highlight-background"
      *ngFor="let item of userUpdates"
    >
      <div class="fw-bold d-flex justify-content-between">
        <!--        <span>بواسطة المستخدم: {{ item.createdByUserName }}</span>-->
        <span>{{ item.creationDate | date : "fullDate" }}</span>

        <button
          class="btn btn-sm btn-outline-primary rounded-5 mt-2"
          (click)="openAddNoteDialog()"
        >
          تعديل
        </button>
      </div>

      <div class="text-container">
        {{ item.comment }}
      </div>
    </div>
  </div>

  <!-- Other Updates -->
  <div *ngIf="otherUpdates.length > 0">
    <div class="d-flex justify-content-between align-items-center mb-3">
      <h5 class="fw-bold">ملاحظات المستخدمين</h5>
      <div>
        <button
          class="btn btn-sm btn-outline-primary rounded-5 me-2"
          (click)="sortUserUpdates('DESC')"
        >
          الأحدث أولاً
        </button>
        <button
          class="btn btn-sm btn-outline-primary rounded-5"
          (click)="sortUserUpdates('ASC')"
        >
          الأقدم أولاً
        </button>
      </div>
    </div>

    <div class="border rounded p-2 mb-2" *ngFor="let item of otherUpdates">
      <div class="fw-bold d-flex justify-content-between">
        <span>بواسطة المستخدم {{ item.createdByUserName }}</span>
        <span>{{ item.createdAt | date : "fullDate" }}</span>
      </div>

      <div class="text-container">
        {{ item.summaryText }}
      </div>
    </div>
  </div>

  <div *ngIf="userUpdates.length === 0">لا توجد ملاحظات</div>
</p-dialog>
