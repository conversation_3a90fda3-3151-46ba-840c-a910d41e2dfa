import { Component, EventEmitter, inject, OnInit, Output } from '@angular/core';
import { Observable } from "rxjs";

import { HttpClient } from "@angular/common/http";
import { ActivatedRoute, Router } from "@angular/router";
import { BaseComponent } from '../../../shared/base-component';
import { BASE_PATH, CaseDocumentCommandResultPortalResponse, LawDocumentCommandResultPortalResponse, SemanticService } from '../../../services';
import { BaseTranslationKeys } from '../../../shared/public.api';
import { CommonModule } from '@angular/common';
import { SearchModule } from '../../search/search.module';

@Component({
  selector: 'users-law-viewer',
  templateUrl: './users-law-viewer.component.html',
  standalone: true, // this is the key change,
  imports: [CommonModule, SearchModule]  // Include CommonModule here
})
export class UsersLawViewerComponent extends BaseComponent implements OnInit {
  protected readonly router = inject(Router);
  protected readonly semantic = inject(SemanticService);
  @Output() onSemanticSearch = new EventEmitter();
  question: string = '';
  searchType!: 'case' | 'law';
  maxKeywords: number = 4500;
  openChat = false;
  constructor(
    private semanticService: SemanticService,
    private route: ActivatedRoute
  ) {
    super(new BaseTranslationKeys('law-viewer', 'user'))
  }


  ngOnInit() {
    const id = this.route.snapshot.paramMap.get('lawId');
    if (id != null){
      this.viewLawDetails(id);
    } else {
      console.error('Invalid or missing case ID in URL');
    }
  }

  selectedResult: any = null;
  loading = false;
  isLawDetailsVisible = false;

  viewLawDetails(lawId: any) {
    this.loading = true;
    this.isLawDetailsVisible = false;
    this.selectedResult = null;

    this.semanticService.getLawDocument(lawId).subscribe({
      next: (response: LawDocumentCommandResultPortalResponse) => {
        this.loading = false;
        if (!response) return;

        this.selectedResult = response.data || null;
        this.isLawDetailsVisible = true;
      },
      error: (err) => {
        this.loading = false;
        console.error('Error retrieving law details:', err);
      },
    });
  }

}
