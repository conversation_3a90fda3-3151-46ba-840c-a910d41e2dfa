import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { FavoriteItem, FavoriteService, FavoriteTypeEnum } from "../../../services";
import { Router } from "@angular/router";
import { MySettingsBaseComponent } from "../my-settings-base.component";
import { CommonModule } from "@angular/common";
import { DialogModule } from "primeng/dialog";

@Component({
  standalone: true,
  selector: 'app-favorite-item',
  templateUrl: './favorite-item.component.html',
  imports: [CommonModule,DialogModule],
})
export class FavoriteItemComponent extends MySettingsBaseComponent implements OnInit {

  constructor(private favoriteService: FavoriteService, private router: Router) {
    super('favoriteItem')
  }

  ngOnInit(): void {

  }

  loading = false;
  @Input() entry: any;
  @Output() delete = new EventEmitter<any>();

  showDialog(entry: FavoriteItem): void {
    const numericCaseId = entry.RefId;
    this.viewDetails(entry); // assuming this expects a number
    // this.selectedEntry = entry;
    // this.dialogVisible = true;
  }

  viewDetails(entry: FavoriteItem) {
    // this.router.navigate(['/users-case-viewer', id])
    if (entry.TypeId === 1) {
      this.router.navigate(
        ['/users-case-viewer', entry.RefId],
        { queryParams: { openChat: false } }
      );
    } else if (entry.TypeId === 2) {
      this.router.navigate(
        ['/users-law-viewer', entry.RefId],
        { queryParams: { openChat: false } }
      );
    }

  }
  onDelete(e: any) { this.delete.emit(e); }
  displayDeleteConfirm: boolean = false;
  confirmingEntry: any = null;

  removeFavorite(selectedResult: any) {
    if (!selectedResult) return;
    this.loading = true;

    this.favoriteService.removeFavorites(selectedResult).subscribe({
      next: () => {
        this.loading = false;
        this.delete.emit(selectedResult);  // emit so parent can update its list
        this.displayDeleteConfirm = false;
      },
      error: err => {
        console.error('Failed to remove favorite:', err);
        this.loading = false;
      }
    });
  }

}
