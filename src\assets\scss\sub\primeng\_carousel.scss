body {
    .p-carousel-container {
        direction: ltr;
        .p-carousel-prev,
        .p-carousel-next {
            width: 3rem;
            height: 3rem;
            color: #6c757d;
            border: 0 none;
            background: transparent;
            // color: $primary;
            // border: 1px solid #6c757d;
            // background-color: #fff;
            border-radius: 50%;
            transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
            //margin: 0.5rem;
            z-index: 1;
            @include media-breakpoint-down(sm) {
                position: absolute;
                border: 1px solid #a6b6b9 !important;
                background: #218975e0 !important;
            }
            &:hover, &:enabled {
                color: #ffff;
                border-color: transparent;
                background: rgba(255, 255, 255, 0.12);
            }
            span {
                transform: scaleX(1);

            }
        }
        @include media-breakpoint-down(sm) {
            .p-carousel-prev {
                left: -14px;
                [dir="ltr"] & {
                    right: -14px;
                    left: auto;
                }
            }
            .p-carousel-next {
                right: -14px;
                [dir="ltr"] & {
                    left: -14px;
                    right: auto;
                }
            }
        }
        .p-carousel-item {
            direction: rtl;
            [dir="ltr"] & {
                direction: ltr;    
            }
            // &:after {
            //     content: '';
            //     width: 1px;
            //     position: absolute;
            //     top: 30%; 
            //     height:30%;
            //     background-color: #EFEFEF; // The color of your border
            // }
        }

    }
    .p-carousel-indicators {
        display: none;
    }
    .news-item {
        .news-item-content {
            position: relative;
            margin: .3rem;
            padding: 2rem 1.5rem;
            .item-title {
                color: #211259;
                line-height: 1.5em;
                height: 3em;
                overflow: hidden;
            }
            .item-date {
                color: #BEBEBE;
                font-size: 14px;
            }
            .item-description {
                color: #575757;
                line-height: 1.5em;
                height: 5em;
                overflow: hidden;
            }
            .item-link {
                color: #999999;
                border-top: 1px solid #E5E5E5;
            }
        }
    }
}