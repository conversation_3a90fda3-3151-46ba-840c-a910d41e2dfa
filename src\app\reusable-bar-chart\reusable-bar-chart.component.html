<div *ngIf="!loading && !error" class="p-3">
  <div class="mb-3">
    <!-- <label for="chartTypeSelect" class="form-label fw-semibold small text-muted">Chart Type</label> -->
    <select id="chartTypeSelect" [(ngModel)]="selectedChartTypeId" (ngModelChange)="onChartTypeChange()"
      class="form-select form-select-sm w-auto d-inline-block" style="min-width: 180px;">
      <option *ngFor="let type of chartTypes" [ngValue]="type.typeId">
        {{ type.typeAr }}
      </option>
    </select>
  </div>

  <canvas baseChart [data]="barChartData" [options]="barChartOptions" [type]="barChartType" [plugins]="barChartPlugins">
  </canvas>
</div>
