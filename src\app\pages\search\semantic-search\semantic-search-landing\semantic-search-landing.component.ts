import { Component, inject, OnInit } from '@angular/core';
import { SemanticService } from '../../../../services';
import { UploadAttachmentInput } from '../../../../shared/public.api';
import { SemanticExtractSearchResult } from '../../../extract/extract-info-response.model';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-semantic-search-landing',
  templateUrl: './semantic-search-landing.component.html',
  styleUrls: ['./semantic-search-landing.component.scss'],
})
export class SemanticSearchLandingComponent implements OnInit {
  private readonly semantic = inject(SemanticService);
  protected searchType: string = 'file';

  constructor(private router: Router) {}

  ngOnInit() {
    const isSearchByQuestion = localStorage.getItem('isSearchByQuestion');
    if (isSearchByQuestion) {
      this.searchType = isSearchByQuestion == 'true' ? 'text' : 'file';
    }
  }

  setSearchType(type: string) {
    this.searchType = type;

    if (type === 'file') {
      this.router.navigate(['/search/semantic/file']);
    } else {
      this.router.navigate(['/search/semantic/text']);
    }
  }

  searchResult(result: any) {
    console.log(result);

    if (this.searchType == 'file') {
      this.searchByFile(result);
    }
  }

  searchByFile(result: UploadAttachmentInput) {
    this.semantic.extractFromFile(result as any).subscribe((response) => {
      console.log(response);
      // localStorage.setItem('file', JSON.stringify(response));
      localStorage.setItem(
        'file_entities',
        JSON.stringify(response.data.entities)
      );
      localStorage.setItem(
        'file_category',
        JSON.stringify(response.data.category)
      );
      localStorage.setItem(
        'file_keywords',
        JSON.stringify(response.data.keyWords)
      );
    });
  }
}
