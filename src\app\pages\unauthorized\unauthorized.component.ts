import { Component, inject, OnInit } from '@angular/core';
import { AuthService } from '../../auth/public-api';
import { Router } from '@angular/router';
import { AngularSvgIconModule } from 'angular-svg-icon';

@Component({
  selector: 'app-unauthorized',
  templateUrl: './unauthorized.component.html',
  standalone: true,
  imports: [AngularSvgIconModule],
  styleUrls: ['./unauthorized.component.css']
})
export class UnauthorizedComponent implements OnInit {
  private readonly _auth = inject(AuthService);
  private readonly _router = inject(Router);
  constructor() {
    // if (this._auth.UserProfile) {
    //   console.log("redirect from unauthorized")
    //   this.router.navigate(['/'])
    // }
  }

  ngOnInit() {
  }

  redirectToHome = () => {
    if (this._auth.isAuthenticated) {
      this._router.navigateByUrl('/my-settings');
    } else {
      this._router.navigateByUrl('/');
    }
  }

  logout() {
    this._auth.logout();
  }
}
