.gitignore
README.md
api.module.ts
api/api.ts
api/auth.service.ts
api/case-summaries.service.ts
api/chat.service.ts
api/comment.service.ts
api/configuration.service.ts
api/favorite.service.ts
api/feedback.service.ts
api/health.service.ts
api/highlight.service.ts
api/lookup.service.ts
api/mock.service.ts
api/semantic.service.ts
api/statistics.service.ts
api/user-management.service.ts
api/user-profile.service.ts
configuration.ts
encoder.ts
git_push.sh
index.ts
model/case-data-by-file.ts
model/case-data-by-question.ts
model/case-document-command-result-portal-response.ts
model/case-document-command-result.ts
model/case-summarization-input.ts
model/case-summary-type.ts
model/chart-record.ts
model/chart-result-portal-response.ts
model/chart-result.ts
model/chart-type-dto-list-portal-response.ts
model/chart-type-dto.ts
model/chat-command-result-portal-response.ts
model/chat-command-result.ts
model/chat-history-item.ts
model/classification-and-sub-classification-lookup-result-portal-response.ts
model/classification-and-sub-classification-lookup-result.ts
model/command-result-portal-response.ts
model/command-result.ts
model/comment-history-item.ts
model/comment-input.ts
model/comment-result-portal-response.ts
model/comment-result.ts
model/contact-information.ts
model/create-case-feedback-input.ts
model/create-case-summary-user-update-input.ts
model/create-classification-feedback-input.ts
model/create-document-summarize-input.ts
model/create-user-with-profile-input.ts
model/create-whitelist-user-input.ts
model/e-searching-type.ts
model/favorite-input.ts
model/favorite-item.ts
model/favorite-result-portal-response.ts
model/favorite-result.ts
model/favorite-type-enum.ts
model/general-summary-result-portal-response.ts
model/general-summary-result.ts
model/highlight-input.ts
model/highlight-item.ts
model/highlight-result-portal-response.ts
model/highlight-result.ts
model/highlight-section-enum.ts
model/internal-user-profile.ts
model/irac-case-summarization-input.ts
model/irac-summary-result-portal-response.ts
model/irac-summary-result.ts
model/keywords.ts
model/law-data-by-file.ts
model/law-data-by-question.ts
model/law-document-command-result-portal-response.ts
model/law-document-command-result.ts
model/lookup-details-result.ts
model/message.ts
model/models.ts
model/named-entities.ts
model/permission-group.ts
model/permission.ts
model/person-full-name.ts
model/portal-response-error.ts
model/portal-response.ts
model/role.ts
model/search-by-file-command-input.ts
model/search-by-file-command-result-portal-response.ts
model/search-by-file-command-result.ts
model/search-by-question-command-input.ts
model/search-by-question-command-result-portal-response.ts
model/search-by-question-command-result.ts
model/search-in-case-by-file-result.ts
model/search-in-case-by-question-result.ts
model/search-in-law-by-file-result.ts
model/search-in-law-by-question-result.ts
model/search-source.ts
model/search-type.ts
model/semantic-chat-input.ts
model/semantic-extract-result-portal-response.ts
model/semantic-extract-result.ts
model/sso-user.ts
model/sub-classification-details-result.ts
model/summary-type-enum.ts
model/update-case-summary-user-update-input.ts
model/update-whitelist-user-input.ts
model/update-whitelist-user-status-input.ts
model/user-case-history-result-portal-response.ts
model/user-case-history-result.ts
model/user-case-history.ts
model/user-details-result-portal-response.ts
model/user-details-result.ts
model/user-document-summaries-list-query-result-paged-result-portal-response.ts
model/user-document-summaries-list-query-result-paged-result.ts
model/user-document-summaries-list-query-result.ts
model/user-profile-details.ts
model/user-profile-result-portal-response.ts
model/user-profile-result.ts
model/user-profile-view-result-portal-response.ts
model/user-profile-view-result.ts
model/user-profile.ts
model/user.ts
model/users-list-query-input.ts
model/users-list-query-result-paged-result-portal-response.ts
model/users-list-query-result-paged-result.ts
model/users-list-query-result.ts
model/users-whitelist.ts
param.ts
variables.ts
