import { Component, Input, OnInit, inject } from "@angular/core";
import { SearchEvents } from "../../../shared/events/search.events";
import { BASE_PATH } from "../../../services";
import { Observable } from "rxjs";
import { HttpClient } from "@angular/common/http";

@Component({ template: '' })
export abstract class RegularSearchBaseResult<TData> implements OnInit {

    protected data!: TData;
    protected readonly _basePath = inject(BASE_PATH);
    protected readonly _httpClient = inject(HttpClient);
    protected onInit(): void { }

    @Input() searchText!: string;
    protected name: string = 'name';

    ngOnInit(): void {
        this.loadData();
        this.onInit();
    }

    protected loadData() {
        if (!this.searchText) {
            return;
        }

        this.getDataObservable().subscribe(data => this.data = data);
    }

    protected abstract getDataObservable(): Observable<TData>;

}