import { NgModule } from '@angular/core';
import { CommonModule, DecimalPipe } from '@angular/common';
import { SearchComponent } from './search.component';
import { SplitterModule } from 'primeng/splitter';
import { AngularSvgIconModule } from 'angular-svg-icon';
import { TableModule } from 'primeng/table';
import { RouterModule } from '@angular/router';
import { semantic_search_components } from './semantic-search/public.api';
import { regular_search_component } from './regular-search/public.api';
import { RegularSearchResultComponent } from './regular-search/regular-search-result/regular-search-result.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SemanticSearchLandingComponent } from './semantic-search/semantic-search-landing/semantic-search-landing.component';
import { ExtractInfoComponent } from '../extract/extract-info/extract-info.component';
import { SearchResultListComponent } from './semantic-search/search-result-list/search-result-list.component';
import { SearchQuestionResultListComponent } from './semantic-search/search-question-result-list/search-question-result-list.component';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { DialogModule } from 'primeng/dialog';
import { CaseDetailsViewerComponent } from './semantic-search/search-result-list/case-details-viewer/case-details-viewer.component';
import { LawDetailsViewerComponent } from './semantic-search/search-result-list/law-details-viewer/law-details-viewer.component';
import { TruncateWordsPipe } from '../../shared/pipes/truncate-words.pipe';
import { DateRtlPipe } from '../../shared/pipes/date-rtl.pipe';
import { SemanticSearchLandingFileComponent } from './semantic-search/semantic-search-landing/semantic-search-landing-file/semantic-search-landing-file.component';
import { SemanticSearchLandingTextComponent } from './semantic-search/semantic-search-landing/semantic-search-landing-text/semantic-search-landing-text.component';
import { ButtonModule } from 'primeng/button';
import { SliderModule } from 'primeng/slider';
import { ChatPopupComponent } from '../chat-popup/chat-popup.component';
import { DropdownModule } from 'primeng/dropdown';
import { ReusableBarChartComponent } from '../../reusable-bar-chart/reusable-bar-chart.component';
import { HighlightSectionComponent } from '../highlight/highlight-section.component';
import { CommentSharedComponent } from '../comments-shared/comments-shared.component';

@NgModule({
  declarations: [
    ...semantic_search_components,
    ...regular_search_component,
    SearchResultListComponent,
    SearchQuestionResultListComponent,
    CaseDetailsViewerComponent,
    LawDetailsViewerComponent,
    // MarkdownPipe
  ],
  providers: [DecimalPipe],
  imports: [
    CommonModule,
    SplitterModule,
    FormsModule,
    ReactiveFormsModule,
    AngularSvgIconModule,
    TableModule,
    ReactiveFormsModule,
    HighlightSectionComponent,
    CommentSharedComponent,
    ButtonModule,
    DialogModule,
    SliderModule,
    DropdownModule,
    ChatPopupComponent,
    ReusableBarChartComponent,
    RouterModule.forChild([
      {
        path: 'semantic',
        component: SemanticSearchLandingComponent,
      },

      {
        path: 'semantic/text',
        component: SemanticSearchLandingTextComponent,
      },
      {
        path: 'semantic/result',
        component: ExtractInfoComponent,
      },
      {
        path: 'semantic/question-result',
        data: { searchType: 'question' },
        component: SearchQuestionResultListComponent,
      },
      {
        path: 'semantic/file-result',
        data: { searchType: 'file' },
        component: SearchResultListComponent,
      },
      {
        path: ':searchType',
        component: SearchComponent,
      },
    ]),
    OverlayPanelModule,
    TruncateWordsPipe,
    DateRtlPipe,
  ],
  exports: [
    SearchResultListComponent,
    CaseDetailsViewerComponent,
    LawDetailsViewerComponent,
  ],
})
export class SearchModule { }
