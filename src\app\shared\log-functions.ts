let isProduction = false;

export function SetDevLogProduction(isProd: boolean) {
    isProduction = isProd;
}

function printLog(message: any, _function: Function, color?: string | undefined, borderColor?: string | undefined, additionalStyle?: string) {
    if (isProduction)
        return;

    if (typeof message == 'object') {
        _function.call(0, JSON.stringify(message, null, 2));
        return;
    }
    const _padding = "padding:3px 5px;";
    const _fontSize = "font-size:15px;";
    const _border = borderColor ? `border:1px solid ${borderColor};` : '';
    const _color = color ? `background:${color};` : '';
    _function.call(0, "%c" + message, `${_color} ${_fontSize} ${_padding} ${_border}`);

}

function Log(message?: any): void {
    printLog(message, console.log, "blue");
}

function Info(message?: any): void {
    printLog(message, console.info, "green");
}

function Warning(message?: any): void {
    printLog(message, console.warn, undefined, 'yellow');
}

function Error(message?: any): void {
    printLog(message, console.error, undefined, 'red');
}

function Debug(message?: any, ...optionalParams: any[]): void {
    console.debug(message, ...optionalParams);
}

function Table(messsage: { [label: string]: string | any }): void {
    if (isProduction) return;
    Object.assign(messsage, { 'Log Timestamp': new Date().toLocaleString() });
    console.table(messsage);
}

const DevLog = {
    Log, Error, Warning, Debug, Table, Info
}

export { DevLog }
