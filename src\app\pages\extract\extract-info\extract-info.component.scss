.search-icon {
  position: relative;
  top: 4px;
  left: 30px;
  cursor: pointer;
}

::ng-deep {
  .p-inputtext.p-inputtext-sm {
    font-size: 0.875rem;
    width: 19rem;
  }
}
.accordion-button:not(.collapsed) {
  background-color: #fff;
}
.accordion-button {
  box-shadow: none;
}
.nav-btn.active {
  background-color: var(--backgroundColorPrimaryDark);
  color: whitesmoke;
}

.nav-btn {
  color: white;
  line-height: 2;
  border-radius: 5rem;
  min-width: 5rem;
  background-color: rgba(72, 131, 113, 0.5);
}

.btn-xs {
  padding: 2px 6px;
  font-size: 0.75rem;
}

/* File upload result styles */
.file-upload-result {
  width: 100%;
}

.file-card {
  background-color: #fff;
  border-radius: 16px;
  width: 400px;
  height: 90px;
  padding: 15px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.file-label {
  font-weight: 500;
  color: #2e3033;
  font-size: 14px;
}

.file-name-container {
  margin-top: 10px;
}

.file-name {
  color: #101214;
  font-size: 14px;
}

.file-check {
  margin-right: 10px;
  display: flex;
}

.classification-info {
  padding: 15px;
  margin-top: 15px;
  color: #6b7280;
  font-size: 16px;
}
.leading-text {
  color: #212529;
  font-size: 16px;
  font-weight: 700;
}

/* New Search Bar Styles */
.search-container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 15px;
}

.search-bar {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid var(--color-grey-91, #e5e7eb);
  overflow: hidden;
}

.search-input {
  flex: 1;
  height: 50px;
  border: none;

  font-size: 16px;
  background: transparent;
  color: #333;
  text-align: right;
  border-radius: 50px;
  width: 100%;
}

.search-input:focus {
  outline: none;
  box-shadow: none;
}

.search-button {
  position: absolute;
  left: 0px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  background: #365a4e;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 62px;
  height: 49px;
}

.search-button:hover {
  color: #eaf1ef;
}

.search-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

::placeholder {
  color: #999;
  opacity: 1;
}

/* Search Results Side Panel Styles */
.search-results-panel {
  position: fixed;
  top: 0;
  left: -40vw; /* Start off-screen */
  width: 40vw; /* 40% of viewport width */
  height: 100vh;
  background-color: #fff;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow-y: auto;
  padding: 20px 0;
  transition: left 0.3s ease; /* Add smooth transition */
}

.search-results-panel.show-panel {
  left: 0; /* Show panel */
}

/* Make the toggle button stay visible even when panel is hidden */
.panel-toggle-btn {
  position: fixed; /* Change from absolute to fixed */
  top: 50%;
  left: 0; /* Align to right edge of screen */
  transform: translateY(-50%);
  width: 31px;
  height: 139px;
  border-radius: 0 20px 20px 0; /* Rounded only on left side */
  background-color: white;
  border: 1px solid #ddd;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1001;
  transition: left 0.3s ease; /* Add transition to match panel */
}

/* Move button when panel is visible */
.search-results-panel.show-panel ~ .panel-toggle-btn,
.search-results-panel.show-panel .panel-toggle-btn {
  left: 40vw; /* Move button to right edge of panel */
}

.search-results-header {
  padding: 0 20px 15px;
  border-bottom: 1px solid #eee;
}

.search-tabs {
  display: flex;
  background-color: #f5f5f5;
  border-radius: 30px;
  padding: 5px;
}

.search-tab-btn {
  flex: 1;
  padding: 10px 15px;
  border: none;
  background: none;
  border-radius: 25px;
  font-weight: 600;
  font-size: 14px;
  color: #666;
  text-align: center;
  cursor: pointer;
}

.search-tab-btn.active {
  background-color: #2e6b59;
  color: white;
}

.search-results-count {
  padding: 20px;
  color: #555;
  font-size: 14px;
  line-height: 1.5;
}

.search-results-list {
  padding: 0 20px;
}

.search-result-item {
  background-color: #fff;
  border-radius: 12px;
  border: 1px solid #eee;
  margin-bottom: 20px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.result-status {
  padding: 10px 15px;
}

.status-badge {
  display: inline-block;
  background-color: #f0b400;
  color: white;
  padding: 5px 15px;
  border-radius: 5px;
  font-weight: 600;
  font-size: 14px;
}

.result-content {
  padding: 0 15px 15px;
}

.result-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  text-align: right;
  margin-bottom: 15px;
}

.result-details {
  margin-bottom: 20px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.detail-label {
  font-weight: 600;
  color: #555;
  font-size: 14px;
}

.detail-value {
  color: #333;
  font-size: 14px;
  text-align: left;
}

.result-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
}

.similarity-rate {
  display: flex;
  align-items: center;
}

.dots {
  color: #999;
  margin-right: 5px;
}

.rate-text {
  font-size: 14px;
  color: #555;
}

.details-btn {
  background-color: #2e6b59;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 8px 15px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.details-btn:hover {
  background-color: #235446;
}

/* Responsive styles */
@media (max-width: 576px) {
  .search-results-panel {
    width: 100%;
    right: -100%;
  }

  .search-results-panel.show-panel ~ .panel-toggle-btn,
  .search-results-panel.show-panel .panel-toggle-btn {
    right: 100%; /* Move button to left edge of panel on mobile */
  }
}

/* Add styles for the details panel */

.details-header {
  text-align: end;
}

.details-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.details-content {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.message-title {
  font-weight: 600;
  font-size: 16px;
}

.message-badge {
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.message-body {
  margin-bottom: 15px;
}

.message-section {
  margin-bottom: 15px;
}

.section-title {
  font-size: 14px;
  font-weight: 700;
  margin-bottom: 5px;
  color: #6b7280;
}

.section-content {
  font-size: 14px;
  line-height: 1.5;
  color: #212529;
  white-space: pre-line;
}

.section-details {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.detail-item {
  font-size: 14px;
  color: #495057;
}

.detail-label {
  font-weight: 700;
  margin-right: 5px;
  color: #6b7280;
}

.message-actions {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  background: none;
  border: 1px solid #007bff;
  color: #007bff;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.action-btn:hover {
  background-color: #007bff;
  color: white;
}

.view-btn {
  background-color: #007bff;
  color: white;
}

.view-btn:hover {
  background-color: #0069d9;
}

/* Responsive styles */
@media (max-width: 576px) {
  .details-panel {
    width: 90%;
    right: 5%;
    bottom: 10px;
  }
}

/* Law Details Card Styling */
.law-details-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  width: 100%;
  max-width: 600px;
  margin-right: auto;
  direction: rtl;
}

.law-details-header {
  padding: 16px 20px;
  border-bottom: 1px solid #eaeaea;
}

.law-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  text-align: right;
}

.law-dates {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 14px;
}

.date-item {
  display: flex;
  flex-direction: column;
}

.date-label {
  color: #666;
  margin-bottom: 4px;
}

.date-value {
  color: #333;
  font-weight: 500;
}

.date-separator {
  height: 20px;
  width: 1px;
  background-color: #ddd;
}

.law-content {
  padding: 20px;
  background-color: #f9f9f9;
}

.law-section {
  margin-bottom: 16px;
  text-align: right;
}

.law-section:last-child {
  margin-bottom: 0;
}

.section-label {
  color: #666;
  margin-bottom: 4px;
  font-weight: 500;
}

.section-value {
  color: #333;
  font-weight: 600;
  font-size: 16px;
}

.law-footer {
  padding: 16px 20px;
  border-top: 1px solid #eaeaea;
  display: flex;
  justify-content: flex-end;
}

.similarity-rate {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rate-label {
  color: #666;
  font-size: 14px;
}

.rate-value {
  color: #333;
  font-weight: 600;
  font-size: 18px;
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .law-details-card {
    max-width: 100%;
  }

  .law-dates {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .date-separator {
    display: none;
  }
}

/* Law Navigation Tabs */
.law-navigation-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
  direction: rtl;
}

.tab-button {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.tab-button:hover {
  background-color: #e9ecef;
}

.tab-button.active {
  background-color: #fff;
  border-color: #adb5bd;
  color: #212529;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .law-navigation-tabs {
    justify-content: center;
  }
}

/* Fixed height container with scrollable content */
.fixed-height-container {
  display: flex;
  flex-direction: column;
  /* Adjust 150px based on your header/footer height */

  margin-right: auto;
  position: relative;
}

/* Top section that stays fixed */
.fixed-top-section {
  flex-shrink: 0;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

/* Scrollable middle section */
.scrollable-content {
  flex-grow: 1;
  overflow-y: auto;
  padding: 15px 0;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 128, 0, 0.3) transparent;

  /* Custom scrollbar for WebKit browsers */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 128, 0, 0.3);
    border-radius: 3px;
  }

  /* Ensure content is pushed to the bottom */
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

/* Bottom section that stays fixed */
.fixed-bottom-section {
  flex-shrink: 0;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

/* Ensure the content container has proper width */
.content-container {
  width: 60%;
  margin-right: auto;
  padding: 1rem;
  line-height: 1.6;
  font-size: 1rem;
  direction: rtl;
  overflow-wrap: break-word;
  word-wrap: break-word;
  hyphens: auto;

  /* Ensure proper rendering of HTML content */
  ::ng-deep p {
    margin-bottom: 1rem;
  }

  ::ng-deep ul,
  ::ng-deep ol {
    padding-right: 1.5rem;
    margin-bottom: 1rem;
  }

  /* Responsive width adjustment */
  @media (max-width: 992px) {
    width: 80%;
  }

  @media (max-width: 576px) {
    width: 100%;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .fixed-height-container {
    height: calc(100vh - 120px); /* Smaller offset for mobile */
  }
}

.split-screen {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.bottom-half {
  flex: 1; // ⬅️ split 50%
  display: flex;
  flex-direction: column;
  background: #f9f9f9;
  border-top: 1px solid #ccc;
  padding: 0;
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

.messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.message {
  background: #e1e1e1;
  padding: 0.6rem 1rem;
  border-radius: 10px;
  max-width: 80%;
}

.chat-input {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-top: 1px solid #ddd;
  background: #fff;
  position: relative; // optional
  bottom: 0; // optional if already at bottom by layout
  width: 100%;
}
.search-input {
  flex: 1;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  border-radius: 6px;
  border: 1px solid #ccc;
  margin-inline-end: 0.5rem; // creates spacing between input and button
  box-sizing: border-box;
}

.search-button {
  padding: 0.5rem 1rem;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  white-space: nowrap; // prevent it from wrapping
  flex-shrink: 0;

  &:hover {
    background: #0056b3;
  }

  &:disabled {
    background: #999;
    cursor: not-allowed;
  }
}

.file-upload-result {
  display: flex;
  flex-direction: column;
  height: auto !important;
  min-height: 0 !important;
  overflow: visible !important;
}

.vertical-fade-scroll {
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  white-space: normal;
  position: relative;
  scroll-behavior: smooth;

  &::-webkit-scrollbar {
    width: 8px;
  }
  &::-webkit-scrollbar-thumb {
    background: rgba(54, 90, 78, 0.2);
    border-radius: 4px;
  }

  // Fade overlays
  // &::before,
  // &::after {
  //   content: "";
  //   position: absolute;
  //   left: 0;
  //   width: 100%;
  //   height: 40px;
  //   pointer-events: none;
  //   z-index: 2;
  //   transition: opacity 0.3s;
  // }
  // &::before {
  //   top: 0;
  //   background: linear-gradient(to bottom, #f4f4f6 80%, transparent 100%);
  //   opacity: 0.8;
  // }
  // &::after {
  //   bottom: 0;
  //   background: linear-gradient(to bottom, #f4f4f6 80%, transparent 100%);
  //   opacity: 0.8;
  // }
}

// Responsive: ensure fade overlays always cover the edge
@media (max-width: 768px) {
  .vertical-fade-scroll::before,
  .vertical-fade-scroll::after {
    height: 24px;
  }
}

/* Chat integration styles */
.bottom-half {
  flex: 1; // Takes 50% of the available height
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.full-width-chat {
  width: 100%;
  height: 100%;
  display: block;
}

/* Override chat-pot component styles for embedded use */
::ng-deep .full-width-chat {
  height: 100%;
  width: 100%;

  app-chat-pot {
    height: 100%;
    width: 100%;
    display: block;

    .chat-container {
      height: 100%;
      border: none !important;
      border-radius: 0 !important;
      box-shadow: none !important;

      &.embedded-chat {
        height: 100%;
        min-height: unset;
      }
    }

    .chat-header {
      display: none !important; // Hide the header in embedded mode
    }

    .embedded-chat-container {
      height: calc(100% - 80px) !important; // Adjust for input area
      background-color: transparent !important;
    }

    .chat-input-container {
      border-top: 1px solid #e9ecef !important;
      background-color: white !important;
    }
  }

  .chat-input-container {
    position: absolute;
    bottom: 0;
    width: 100%;
    background: white;
    padding: 10px;
    border-top: 1px solid #eee;
  }
}
