/**
 * MOSAED.Internal.Api
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { Role } from './role';
import { UserProfile } from './user-profile';
import { PermissionGroup } from './permission-group';


export interface Permission { 
    code: number;
    name?: string | null;
    nameAr?: string | null;
    createdAt: string;
    updatedBy?: number | null;
    updatedByUser: UserProfile;
    updatedAt?: string | null;
    roleCode: number;
    role: Role;
    isActive: boolean;
    groupCode: number;
    group: PermissionGroup;
}

