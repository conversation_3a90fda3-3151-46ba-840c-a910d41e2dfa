.welcome-page {
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url("/assets/images/bg-1.svg"),
    url("/assets/images/bg-2.svg");
  background-position: center top, center bottom;

  padding: 40px 0;
  background-repeat: no-repeat, no-repeat;
}

.welcome-content {
  max-width: 900px;
  margin: 0 auto;
  padding: 40px 20px;
}

.welcome-title {
  color: #365a4e;
  font-size: 47.45px;
  font-weight: 500;
}

.welcome-subtitle {
  color: #0a022e;
  font-size: 47.45px;
  font-weight: 500;
}

.welcome-description {
  font-size: 21.09px;
  color: #0a022e;
  margin-left: auto;
  margin-right: auto;
}

.option-title {
  font-size: 1.1rem;
  color: #333;
}

.option-card {
  background-color: #fff;
  border-radius: 10px;
  border-radius: 16px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #d7d8db;
}

.option-icon {
  font-size: 2rem;
  color: #2d4f43;
  background-color: rgba(45, 79, 67, 0.1);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.option-card-title {
  font-weight: 600;
  color: #333;
}

.option-card-text {
  color: #666;
  font-size: 0.95rem;
}

.btn-outline-primary {
  color: #2d4f43;
  border: 1px solid #d2d6db;
  padding: 8px 16px;
  border-radius: 4px;
}

.btn-outline-primary:hover {
  background-color: #2d4f43;
  color: #fff;
}
