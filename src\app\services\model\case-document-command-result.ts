/**
 * MOSAED.Internal.Api
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { Keywords } from './keywords';
import { NamedEntities } from './named-entities';


export interface CaseDocumentCommandResult { 
    id?: number | null;
    refId?: string | null;
    caseId?: string | null;
    category?: string | null;
    subCategory?: string | null;
    verdict?: string | null;
    rules?: Array<string> | null;
    summary?: string | null;
    auditVerdictNumber?: string | null;
    auditVerdictDate?: string | null;
    verdictDate?: string | null;
    namedEntities: NamedEntities;
    keywords: Keywords;
    groupedNamedEnities?: Array<string> | null;
    groupedKeywords?: Array<string> | null;
    cleanTxt?: string | null;
    court?: string | null;
    typeOfSentence?: string | null;
    briefSentence?: string | null;
    actions?: string | null;
    reasons?: string | null;
    additionalByLlm?: Array<string> | null;
}

