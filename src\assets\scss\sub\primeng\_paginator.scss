// ---------- paginator

body .p-paginator {
  padding: 1rem 0;
  border-bottom: 0;
  border-radius: 2px;
  .p-dropdown .p-dropdown-label {
    padding: 0.5rem;
  }
}

body .p-paginator .p-paginator-first,
body .p-paginator .p-paginator-first,
body .p-paginator .p-paginator-prev,
body .p-paginator .p-paginator-next,
body .p-paginator .p-paginator-last {
  @extend .flip-ltr;
}
.page-link {
  background-color: transparent;
  border: 1px solid #9a9a9a40;
}
.p-paginator-prev,
.p-paginator-first,
.p-paginator-next,
.p-paginator-last {
  span {
    [dir="ltr"] & {
      transform: scaleX(-1);
    }
  }
}

.p-paginator .p-paginator-pages .p-paginator-page.p-highlight {
  color: var(--primary);
  background: var(--green-100);
}
