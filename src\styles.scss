// @import "./scss/style";
@import "../src/assets/scss/style.scss";
@import "primeicons/primeicons.css";

main {
  min-height: 70vh;
}

body {
  min-height: 97vh;
}

/* For WebKit browsers */
.scroll-lg::-webkit-scrollbar {
  width: 0; /* Hide scrollbar for vertical */
  height: 0; /* Hide scrollbar for horizontal */
}

/* For Firefox */
.scroll-lg {
  scrollbar-width: thin; /* Show a thin scrollbar */
}

/* For IE and Edge */
.scroll-lg {
  -ms-overflow-style: auto; /* Default scrollbar style */
}

.scroll-lg {
  overflow-y: auto; /* Show scrollbar when content overflows vertically */
  overflow-x: hidden; /* Show scrollbar when content overflows horizontally */
  height: 90vh; /* Example max-height to demonstrate overflow */
  width: 100%; /* Example max-width to demonstrate overflow */
}

html[dir="rtl"] {
  .p-datatable .p-datatable-tbody > tr > td {
    text-align: right !important;
  }
}

/* fix primeng icons */
.pi {
  vertical-align: middle;
  padding-bottom: 0.2rem;
}

.p-overlaypanel .p-overlaypanel-content {
  padding: 0.5rem 0.1rem !important;
}

.dropdown-overlay-container {
  padding: 1rem;
  max-height: 30rem;
  max-width: 30rem;
  overflow-y: scroll;
  overflow-x: hidden;
}

.text-justify {
  text-align: justify;
}


.edit-button {
  background-color: $bg-edit-Button;
  color: $primary;
  border: 1px solid $primary;
  border-radius: 6px;
  font-weight: 500;


  &:hover{
  background-color: $bg-edit-Button-hover;
  color: $primary-dark;
  border-color: $primary-dark;
  }

}

.p-overlaypanel .p-overlaypanel-content {
  padding: 0px !important;
}


.summary-text {
  display: -webkit-box;
  display: box;
  -webkit-box-orient: vertical;
  box-orient: vertical;
  -webkit-line-clamp: 3;
  line-clamp: 3; /* Standard syntax */
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5em;
  max-height: 4.5em;
  padding-left: 20px;
}

.markdown-preview {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;

  // Clamp to 4 full lines
  -webkit-line-clamp: 4;
  line-clamp: 4;

  // Enforce exact height to match 4 lines
  line-height: 1.5em;
  max-height: calc(1.5em * 4); // 6em
  height: calc(1.5em * 4);

  // Optional: ellipsis (may not show on multi-line truncation)
  text-overflow: ellipsis;

  // Important: remove padding/margins that could break height
  padding: 0;
  margin: 0;

  // Normalize markdown styles inside (remove excess spacing)
  * {
    margin: 0 !important;
    padding: 0 !important;
    line-height: inherit !important;
  }

  direction: rtl;
}


