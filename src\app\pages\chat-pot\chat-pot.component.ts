import {
  Component,
  Input,
  ViewChild,
  ElementRef,
  ViewEncapsulation,
  AfterViewChecked,
  OnInit,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { OverlayPanel, OverlayPanelModule } from 'primeng/overlaypanel';

import {
  ChatHistoryItem,
  ChatService,
  SemanticChatInput,
} from '../../services';
import { CaseData } from '../../services/model/case-data';
import { FormsModule } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';
import { MarkdownPipe } from '../../markdown.pipe';

@Component({
  selector: 'app-chat-pot',
  standalone: true,
  templateUrl: './chat-pot.component.html',
  styleUrls: ['./chat-pot.component.scss'],
  encapsulation: ViewEncapsulation.None, // Ensures styles are not encapsulated
  imports: [
    CommonModule,
    FormsModule,
    OverlayPanelModule,
    InputTextModule,
    ButtonModule,
    MarkdownPipe,
  ],
})
export class ChatPotComponent implements OnInit, AfterViewChecked, OnChanges {
  @Input() selectedCase!: CaseData;
  @Input() embedded: boolean = false; // New property to check if component is embedded

  chatHistory: any[] = [];
  groupedChatHistory: { label: string; entries: ChatHistoryItem[] }[] = [];

  @ViewChild('chatOverlay') chatOverlay!: OverlayPanel;
  @ViewChild('scrollContainer') scrollContainer!: ElementRef;

  newMessage = '';
  messages: { sender: string; text: string }[] = [];
  isOverlayVisible: boolean = false;

  constructor(private chatService: ChatService) {}

  private shouldScroll = true;

  ngOnInit() {
    this.queryChat();

    // Don't automatically show overlay - let user trigger it
    // The overlay will be shown when user focuses on input or starts typing
  }

  ngOnChanges(changes: SimpleChanges) {
    // When selectedCase changes, refresh the chat
    if (changes['selectedCase'] && !changes['selectedCase'].firstChange) {
      this.queryChat();

      // Reset overlay visibility when case changes
      this.isOverlayVisible = false;
    }
  }

  toggle(event: Event) {
    if (this.embedded) {
      // If embedded, always show
      this.isOverlayVisible = true;
      if (this.chatOverlay) {
        this.chatOverlay.show(null, document.body);
      }
    } else {
      // Normal toggle behavior for non-embedded use
      this.isOverlayVisible = !this.isOverlayVisible;
      this.chatOverlay.toggle(event);
    }
    this.shouldScroll = true;
  }

  onOverlayShow() {
    this.isOverlayVisible = true;
    this.shouldScroll = true;
  }

  onOverlayHide() {
    if (!this.embedded) {
      this.isOverlayVisible = false;
    } else {
      // If embedded, prevent hiding
      setTimeout(() => {
        if (this.chatOverlay) {
          this.chatOverlay.show(null, document.body);
        }
      }, 0);
    }
  }

  deleteMessages(): void {
    if (!this.selectedCase) {
      return;
    }
    const input: SemanticChatInput = {
      caseId: this.selectedCase.caseId.toString(),
    };
    this.chatService.deleteChat(input).subscribe({
      next: (response) => {
        let responseString = response.data.response?.toString() || '';
        this.messages = [];
        this.chatHistory = [];

        // this.chatHistory = response.data?.chatHistory ?? [];
        this.newMessage = '';
        this.scrollToBottom();
      },
      error: (err) => {
        console.error('Error querying chat:', err);
      },
    });
  }
  clearMessages() {
    this.messages = [];
    this.chatHistory = [];
    this.groupedChatHistory = [];
  }

  ngAfterViewChecked() {
    if (this.shouldScroll) {
      this.scrollToBottom();
      this.shouldScroll = false;
    }
  }

  // sendMessage(): void {
  //   if (!this.newMessage.trim()) {
  //     return;
  //   }
  //   this.messages.push({ sender: 'user', text: this.newMessage });
  //   this.messages.push({
  //     sender: 'system',
  //     text: 'هذه إجابة الذكاء الاصطناعي ...'
  //   });
  //   this.shouldScroll = true;
  //   this.queryChat();
  // }
  private formatDate(date: Date): string {
    const mm = String(date.getMonth() + 1).padStart(2, '0');
    const dd = String(date.getDate()).padStart(2, '0');
    const yyyy = date.getFullYear();
    return `${mm}/${dd}/${yyyy}`;
  }
  sendMessage(): void {
    const text = this.newMessage.trim();
    if (!text) return;

    // 1) Push user message into chatHistory
    this.chatHistory.push({
      role: 'user',
      content: text,
      creationDate: this.formatDate(new Date()),
    });

    // 2) Push placeholder system message
    const placeholderIndex = this.chatHistory.length;
    this.chatHistory.push({
      role: 'assistant',
      content: 'هذه إجابة الذكاء الاصطناعي ...',
      creationDate: this.formatDate(new Date()),
    });

    // clear input + scroll
    this.newMessage = '';
    this.shouldScroll = true;
    this.groupChatHistory();

    // 3) Fire the real API call
    const input: SemanticChatInput = {
      document: this.selectedCase!.cleanTxt ?? '',
      messages: text,
      chatHistory: this.chatHistory,
      caseId: this.selectedCase!.caseId.toString(),
      refId: this.selectedCase.refId,
    };
    this.chatService.getChat(input).subscribe({
      next: (resp) => {
        const realText = resp.data.response?.toString() || '';

        // 4) Overwrite placeholder
        this.chatHistory[placeholderIndex] = {
          role: 'assistant',
          content: realText,
          creationDate: this.formatDate(new Date()),
        };

        // 5) Append any server‐returned history items
        if (resp.data.chatHistory?.length) {
          // this.chatHistory.push(...resp.data.chatHistory);

          this.chatHistory = [
            ...resp.data.chatHistory, // entire fresh log
            // if you still need a trailing placeholder for streaming, add it here
          ];
        }

        // re‐group & scroll again
        this.groupChatHistory();
        this.shouldScroll = true;
      },
      error: (err) => console.error(err),
    });
  }

  queryChat() {
    if (!this.selectedCase) return;

    const input: SemanticChatInput = {
      document: this.selectedCase.cleanTxt ?? '',
      messages: this.newMessage,
      chatHistory: this.chatHistory,
      refId: this.selectedCase.refId,
    };

    this.chatService.getChat(input).subscribe({
      next: (response) => {
        const responseString = response.data.response?.toString() || '';
        this.messages.push({ sender: 'assistant', text: responseString });

        if (response.data?.chatHistory?.length) {
          this.chatHistory.push(...response.data.chatHistory);
        }

        this.newMessage = '';
        this.groupChatHistory();
        this.shouldScroll = true;
      },
      error: (err) => {
        console.error('Error querying chat:', err);
      },
    });
  }

  private scrollToBottom(): void {
    if (!this.scrollContainer || !this.scrollContainer.nativeElement) return;

    try {
      const el = this.scrollContainer.nativeElement;
      el.scrollTop = el.scrollHeight;
    } catch (err) {
      console.warn('Scroll to bottom failed:', err);
    }
  }

  groupChatHistory() {
    const now = new Date();
    const grouped: { [key: string]: ChatHistoryItem[] } = {
      Older: [],
      'Last 7 Days': [],
      Yesterday: [],
      Today: [],
    };

    for (const entry of this.chatHistory) {
      if (!entry.creationDate) continue;
      const date = new Date(entry.creationDate + 'Z');
      const diffTime = now.getTime() - date.getTime();
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays === 0) {
        grouped['Today'].push(entry);
      } else if (diffDays === 1) {
        grouped['Yesterday'].push(entry);
      } else if (diffDays <= 7) {
        grouped['Last 7 Days'].push(entry);
      } else {
        grouped['Older'].push(entry);
      }
    }

    this.groupedChatHistory = Object.entries(grouped)
      .filter(([_, entries]) => entries.length > 0)
      .map(([label, entries]) => ({ label, entries }));
    this.scrollToBottom();
  }

  onInputFocus() {
    this.shouldScroll = true;
    // Show the overlay when input is focused
    if (!this.isOverlayVisible) {
      this.showOverlay();
    }
  }

  onInputChange() {
    // Show the overlay when user starts typing
    if (
      this.newMessage &&
      this.newMessage.length > 0 &&
      !this.isOverlayVisible
    ) {
      this.showOverlay();
    }
  }

  showOverlay() {
    this.isOverlayVisible = true;
    if (this.chatOverlay) {
      this.chatOverlay.show(null, document.body);
    }
  }
}
