import { inject } from "@angular/core";
import { BaseComponent, BaseTranslationKeys } from "../../shared/public.api";
import { UserManagementService } from "../../services";

export class UserManagementBase extends BaseComponent {

    protected readonly _userManagementService = inject(UserManagementService)

    /**
     *
     */
    constructor(componentName: string) {
        super(new BaseTranslationKeys('userManagement', componentName));

    }
}
