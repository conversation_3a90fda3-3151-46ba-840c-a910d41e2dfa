export function scrollToElementWithErrorMessage() {
  setTimeout(function () {
    const firstElementWithError = document.querySelector('.invalid-feedback')
    //('.ng-invalid:not(form):not(fieldset)');// ng-dirty ng-invalid
    if (firstElementWithError) {
      scrollToTargetAdjusted(firstElementWithError);
      // firstElementWithError.scrollIntoView({ top:'', behavior: 'smooth' });
    } else {
      const elementWithErrorMessage = document.querySelector('.text-danger');
      if (elementWithErrorMessage) {
        scrollToTargetAdjusted(elementWithErrorMessage);
        // elementWithErrorMessage.scrollIntoView({ behavior: 'smooth' });
      } else {
        const serverErrorMessage = document.querySelector('.p-message-error');
        if (serverErrorMessage) {
          // serverErrorMessage.scrollIntoView({ behavior: 'smooth' });
          scrollToTargetAdjusted(serverErrorMessage);
        }
      }
    }
  }, 100)
}


function scrollToTargetAdjusted(element: Element) {
  var headerOffset = 180;
  var elementPosition = element.getBoundingClientRect().top;
  var offsetPosition = elementPosition + window.pageYOffset - headerOffset;
  window.scrollTo({
    top: offsetPosition,
    behavior: "smooth"
  });
}

export function scrollToTop() {
  window.scrollTo({
    top: 0,
    left: 0,
    behavior: "smooth"
  });
}
