import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ReusableBarChartComponent } from './reusable-bar-chart.component';

describe('ReusableBarChartComponent', () => {
  let component: ReusableBarChartComponent;
  let fixture: ComponentFixture<ReusableBarChartComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReusableBarChartComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(ReusableBarChartComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
