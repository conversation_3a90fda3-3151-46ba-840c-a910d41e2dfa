import { LogLevel, OpenIdConfiguration, StsConfigHttpLoader } from "angular-auth-oidc-client";
import { of } from "rxjs";
import { AppConfigService } from "../../shared/public.api";

export function loadOpenIdConfiguration(appConfig: AppConfigService) {

    const config = appConfig.oidc;
    const scopes = (isMock: boolean): string => `openid profile ${isMock ? '' : ''}`;

    let authConfig: OpenIdConfiguration = {
        authority: config.authority,
        redirectUrl: window.location.origin,
        postLoginRoute: "login-callback",
        postLogoutRedirectUri: window.location.origin,
        renewUserInfoAfterTokenRenew: true,
        clientId: config.clientId,
        scope: scopes(config.isMock),
        responseType: 'code',
        silentRenew: true,
        //renewTimeBeforeTokenExpiresInSeconds: 30,
        useRefreshToken: true,
        logLevel: LogLevel.Debug,
    };
    return new StsConfigHttpLoader(of(authConfig));
}
