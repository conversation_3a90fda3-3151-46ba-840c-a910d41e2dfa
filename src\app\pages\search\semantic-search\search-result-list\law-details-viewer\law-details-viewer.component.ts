import { Component, Input, OnInit } from '@angular/core';
import { LawData } from "../../../../../services/model/law-data";
import { FavoriteService, FavoriteTypeEnum, HighlightItem, HighlightService } from '../../../../../services';

@Component({
  selector: 'app-law-details-viewer',
  templateUrl: './law-details-viewer.component.html',
  styleUrls: ['./law-details-viewer.component.css']
})
export class LawDetailsViewerComponent implements OnInit {
  @Input() selectedLaw!: LawData;
  value!: string;
  highlightHistory: HighlightItem[] = [];
  title: string = 'نص النظام و القانون';
  constructor(private favorite: FavoriteService, private highlightService: HighlightService) { }
  favoriteType = FavoriteTypeEnum;
  ngOnInit() {

    this.DoesItHaveFavorites();
    this.getHighlights();
  }

  updateContent(title: string) {
    this.title = title;
  }

  highlightfetched = false;
  getHighlights() {

    if (!this.selectedLaw?.refId) {
      // nothing to load, or show an error/toast
      return;
    }
    this.highlightService.getHighlightsByRefId(this.selectedLaw?.refId).subscribe({
      next: (response) => {
        if (response.data?.highlightItems && response.data?.highlightItems.length > 0) {
          this.highlightHistory = response.data.highlightItems ?? [];
        }
        this.highlightfetched = true;
        // setTimeout(() => this.applyServerHighlights(), 0);
      },
      error: (err) => {
        console.error('Error querying chat:', err);
      }
    });
  }

  DoesItHaveFavorites() {
    this.favorite.doesItHaveFavorites({
      refId: this.selectedLaw?.refId,
      typeId: FavoriteTypeEnum.Regulation
    }).subscribe({
      next: (result) => {
        this.loading = false;
        this.isFavorite = result.data.isFavorite!;
        // const matchedItem = this.searchResult.data.find((item: any) => item.id === selectedResult.id) as any;
        // if (matchedItem) {
        //   matchedItem.isFavorite = true;
        // }
        // this.selectedResult = response.data || null;

      },
      error: (err) => {
        this.loading = false;
        console.error('Error retrieving case details:', err);
      },
    });
  }

  removeFavorite() {
    this.favorite.removeFavorites({
      refId: this.selectedLaw?.refId,
      typeId: FavoriteTypeEnum.Regulation
    }).subscribe({
      next: () => {
        this.loading = false;
        this.isFavorite = false;
        // const matchedItem = this.searchResult.data.find((item: any) => item.id === selectedResult.id) as any;
        // if (matchedItem) {
        //   matchedItem.isFavorite = false;
        // }
      },
      error: (err) => {
        this.loading = false;
        console.error('Error retrieving case details:', err);
      },
    });
  }

  loading = false;
  isFavorite = false;
  addToFavorite() {
    this.favorite.storeFavorite({
      refId: this.selectedLaw?.refId,
      typeId: FavoriteTypeEnum.Regulation,
      summary: this.selectedLaw?.systemBrief
    }).subscribe({
      next: () => {
        this.loading = false;
        this.isFavorite = true;
        // const matchedItem = this.searchResult.data.find((item: any) => item.id === selectedResult.id) as any;
        // if (matchedItem) {
        //   matchedItem.isFavorite = true;
        // }
        // this.selectedResult = response.data || null;

      },
      error: (err) => {
        this.loading = false;
        console.error('Error retrieving case details:', err);
      },
    });
  }

}
