{"version": "2.0.0", "tasks": [{"type": "npm", "script": "start", "problemMatcher": [], "label": "npm: start", "detail": "ng serve"}, {"type": "npm", "script": "generate-services", "problemMatcher": [], "label": "npm: generate-services", "detail": "npx @openapitools/openapi-generator-cli generate -i http://localhost:5294/swagger/v1/swagger.json -g typescript-angular -o src/app/services --skip-validate-spec -c src/openapi-config.json"}, {"type": "npm", "script": "build", "group": "build", "problemMatcher": [], "label": "npm: build", "detail": "ng build"}]}