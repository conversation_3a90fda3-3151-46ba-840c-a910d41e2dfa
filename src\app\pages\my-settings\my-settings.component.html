<main class="pt-1">
  <div class="container-xxl">
    <h3 class="text-primary">الإعدادت</h3>
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb pt-1 pb-3">
        <li class="breadcrumb-item"><a [routerLink]="['/']">الرئيسية</a></li>
        <li class="breadcrumb-item active" aria-current="page">
          <b>
            الإعدادت
          </b>
        </li>
      </ol>
    </nav>
    <div class="row">
      <div class="col-lg-3 wow animate__animated animate__fadeIn">
        <div class="d-block sticky-top card border-0 shadow-sm hove rounded-4">
          <div class="card-body">
            <ul class="nav list-group nav-pills flex-column">
              <li class="nav-item py-1">
                <a class="nav-link list-group-item-action" href="javascript:void(0)"
                  (click)="selectedSection = 'personal_information'"
                  [ngClass]="{'active':selectedSection == 'personal_information'}">
                  <i class="pi pi-id-card pe-1"></i>
                  ملف المستخدم

                </a>
              </li>
              <!-- <li class="nav-item py-1">
                <a class="nav-link list-group-item-action" aria-current="try" href="javascript:void(0)"
                  (click)="selectedSection = 'favorite'" [ngClass]="{'active':selectedSection == 'favorite'}">
                  <i class="pi pi-bookmark pe-1"></i>
                  المفضلة
                </a>
              </li> -->
              <!-- <li class="nav-item py-1">
                <a class="nav-link list-group-item-action" href="javascript:void(0)"
                  (click)="selectedSection = 'comments'" [ngClass]="{'active':selectedSection == 'comments'}">
                  <i class="pi pi-book pe-1"></i>
                  التعليقات
                </a>
              </li> -->
              <!-- <li class="nav-item py-1">
                <a class="nav-link list-group-item-action" href="javascript:void(0)"
                  (click)="selectedSection = 'activities'" [ngClass]="{'active':selectedSection == 'activities'}">
                  <i class="pi pi-book pe-1"></i>
                  أنشطتي
                </a>
              </li> -->
              <li class="nav-item py-1">
                <a class="nav-link list-group-item-action active" href="javascript:void(0)"
                  (click)="selectedSection = 'account_setting'"
                  [ngClass]="{'active':selectedSection == 'account_setting'}">
                  <i class="pi pi-cog pe-1"></i>
                  إعدادات الحساب
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="col-lg-9 d-none d-lg-block wow animate__animated animate__fadeIn">
        <div class="d-block card border-0 shadow-sm rounded-4 main_height">
          <div class="card-body">

            @switch (selectedSection) {
            @case ("account_setting") {
            <div class="wow animate__animated animate__fadeIn">
              <app-account-settings />
            </div>
            }
            <!-- @case ("favorite") {
            <div class="wow animate__animated animate__fadeIn">
              <app-favorite />
            </div>
            } -->
            <!-- @case ("comments") {
            <div class="wow animate__animated animate__fadeIn">
              <app-comments />
            </div>
            } -->
            <!-- @case ("activities") {
              <div class="wow animate__animated animate__fadeIn">
                <app-my-activities />
              </div>
              } -->
            @case ("personal_information") {
            <div class="wow animate__animated animate__fadeIn">
              <app-personal-information />
            </div>
            }
            }

          </div>
        </div>
      </div>
    </div>
  </div>
</main>
