import {Component, Input, OnChanges, OnInit, SimpleChanges} from '@angular/core';
import { SearchEvents } from '../../../../shared/events/search.events';
// import { SearchFeaturesPublicDataInner } from '../../../../services';

@Component({
  selector: 'app-search-case-details',
  templateUrl: './search-case-details.component.html',
  styleUrls: ['./search-case-details.component.css']
})
export class SearchCaseDetailsComponent implements OnInit, OnChanges {
  @Input() caseDetails: any;
  protected data!: any;
  protected title: string = 'نص القضية';
  protected content: string = '';
  value: string | undefined;
  nerPredictWords: string[] = [];
  nerList: string[] = [];
  constructor() { }

  updateContent(title: string, content: string) {
    this.title = title;
    this.content = content;
  }
  ngOnInit() {
    if (this.caseDetails) {
      this.content = this.caseDetails.text;
      this.nerList = this.caseDetails.namedEntities;
      this.nerPredictWords = this.caseDetails.keywords;
    }

    SearchEvents.SearchCaseSelected.getListener()
      .subscribe(selectedCase => {
        this.data = selectedCase!;
      });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['caseDetails'] && changes['caseDetails'].currentValue) {
      this.content = this.caseDetails.text;
    }
  }

  getKeyWords() {

    // Fix the JSON by adding the missing closing brace and square bracket
    // const fixedJson = this.data.keywords + '}]}';

    // // Parse the fixed JSON string into a JavaScript object
    // let parsedData;
    // try {
    //   parsedData = JSON.parse(fixedJson);
    //   console.log(parsedData);
    // } catch (error) {
    //   console.error('Failed to parse JSON:', error);
    // }

    return this.caseDetails?.keywords || [];
  }

}
