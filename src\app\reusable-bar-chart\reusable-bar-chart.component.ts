import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ChartOptions, ChartType, ChartDataset, ChartData, Chart, CategoryScale, LinearScale, BarController, BarElement, Tooltip, Legend } from 'chart.js';
import { ChartRecord, ChartTypeDto, StatisticsService } from '../services';
import { BaseChartDirective } from 'ng2-charts';
import { CommonModule } from '@angular/common';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { FormsModule } from '@angular/forms';

// Register the scales, controllers and plugins globally
Chart.register(CategoryScale, LinearScale, BarController, BarElement, Tooltip, Legend, ChartDataLabels);

// interface ChartRecord {
//   labelName: string;
//   totalOccurrences: number;
//   chartTypeId: number;
//   chartName: string;
// }


@Component({
  standalone: true,
  imports: [CommonModule, BaseChartDirective, FormsModule],  // all required imports here
  selector: 'app-reusable-bar-chart',
  templateUrl: './reusable-bar-chart.component.html',
  styleUrls: ['./reusable-bar-chart.component.scss']
})
export class ReusableBarChartComponent implements OnInit {

  @Input() filterChartTypeId?: number;  // Optional filter by chartTypeId
  @ViewChild(BaseChartDirective) chart?: BaseChartDirective;
  chartTypes: ChartTypeDto[] = [];
  chartData: ChartRecord[] = [];
  selectedChartTypeId?: number;

  public barChartOptions: ChartOptions = {
    indexAxis: 'y',
    responsive: true,
    layout: {
      padding: {
        right: 60
      }
    },
    plugins: {
      legend: { display: false },
      tooltip: { enabled: true },
      datalabels: {
        anchor: 'end',
        align: 'right', // <- this is key
        offset: 30,
        clamp: false,
        clip: false,
        color: '#444',
        font: {
          weight: 'bold',
          size: 12
        },
        formatter: (value) => value
      }
    },
    scales: {
      x: {
        beginAtZero: true,
        ticks: {
          precision: 0
        }
      },
      y: {
        ticks: {
          mirror: false,
          padding: 10
        }
      }
    }
  };


  public barChartPlugins = [ChartDataLabels];

  public barChartData: ChartData<'bar', number[], string> = {
    labels: [],
    datasets: []
  };

  public barChartLabels: string[] = [];
  // public barChartData: ChartDataset[] = [{ data: [], label: 'Occurrences' }];
  public barChartType: ChartType = 'bar';

  loading = true;
  error = '';

  constructor(private statisticsService: StatisticsService) { }

  ngOnInit() {
    this.fetchTypes();
    this.fetchData();
  }
  fetchTypes() {
    this.loading = true;
    this.statisticsService.getChartTypes().subscribe({
      next: (response) => {
        this.chartTypes = response.data ?? [];
        this.selectedChartTypeId = this.chartTypes[0]?.typeId; // Default to first
        // this.fetchData(); // Fetch based on default
        this.loading = false;
      },
      error: (err) => {
        this.error = 'Failed to load chart data.';
        console.error('Error querying chart:', err);
        this.loading = false;
      }
    });
  }
  onChartTypeChange() {
    this.applyChart();
  }
  fetchData() {
    this.loading = true;
    this.error = '';

    this.statisticsService.getAllChart().subscribe({
      next: (response) => {
        this.chartData = response.data.chartRecords ?? [];

        this.selectedChartTypeId = this.chartTypes[0]?.typeId; // Default to first
        this.applyChart();
        // Apply filter if provided


        this.loading = false;
      },
      error: (err) => {
        this.error = 'Failed to load chart data.';
        console.error('Error querying chart:', err);
        this.loading = false;
      }
    });
  }

  applyChart() {
    let records = this.chartData.filter(r => r.chartTypeId === this.selectedChartTypeId);

    const labels = records.map(r => r.labelName ?? 'Unknown');
    const data = records.map(r => r.totalOccurrences);
    const backgroundColors = data.map(() => this.getRandomColor());

    this.barChartData = {
      labels: records.map(r => r.labelName ?? 'Unknown'),
      datasets: [
        {
          data: records.map(r => r.totalOccurrences),
          label: 'Occurrences',
          backgroundColor: backgroundColors
        }
      ]
    };
    setTimeout(() => this.chart?.update(), 0);
  }

  private getRandomColor(): string {
    return '#' + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0');
  }
}
