import { Component, OnInit } from '@angular/core';
import { RegularSearchBaseResult } from '../regular-search-base-result';
import { SearchEvents } from '../../../../shared/events/search.events';
import { Observable, filter } from 'rxjs';

@Component({
  selector: 'app-regular-search-sources-result',
  templateUrl: './regular-search-sources-result.component.html',
  styleUrls: ['./regular-search-sources-result.component.css']
})
export class RegularSearchSourcesResultComponent extends RegularSearchBaseResult<IRegularSearchSourcesResult[]> implements OnInit {


  protected override getDataObservable(): Observable<IRegularSearchSourcesResult[]> {
    return this._httpClient.get<IRegularSearchSourcesResult[]>(this._basePath + "/document/sources/" + this.name)
  }
}

interface IRegularSearchSourcesResult {
  name: string;
  description: string;
  url: string;
  entity: string;
  mainSystem: string;
}
