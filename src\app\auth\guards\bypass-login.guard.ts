import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import {AppConfigService} from "../../shared/app-config.service";

@Injectable({ providedIn: 'root' })
export class BypassLoginGuard implements CanActivate {
  constructor(private appConfigService: AppConfigService, private router: Router) {}

  canActivate(): boolean {
    if (this.appConfigService.appConfig.useByPass) {
      return true;
    } else {
      this.router.navigate(['/']);
      return false;
    }
  }
}
