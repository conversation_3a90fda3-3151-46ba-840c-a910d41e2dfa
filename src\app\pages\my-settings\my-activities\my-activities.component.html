<ul class="nav nav-tabs mb-3">
  <li class="nav-item" *ngFor="let section of sectionNames">
    <a class="nav-link" [class.active]="selectedSection === section" (click)="selectedSection = section"
      href="javascript:void(0)">
      {{ section }}
    </a>
  </li>
</ul>


<div *ngIf="selectedSection === Section.MyChats">

  <h3 class="pb-2 text-primary">محادثاتي</h3>

  <!-- <p-dialog header="تفاصيل المحتوى" [(visible)]="dialogVisible" [modal]="true" [style]="{width: '50vw'}">
    <app-case-details-viewer [selectedCase]="selectedResult" *ngIf="isCaseDetailsVisible" />
  </p-dialog> -->
  <div *ngIf="chatHistory.length === 0" class="text-center text-muted">
    <p>لا توجد محادثه</p>
  </div>
  <div class="row" dir="rtl">
    <div class="col-12 mb-2" *ngFor="let entry of chatHistory">
      <div class="card p-3 d-flex flex-row-reverse justify-content-between align-items-center">

        <!-- Button on the right side in RTL -->
        <div class="d-flex align-items-center gap-2 mb-3">
          <button class="btn btn-primary  py-2 px-3  text-white" (click)="showDialog(entry)">{{ 'عرض'}}</button>

          <img src="assets/svg/trash.svg" alt="حذف" width="40" height="40" class="cursor-pointer"
            (click)="confirmingEntry = entry; displayDeleteConfirm = true">
        </div>

        <!-- Info on the left side in RTL -->
        <div class="w-100 ms-3 text-start">
          <div><strong>رقم الحالة:</strong> {{ entry.CaseId || 'غير متوفر' }}</div>
          <!-- <div><strong>المحتوى:</strong> {{ (entry.content ?? '').slice(0, stringLengthShown) }}{{ (entry.content ?? '').length > stringLengthShown ?
            '...' : '' }}</div> -->

          <!-- <div><strong>المحتوى:</strong>
            <div class="summary-text">
              {{ entry.content || 'غير متوفر' }}
            </div>
          </div> -->

          <div class="assistant-bubble">
            <div class="markdown-preview markdown-body" [innerHTML]="(entry.content ?? '') | markdown">
            </div>
          </div>

          <div class="text-muted"><strong>التاريخ:</strong> {{ entry.creationDate | date: 'MM/dd/yyyy' }}</div>
        </div>

      </div>
    </div>
  </div>
</div>

<div *ngIf="selectedSection === Section.MyComments">

  <h3 class="pb-2 text-primary">تعليقاتي</h3>

  <div class="row" dir="rtl">

    <div *ngIf="commentHistory.length === 0" class="text-center text-muted">
      <p>لا توجد ملاحظه</p>
    </div>

    <div class="col-12 mb-2" *ngFor="let entry of commentHistory">
      <div class="card p-3 d-flex flex-row-reverse justify-content-between align-items-center">

        <!-- Button on the right side in RTL -->
        <!-- <div class="d-flex align-items-center gap-2 mb-3">
          <button class="btn btn-primary  py-2 px-3  text-white" (click)="showDialog(entry)">عرض</button>
        </div> -->

        <div class="d-flex align-items-center gap-2 mb-3">
          <button class="btn btn-primary  py-2 px-3  text-white" (click)="showDialog(entry)">{{ 'عرض'}}</button>

          <img src="assets/svg/trash.svg" alt="حذف" width="40" height="40" class="cursor-pointer"
            (click)="confirmingEntry = entry; displayDeleteConfirm = true">
        </div>

        <!-- Info on the left side in RTL -->
        <div class="w-100 ms-3 text-start">
          <div><strong>رقم الحالة:</strong> {{ entry.CaseId || 'غير متوفر' }}</div>
          <!-- <div><strong>المحتوى:</strong> {{ (entry.content ?? '').slice(0, stringLengthShown) }}{{ (entry.content ?? '').length > stringLengthShown ?
            '...' : '' }}</div> -->

          <div><strong>المحتوى:</strong>
            <div class="summary-text">
              {{ entry.content || 'غير متوفر' }}
            </div>
          </div>

          <div *ngIf="entry.TypeId === favoriteType.Case"><strong>النوع:</strong>
            <div class="summary-text">
              قضية
            </div>
          </div>


          <div *ngIf="entry.TypeId === favoriteType.Regulation"><strong>النوع:</strong>
            <div class="summary-text">
              نظام
            </div>
          </div>

          <div class="text-muted"><strong>التاريخ:</strong> {{ entry.creationDate | date: 'MM/dd/yyyy' }}</div>
        </div>

      </div>
    </div>
  </div>
</div>

<div *ngIf="selectedSection === Section.Highlights">

  <h3 class="pb-2 text-primary">تمييزاتي</h3>

  <div class="row" dir="rtl">

    <div *ngIf="highlightHistory.length === 0" class="text-center text-muted">
      <p>لا توجد ملاحظه</p>
    </div>

    <div class="col-12 mb-2" *ngFor="let entry of highlightHistory">
      <div class="card p-3 d-flex flex-row-reverse justify-content-between align-items-center">

        <!-- Button on the right side in RTL -->
        <!-- <div class="d-flex align-items-center gap-2 mb-3">
          <button class="btn btn-primary  py-2 px-3  text-white" (click)="showDialog(entry)">عرض</button>
        </div> -->

        <div class="d-flex align-items-center gap-2 mb-3">
          <button class="btn btn-primary  py-2 px-3  text-white" (click)="showDialog(entry)">{{ 'عرض'}}</button>

          <img src="assets/svg/trash.svg" alt="حذف" width="40" height="40" class="cursor-pointer"
            (click)="confirmingEntry = entry; displayDeleteConfirm = true">
        </div>

        <!-- Info on the left side in RTL -->
        <div class="w-100 ms-3 text-start">
          <div><strong>رقم الحالة:</strong> {{ entry.CaseId || 'غير متوفر' }}</div>
          <!-- <div><strong>المحتوى:</strong> {{ (entry.content ?? '').slice(0, stringLengthShown) }}{{ (entry.content ?? '').length > stringLengthShown ?
            '...' : '' }}</div> -->

          <div><strong>المحتوى:</strong>
            <div class="summary-text">
              {{ entry.content || 'غير متوفر' }}
            </div>
          </div>

          <div *ngIf="entry.TypeId === favoriteType.Case"><strong>النوع:</strong>
            <div class="summary-text">
              قضية
            </div>
          </div>


          <div *ngIf="entry.TypeId === favoriteType.Regulation"><strong>النوع:</strong>
            <div class="summary-text">
              نظام
            </div>
          </div>

          <div class="text-muted"><strong>التاريخ:</strong> {{ entry.creationDate | date: 'MM/dd/yyyy' }}</div>
        </div>

      </div>
    </div>
  </div>
</div>


<div *ngIf="selectedSection === Section.All">

  <h3 class="pb-2 text-primary">الكل</h3>

  <div class="row" dir="rtl">
    <div class="col-12 mb-2" *ngFor="let entry of allHistory">
      <div class="card p-3 d-flex flex-row-reverse justify-content-between align-items-center">

        <div class="d-flex align-items-center gap-2 mb-3">
          <button class="btn btn-primary  py-2 px-3  text-white" (click)="showDialog(entry)">{{ 'عرض'}}</button>

          <img src="assets/svg/trash.svg" alt="حذف" width="40" height="40" class="cursor-pointer"
            (click)="confirmingEntry = entry; displayDeleteConfirm = true">
        </div>

        <div class="w-100 ms-3 text-start">
          <div><strong>رقم الحالة:</strong> {{ entry.CaseId || 'غير متوفر' }}</div>
          <div><strong>نوع:</strong>
            {{ entry.Source === 'comment' ? 'ملاحظة' : entry.Source === 'chat' ? 'محادثة' : 'غير متوفر' }}
          </div>
          <!-- <div><strong>المحتوى:</strong>
            <div class="summary-text">
              {{ entry.content || 'غير متوفر' }}
            </div>
          </div> -->

          <div class="assistant-bubble">
            <div class="markdown-preview markdown-body" [innerHTML]="(entry.content ?? '') | markdown">
            </div>
          </div>
          <div class="text-muted"><strong>التاريخ:</strong> {{ entry.creationDate | date: 'MM/dd/yyyy' }}</div>
        </div>

      </div>
    </div>
  </div>
</div>
<p-dialog header="تأكيد الحذف" [(visible)]="displayDeleteConfirm" [modal]="true" [closable]="true"
  [dismissableMask]="true" [style]="{ width: '350px' }">
  <p>هل أنت متأكد أنك تريد ازالة هذا التعليق؟</p>
  <div class="d-flex justify-content-end gap-2 mt-3">
    <button type="button" class="btn btn-warning text-primary fw-bold d-flex"
      (click)="displayDeleteConfirm = false">إلغاء</button>
    <button type="button" class="btn btn-danger text-primary fw-bold d-flex"
      (click)="deleteItem(confirmingEntry); displayDeleteConfirm = false">
      حذف
    </button>
  </div>
</p-dialog>
