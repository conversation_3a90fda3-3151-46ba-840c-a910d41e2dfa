import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { BaseForm, CustomFormControl, EMAIL } from '../../../shared/public.api';
import { Validators } from '@angular/forms';
import { UserManagementBase } from '../user-management-base.component';

@Component({
  selector: 'app-create-user',
  templateUrl: './create-user.component.html',
  styleUrls: ['./create-user.component.css']
})
export class CreateUserComponent extends UserManagementBase implements OnInit {

  protected controls = new Control();

  constructor() {
    super('createUser')
  }


  protected showForm: boolean = false;
  @Output('onSave') onSave = new EventEmitter<boolean>(false);


  ngOnInit() {
  }

  show() {
    this.showForm = true;
  }

  protected submit() {

    this.controls.name.setValue(this.controls.name?.value?.trim());

    if (this.controls.isInvalid) {
      return;
    }

    const isActive = this.controls.isActive.value;

    this._userManagementService.create({
      email: this.controls.email.value.trim(),
      name: this.controls.name.value,
      isActive: isActive == null ? false : isActive
    }).subscribe({
      next: (response) => {
        if (response.isSuccess) {
          this.showForm = false;
          this.controls.form.reset();
          this._messageService.add({
            severity: 'success',
            summary: 'نجحت العملية',
            detail: 'تم إنشاء مستخدم جديد بنجاح'
          });
          this.onSave.emit(true);
        }
      }
    });
  }
}


class Control extends BaseForm {
  public readonly name = new CustomFormControl('name', [Validators.required, Validators.minLength(2), Validators.maxLength(50), Validators.pattern(/^[a-zA-Z\u0621-\u064A\s]*$/)]).setForm(this.form);
  public readonly email = new CustomFormControl('email', [Validators.required, Validators.pattern(EMAIL)]).setForm(this.form);
  public readonly isActive = new CustomFormControl('isActive', [], false).setForm(this.form);
}
