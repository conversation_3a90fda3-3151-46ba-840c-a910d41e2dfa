import { BehaviorSubject } from "rxjs";
import { SearchTypeEnum } from "../models/search-type.enum";
import { SystemContinuesEvent, SystemEvent } from "./event";

export namespace ExtractInfoEvents {

    /**
     * @description this event is fired when the user upload the file to the server 
     * and the server returns the contest of the file
     */
    export const InfoExtracted = new SystemContinuesEvent<string>('InfoExtracted');


  


}