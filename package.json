{"name": "judicial-assistant", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "generate-services": "npx @openapitools/openapi-generator-cli generate -i http://localhost:64755/swagger/v1/swagger.json -g typescript-angular -o src/app/services --skip-validate-spec -c src/openapi-config.json"}, "private": true, "dependencies": {"@angular/animations": "^17.1.0", "@angular/cdk": "^17.1.5", "@angular/common": "^17.1.0", "@angular/compiler": "^17.1.0", "@angular/core": "^17.1.0", "@angular/forms": "^17.1.0", "@angular/platform-browser": "^17.1.0", "@angular/platform-browser-dynamic": "^17.1.0", "@angular/router": "^17.1.0", "@auth0/angular-jwt": "^5.2.0", "@ngx-translate/core": "^15.0.0", "@popperjs/core": "^2.11.8", "angular-auth-oidc-client": "^18.0.2", "angular-svg-icon": "^16.1.0", "animate.css": "^4.1.1", "bootstrap": "^5.3.2", "chartjs-plugin-datalabels": "^2.2.0", "font-awesome": "^4.7.0", "marked": "^15.0.11", "ng-block-ui": "^4.0.1", "ng2-charts": "^6.0.0", "ngx-translate-multi-http-loader": "^17.0.0", "primeicons": "^6.0.1", "primeng": "^17.7.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^17.1.3", "@angular/cli": "^17.1.3", "@angular/compiler-cli": "^17.1.0", "@types/jasmine": "~5.1.0", "@types/node": "^20.12.7", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.3.2"}}