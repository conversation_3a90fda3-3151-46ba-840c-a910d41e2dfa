import { CommonModule } from "@angular/common";
import { Component, OnInit, inject } from "@angular/core";
//import { AuthenticationService } from "../../shared/components/authentication/authentication.service";
import { TranslateModule } from "@ngx-translate/core";
import { RouterLink } from "@angular/router";
import { AuthService } from "../../auth/auth.service";
import {UserProfile} from "../../auth/models/user-profile.interface";
import {SvgIconComponent} from "angular-svg-icon";
import {OverlayPanelModule} from "primeng/overlaypanel";
import {AppConfigService} from "../../shared/app-config.service";
@Component({
  selector: "app-landing-page",
  standalone: true,
  imports: [CommonModule, TranslateModule, RouterLink, SvgIconComponent, OverlayPanelModule],
  templateUrl: "./landing-page.component.html",
  styleUrls: ["./landing-page.component.scss"],
})
export class LandingPageComponent implements OnInit {
  protected isAuthenticated: boolean = false;
  private readonly authService = inject(AuthService);
  private readonly appConfigService = inject(AppConfigService);
  protected userInformation: UserProfile | null = null;

  ngOnInit(): void {
    this.authService.checkIsAuthenticated.subscribe(isAuthenticated => this.isAuthenticated = isAuthenticated);

    this.authService.userProfileObservable.subscribe(user => {
      this.userInformation = user;
    });
  }

  async login() {
    try {
      const useByPass = this.appConfigService.appConfig.useByPass;
      console.log(this.appConfigService.appConfig);

      if (useByPass) {
        console.log("Bypass flow enabled. Redirecting to bypass page...");
        window.location.href = "/bypass-login";
      } else {
        console.log("Using SSO login...");
        this.authService.login();
      }
    } catch (error) {
      console.error("Error during login flow:", error);
    }
  }

  logout() {
    this.authService.logout();
  }
}
