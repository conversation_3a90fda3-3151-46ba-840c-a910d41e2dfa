<main class="pt-1">
  <div class="container-xxl">
    <h3 class="text-primary fw-bold">البحث العادي</h3>
    <nav class="nav list-group nav-pills flex-column fw-bold">
      <ol class="breadcrumb pt-1 pb-3">
        <li class="breadcrumb-item"><a href="#">الرئيسية</a></li>
        <!-- <li class="breadcrumb-item active" aria-current="page">نتائج البحث</li> -->
      </ol>
    </nav>

    <div class="card">

      <div class="card-body">

        <div class="d-md-flex justify-content-between align-content-center">

          <div class="d-md-flex justify-content-start align-items-center">
            <input type="text" id="search" name="search" [(ngModel)]="searchText" (keydown)="resetSearch()"
                   (keyup)="resetSearch()" (change)="resetSearch()" class="form-control w-100 me-3"
                   placeholder="البحث..."/>

            <div class="form-check form-check-inline mx-2" *ngFor="let item of allCategories">
              <input class="form-check-input" type="checkbox" (change)="isSearched = false" [(ngModel)]="item.selected"
                     id="inlineCheckbox_{{item.id}}">
              <label class="form-check-label text-nowrap" for="inlineCheckbox_{{item.id}}">
                {{ item.label }}
              </label>
            </div>
          </div>

          <div class="">
            <button [disabled]="!canSearch()" class="btn border pt-0" (click)="search()" type="submit">

              <span class="mx-1">
                <svg-icon src="assets/svg/search-dark.svg"/>
                بحث
              </span>
            </button>
          </div>
        </div>

      </div>

    </div>


    <div class="row" *ngIf="isSearched">
      <div class="pt-4">
        <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
          <li class="nav-item" role="presentation" *ngFor="let item of selectedCategory">
            <button class="nav-link {{item.opened? 'active':''}}" id="pills-{{item.id}}-tab" data-bs-toggle="pill"
                    [attr.data-bs-target]="'#pills-'+item.id" type="button" role="tab"
                    [attr.aria-controls]="'pills-'+item.id"
                    [attr.aria-selected]="item.opened" (click)="item.opened = true">

              {{ item.label }}
            </button>
          </li>
        </ul>
      </div>
      <div class="tab-content" id="pills-tabContent">


        <div class="tab-pane fade" id="pills-regulations" role="tabpanel" aria-labelledby="pills-regulations-tab">
          <!--          <app-regular-search-regulations-result [searchText]="searchText" *ngIf="categories.regulations.opened" />-->
          <app-search-question-result-list></app-search-question-result-list>
        </div>


        <div class="tab-pane fade" id="pills-circulars" role="tabpanel" aria-labelledby="pills-circulars-tab">
          <!--          <app-regular-search-circulars-result [searchText]="searchText" *ngIf="categories.circulars.opened" />-->
          <app-search-question-result-list></app-search-question-result-list>
        </div>

        <div class="tab-pane fade" id="pills-judicial_precedents" role="tabpanel"
             aria-labelledby="pills-judicial_precedents-tab">
          <!--          <app-regular-search-judicial-precedents-result [searchText]="searchText" *ngIf="categories.judicial_precedents.opened" />-->
          <app-search-question-result-list></app-search-question-result-list>
        </div>

        <div class="tab-pane fade" id="pills-sources" role="tabpanel" aria-labelledby="pills-sources-tab">
          <!--          <app-regular-search-sources-result [searchText]="searchText" *ngIf="categories.sources.opened" />-->
          <app-search-question-result-list></app-search-question-result-list>
        </div>

        <div class="tab-pane fade" id="pills-forms" role="tabpanel" aria-labelledby="pills-forms-tab">
          <!--          <app-regular-search-forms-result [searchText]="searchText" *ngIf="categories.forms.opened" />-->
          <app-search-question-result-list></app-search-question-result-list>
        </div>
      </div>

    </div>
  </div>
</main>
