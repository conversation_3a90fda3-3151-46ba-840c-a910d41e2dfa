<div class="search-container">
  <!-- Search Results Side Panel -->
  <div class="search-results-panel" [ngClass]="{ 'show-panel': showSearchPanel }">
    <div class="search-results-count" *ngIf="searchResults?.length">
      تم العثور على {{ searchResults.length }} نتائج مرتبطة بسؤالك. يمكنك
      التمرير للاطلاع عليها، أو النقر على أي منها لعرض التفاصيل.
    </div>

    <div class="search-results-list">
      <app-search-question-result-list *ngIf="isSearchResultVisible" (goBackToSearch)="hideSearchResults()"
        (caseSelected)="onCaseSelected($event)" (lawSelected)="onLawSelected($event)"/>
    </div>
  </div>

  <div class="main-content" [ngClass]="{ 'panel-open': showSearchPanel }">
    <app-case-details-viewer [selectedCase]="selectedCaseDetails" *ngIf="selectedCaseDetails" />
    <app-law-details-viewer [selectedLaw]="selectedLawDetails" *ngIf="selectedLawDetails" />

  </div>

  <!-- Main Content -->
  <div class="main-content" [ngClass]="{ 'panel-open': showQuestionPanel }">
    <div class="search-form-container">
      <div class="card">
        <div class="card-header">
          <h4 class="fw-bold text-center">أدخل السؤال هنا للبحث</h4>
        </div>
        <div class="card-body">
          <form (ngSubmit)="submitSearch()" #keywordsForm="ngForm">
            <div class="search-type-selector mb-3">
              <div class="form-check form-check-inline">
                <input type="radio" id="searchByLaw" name="searchType" class="form-check-input" [(ngModel)]="searchType"
                  value="law" required />
                <label for="searchByLaw" class="form-check-label fw-bold">بحث بالنظام</label>
              </div>
              <div class="form-check form-check-inline">
                <input type="radio" id="searchByCase" name="searchType" class="form-check-input"
                  [(ngModel)]="searchType" value="case" required />
                <label for="searchByCase" class="form-check-label fw-bold">بحث بالقضية</label>
              </div>
            </div>

            <div class="form-group">
              <textarea id="keywords" class="form-control custom-textarea" rows="8" [(ngModel)]="question"
                name="keywords" maxlength="10000" (input)="onInput($event)" required placeholder="أدخل السؤال للبحث"
                style="resize: none"></textarea>
              <div class="word-count text-muted text-end mt-1">
                {{ keywordCount }}/4500
              </div>
            </div>

            <div class="d-flex justify-content-center mt-4">
              <button type="submit" class="btn btn-primary search-btn" [disabled]="!isQuestionValid">
                <i class="pi pi-search me-2"></i>
                بحث
              </button>
            </div>
          </form>

          <div class="search-instructions mt-4">
            <p class="mb-1">
              <i class="pi pi-info-circle me-2"></i>أدخل السؤال الذي تريد البحث
              عنه
            </p>
            <p class="mb-0">
              <i class="pi pi-exclamation-circle me-2"></i>أقصى عدد للكلمات:
              4500 كلمة
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Toggle button outside the panel so it stays visible -->
  <button class="panel-toggle-btn" (click)="toggleSearchPanel()" *ngIf="isSearchResultVisible">
    <i class="pi" [ngClass]="{
        'pi-chevron-right': showSearchPanel,
        'pi-chevron-left': !showSearchPanel
      }"></i>
  </button>
</div>
