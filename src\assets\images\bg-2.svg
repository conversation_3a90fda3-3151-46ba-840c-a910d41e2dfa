<svg width="1104" height="969" viewBox="0 0 1104 969" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.5">
<g filter="url(#filter0_d_231_6895)">
<circle cx="551.5" cy="514.5" r="386.5" stroke="#365A4E" stroke-opacity="0.1" stroke-width="2" shape-rendering="crispEdges"/>
</g>
<circle opacity="0.13" cx="552" cy="514" r="470.25" stroke="#365A4E" stroke-width="1.5"/>
<circle opacity="0.1" cx="552" cy="514" r="551" stroke="#365A4E" stroke-width="2"/>
</g>
<defs>
<filter id="filter0_d_231_6895" x="144" y="107" width="815" height="815" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.211765 0 0 0 0 0.352941 0 0 0 0 0.305882 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_231_6895"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_231_6895" result="shape"/>
</filter>
</defs>
</svg>
