{"auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "register": "Create Account", "username": "Username", "password": "Password", "confirmPassword": "Confirm Password"}, "landing": {"extractLegalInfo": "Extract Legal Information", "extractLegalInfo_desc": "الهدف يكمن في تبسيط استرجاع وتحليل النصوص بشكل أوسع واستخدامها في عدة مهام مثل البحث الدلالي في القضايا والسوابق القضائية وكذلك في القوانين والتعاميم واللوائح", "extractLegalTerms": "Extract Legal Terms", "extractLegalTerms_desc": "الهدف يكمن في تبسيط استرجاع وتحليل النصوص بشكل أوسع واستخدامها في عدة مهام مثل البحث الدلالي في القضايا والسوابق القضائية وكذلك في القوانين والتعاميم واللوائح", "catagoriesDocs": "Catagories Long and Short Documents", "catagoriesDocs_desc": "الهدف يكمن في تبسيط استرجاع وتحليل النصوص بشكل أوسع واستخدامها في عدة مهام مثل البحث الدلالي في القضايا والسوابق القضائية وكذلك في القوانين والتعاميم واللوائح", "semanticSearch": "Semantic Search", "semanticSearch_desc": "الهدف يكمن في تبسيط استرجاع وتحليل النصوص بشكل أوسع واستخدامها في عدة مهام مثل البحث الدلالي في القضايا والسوابق القضائية وكذلك في القوانين والتعاميم واللوائح", "subscribeInFreeTrail": "Subscribe in Free Trial"}, "shared": {"extract-info": "Extract Information", "email": "Email", "name": "Name", "age": "Age", "dob": "Date of Birth", "gender": "Gender", "job": "Occupation", "male": "Male", "female": "Female", "martial_status": "Martial Status", "lookup": "name", "msg": {"required": "حقل {{$}} مطلوب", "invalidField": "صيغة {{$}} غير صحيحة", "SaudiAndIqamaIdErrorMsg": "رقم الهوية يجب أن لايتجاوز 10 أرقام ويجب أن يبدأ بالرقم 1 او 2", "phoneNumberErrorMsg": "رقم الجوال يجب أن يكون 9 أرقام ويجب أن يبدأ بالرقم 5", "noResult": "لا توجد بيانات", "allowedFiles": "The allowed file format is PDF or JPG", "exceedFileSize": "Maximum file size 2MB", "dateMaxErrorMsg": "{{field}} يجب أن يكون اصغر من {{$}}"}}}